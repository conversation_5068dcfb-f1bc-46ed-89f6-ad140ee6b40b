# 情感分析完整版使用说明

## 📋 概述

`情感分析完整版.ipynb` 是一个完整的研报情感分析系统，集成了多种情感分析方法，可以对PDF研报进行全面的情感分析和可视化展示。

## ✨ 主要功能

### 1. PDF文本提取
- 支持PyMuPDF和pdfplumber两种提取方法
- 自动选择最佳提取结果
- 同时提取文本和表格数据

### 2. 文本预处理
- 智能文本清洗（去除URL、HTML标签等）
- 中文分词（使用jieba）
- 停用词过滤
- 词语标准化

### 3. 关键词提取
- **TextRank4zh方法**（如果可用）
- **jieba TextRank方法**
- **TF-IDF方法**
- 多方法融合，提高准确性

### 4. 多种情感分析方法
- **词典法**：基于金融情感词典
- **SnowNLP法**：机器学习情感分析
- **规则法**：基于规则的情感判断
- 多方法对比分析

### 5. 可视化展示
- 情感分析方法对比图
- 关键词情感分布图
- 词云图
- 详细分析报告

## 🚀 快速开始

### 1. 环境要求
```bash
pip install pandas numpy jieba matplotlib seaborn wordcloud pdfplumber scikit-learn
```

可选依赖：
```bash
pip install PyMuPDF textrank4zh snownlp
```

### 2. 文件准备
确保以下文件存在：
- `data/你的PDF文件.pdf`
- `data/stopwords.txt`（停用词文件）
- `data/正面词典.csv`（正面情感词典）
- `data/负面词典.csv`（负面情感词典）

### 3. 运行步骤
1. 打开Jupyter Notebook
2. 运行 `情感分析完整版.ipynb`
3. 按顺序执行所有单元格
4. 查看分析结果和可视化图表

## 📊 输出结果

### 1. 控制台输出
- 各步骤执行状态
- 情感分析得分对比
- 关键词列表
- 匹配的情感词

### 2. 可视化图表
- **方法对比图**：显示三种方法的情感得分
- **关键词情感图**：显示关键词的情感倾向
- **词云图**：直观展示重要关键词

### 3. 详细报告
- 文本统计信息
- 前10个关键词及权重
- 各方法的情感得分
- 匹配的情感词列表

## 🔧 自定义配置

### 修改文件路径
在第8节"执行分析和可视化"中修改：
```python
PDF_PATH = 'data/你的PDF文件.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'
```

### 调整参数
- `num_keywords`：提取的关键词数量
- `top_n`：可视化显示的关键词数量
- 情感阈值：正面(>0.1)、负面(<-0.1)、中性(-0.1~0.1)

## 🎯 特色优势

### 1. 多方法融合
- 不依赖单一方法，提高分析可靠性
- 自动处理依赖缺失，确保系统稳定运行
- 实时对比不同方法的结果

### 2. 容错性强
- 自动检测可用的库和方法
- 提供备选方案，避免因依赖问题导致失败
- 详细的错误提示和处理

### 3. 可视化丰富
- 多种图表类型
- 清晰的颜色编码（绿色=正面，红色=负面，灰色=中性）
- 交互式展示

### 4. 易于扩展
- 模块化设计
- 清晰的函数接口
- 便于添加新的分析方法

## 🔍 测试验证

运行测试脚本验证系统功能：
```bash
python test_sentiment_analysis.py
```

测试内容包括：
- 文本处理功能
- 情感分析功能
- 关键词提取功能
- 可视化功能

## 📝 注意事项

1. **字体问题**：如果图表中文显示异常，系统会自动使用英文标签
2. **依赖缺失**：系统会自动检测并使用可用的方法
3. **文件路径**：确保所有文件路径正确
4. **内存使用**：大型PDF文件可能需要较多内存

## 🆘 常见问题

### Q: 图表中文显示为方框？
A: 系统已配置使用系统默认字体，如仍有问题，图表会使用英文标签。

### Q: 某些库导入失败？
A: 系统设计了容错机制，会自动使用可用的替代方法。

### Q: PDF提取失败？
A: 检查PDF文件是否损坏，系统支持多种提取方法。

### Q: 情感分析结果不准确？
A: 可以更新情感词典，或调整多方法的权重配置。

## 📈 结果解读

- **得分范围**：-1（极负面）到 +1（极正面）
- **阈值设定**：>0.1为正面，<-0.1为负面，其他为中性
- **多方法对比**：关注一致性，差异较大时需要人工判断
- **关键词分析**：重点关注高权重关键词的情感倾向

---

🎉 **祝您使用愉快！如有问题，请查看代码注释或联系开发者。**
