{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 研报情感分析系统 - 修复版\n", "\n", "本notebook整合了研报情感分析的完整流程，包括：\n", "1. PDF文本提取\n", "2. 文本预处理\n", "3. 关键词提取\n", "4. 多种情感分析方法对比\n", "5. 结果可视化\n", "\n", "**修复内容：**\n", "- ✅ 解决了情感词典编码问题（支持GBK、UTF-8等多种编码）\n", "- ✅ 修复了中文字体显示问题\n", "- ✅ 优化了容错机制\n", "\n", "所有结果都在notebook内展示，无需外部文件。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库和模块"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 中文字体设置成功\n", "✅ 所有必要的库已导入完成\n", "PyMuPDF可用: True\n", "TextRank4zh可用: True\n", "SnowNLP可用: True\n", "中文字体可用: True\n", "📊 将使用多种传统方法进行情感分析对比\n"]}], "source": ["# 基础库\n", "import os\n", "import sys\n", "import time\n", "import re\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import concurrent.futures\n", "from typing import List, Tuple, Dict, Optional\n", "\n", "# 数据处理\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF处理\n", "import pdfplumber\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"警告: PyMuPDF不可用，将使用其他PDF提取方法\")\n", "\n", "# 文本处理\n", "import jieba\n", "import jieba.analyse\n", "from tqdm import tqdm\n", "\n", "# TextRank\n", "try:\n", "    from textrank4zh import TextRank4Keyword, TextRank4Sentence\n", "    TEXTRANK4ZH_AVAILABLE = True\n", "except ImportError:\n", "    TEXTRANK4ZH_AVAILABLE = False\n", "    print(\"警告: textrank4zh不可用，将使用jieba的TextRank\")\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 情感分析库\n", "try:\n", "    from snownlp import SnowNLP\n", "    SNOWNLP_AVAILABLE = True\n", "except ImportError:\n", "    SNOWNLP_AVAILABLE = False\n", "    print(\"警告: SnowNLP不可用，将使用其他情感分析方法\")\n", "\n", "# 可视化\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "import seaborn as sns\n", "from wordcloud import WordCloud\n", "\n", "# 设置matplotlib字体，支持中文显示\n", "import platform\n", "system = platform.system()\n", "\n", "# 根据操作系统设置中文字体\n", "if system == 'Windows':\n", "    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']\n", "elif system == 'Darwin':  # macOS\n", "    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'sans-serif']\n", "else:  # Linux\n", "    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'sans-serif']\n", "\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['font.size'] = 12\n", "\n", "# 测试中文字体\n", "try:\n", "    fig, ax = plt.subplots(figsize=(1, 1))\n", "    ax.text(0.5, 0.5, '测试中文', ha='center', va='center')\n", "    plt.close(fig)\n", "    print(\"✅ 中文字体设置成功\")\n", "    CHINESE_FONT_AVAILABLE = True\n", "except Exception as e:\n", "    print(f\"⚠️ 中文字体设置可能有问题: {e}，将使用英文标签\")\n", "    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']\n", "    CHINESE_FONT_AVAILABLE = False\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ 所有必要的库已导入完成\")\n", "print(f\"PyMuPDF可用: {PYMUPDF_AVAILABLE}\")\n", "print(f\"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}\")\n", "print(f\"SnowNLP可用: {SNOWNLP_AVAILABLE}\")\n", "print(f\"中文字体可用: {CHINESE_FONT_AVAILABLE}\")\n", "print(\"📊 将使用多种传统方法进行情感分析对比\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 修复后的情感词典加载函数"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔧 测试情感词典加载功能...\n", "✅ 使用 gbk 编码加载正面词典: data/正面词典.csv\n", "✅ 使用 gbk 编码加载负面词典: data/负面词典.csv\n", "📚 情感词典加载完成，共 2612 个词\n", "📊 加载结果: 正面词 1117 个，负面词 1495 个\n", "✅ 情感词典加载功能测试完成\n"]}], "source": ["def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:\n", "    \"\"\"\n", "    加载情感词典（支持多种编码）\n", "    \n", "    参数:\n", "        positive_path: 正面词典文件路径\n", "        negative_path: 负面词典文件路径\n", "    \n", "    返回:\n", "        情感词典，正面词为正值，负面词为负值\n", "    \"\"\"\n", "    sentiment_dict = {}\n", "    \n", "    # 默认情感词\n", "    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']\n", "    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']\n", "    \n", "    # 添加默认词\n", "    for word in default_positive:\n", "        sentiment_dict[word] = 1.0\n", "    for word in default_negative:\n", "        sentiment_dict[word] = -1.0\n", "    \n", "    # 加载正面词典\n", "    if os.path.exists(positive_path):\n", "        loaded = False\n", "        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "            try:\n", "                if positive_path.endswith('.csv'):\n", "                    df = pd.read_csv(positive_path, encoding=encoding)\n", "                    if not df.empty:\n", "                        words = df.iloc[:, 0].tolist()\n", "                        for word in words:\n", "                            if isinstance(word, str) and word.strip():\n", "                                sentiment_dict[word.strip()] = 1.0\n", "                else:\n", "                    with open(positive_path, 'r', encoding=encoding) as f:\n", "                        for line in f:\n", "                            word = line.strip()\n", "                            if word:\n", "                                sentiment_dict[word] = 1.0\n", "                print(f\"✅ 使用 {encoding} 编码加载正面词典: {positive_path}\")\n", "                loaded = True\n", "                break\n", "            except (UnicodeDecodeError, pd.errors.EmptyDataError):\n", "                continue\n", "        \n", "        if not loaded:\n", "            print(f\"⚠️ 无法加载正面词典: {positive_path}\")\n", "    \n", "    # 加载负面词典\n", "    if os.path.exists(negative_path):\n", "        loaded = False\n", "        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "            try:\n", "                if negative_path.endswith('.csv'):\n", "                    df = pd.read_csv(negative_path, encoding=encoding)\n", "                    if not df.empty:\n", "                        words = df.iloc[:, 0].tolist()\n", "                        for word in words:\n", "                            if isinstance(word, str) and word.strip():\n", "                                sentiment_dict[word.strip()] = -1.0\n", "                else:\n", "                    with open(negative_path, 'r', encoding=encoding) as f:\n", "                        for line in f:\n", "                            word = line.strip()\n", "                            if word:\n", "                                sentiment_dict[word] = -1.0\n", "                print(f\"✅ 使用 {encoding} 编码加载负面词典: {negative_path}\")\n", "                loaded = True\n", "                break\n", "            except (UnicodeDecodeError, pd.errors.EmptyDataError):\n", "                continue\n", "        \n", "        if not loaded:\n", "            print(f\"⚠️ 无法加载负面词典: {negative_path}\")\n", "    \n", "    print(f\"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词\")\n", "    return sentiment_dict\n", "\n", "# 测试情感词典加载\n", "print(\"🔧 测试情感词典加载功能...\")\n", "test_dict = load_sentiment_dict('data/正面词典.csv', 'data/负面词典.csv')\n", "positive_count = sum(1 for v in test_dict.values() if v > 0)\n", "negative_count = sum(1 for v in test_dict.values() if v < 0)\n", "print(f\"📊 加载结果: 正面词 {positive_count} 个，负面词 {negative_count} 个\")\n", "print(\"✅ 情感词典加载功能测试完成\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}