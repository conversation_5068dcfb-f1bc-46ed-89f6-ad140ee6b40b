# 兼容性补丁 - 修复 transformers 依赖冲突
import warnings
warnings.filterwarnings('ignore', category=DeprecationWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

# 修复 cryptography 和 pyOpenSSL 的兼容性问题
try:
    import cryptography
    from cryptography import utils
    
    # 检查是否需要修复 deprecated 函数
    if hasattr(utils, 'deprecated'):
        original_deprecated = utils.deprecated
        
        def patched_deprecated(reason, name=None, **kwargs):
            # 移除不支持的 name 参数，只保留 reason
            return original_deprecated(reason)
        
        utils.deprecated = patched_deprecated
        print("✅ cryptography 兼容性补丁已应用")
    else:
        # 如果没有 deprecated 函数，创建一个简单的
        def deprecated(reason, name=None, **kwargs):
            def decorator(func):
                return func
            return decorator
        utils.deprecated = deprecated
        print("✅ cryptography deprecated 函数已创建")
        
except ImportError:
    print("⚠️ cryptography 未安装，跳过补丁")
except Exception as e:
    print(f"⚠️ cryptography 补丁失败: {e}")

# 修复 OpenSSL 相关问题
try:
    # 预先导入可能有问题的模块
    import OpenSSL
    from OpenSSL import crypto, SSL
    print("✅ OpenSSL 模块导入成功")
except Exception as e:
    print(f"⚠️ OpenSSL 导入失败: {e}")

print("🩹 兼容性补丁加载完成")
