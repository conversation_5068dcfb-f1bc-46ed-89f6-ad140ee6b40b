#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复字体和报错问题
"""

import json
import os
import platform

def fix_notebook_font_and_errors():
    """修复notebook中的字体和报错问题"""
    
    print("开始修复字体和报错问题...")
    
    # 读取原版notebook
    with open('研报情感分析完整版.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # 新的字体设置代码
    new_font_setup = '''# 设置matplotlib和WordCloud字体支持
import platform
system = platform.system()

# 获取系统默认字体路径
def get_system_font_path():
    """获取系统默认中文字体路径"""
    import matplotlib.font_manager as fm
    
    # 优先使用系统默认字体
    system_fonts = []
    
    if system == 'Windows':
        system_fonts = [
            'C:/Windows/Fonts/simhei.ttf',  # 黑体
            'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑
            'C:/Windows/Fonts/simsun.ttc',  # 宋体
            'C:/Windows/Fonts/arial.ttf'    # Arial
        ]
    elif system == 'Darwin':  # macOS
        system_fonts = [
            '/System/Library/Fonts/PingFang.ttc',
            '/System/Library/Fonts/Arial.ttf',
            '/Library/Fonts/Arial.ttf'
        ]
    else:  # Linux
        system_fonts = [
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
            '/usr/share/fonts/TTF/arial.ttf'
        ]
    
    # 检查字体文件是否存在
    for font_path in system_fonts:
        if os.path.exists(font_path):
            return font_path
    
    # 如果没有找到，使用matplotlib的默认字体
    try:
        # 获取系统中可用的字体
        font_list = fm.findSystemFonts()
        if font_list:
            return font_list[0]  # 返回第一个可用字体
    except:
        pass
    
    return None

# 获取系统字体路径
SYSTEM_FONT_PATH = get_system_font_path()
print(f"检测到系统字体: {SYSTEM_FONT_PATH}")

# 设置matplotlib字体（使用系统默认字体）
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 测试字体
try:
    fig, ax = plt.subplots(figsize=(1, 1))
    ax.text(0.5, 0.5, 'Test Font', ha='center', va='center')
    plt.close(fig)
    print("字体设置成功")
    FONT_AVAILABLE = True
except Exception as e:
    print(f"字体设置可能有问题: {e}")
    FONT_AVAILABLE = False'''

    # 新的WordCloud函数
    new_wordcloud_function = '''def create_wordcloud(keywords: List[Tuple[str, float]]) -> None:
    """
    创建关键词云图，使用系统默认字体
    
    参数:
        keywords: 关键词列表
    """
    if not keywords:
        print("没有关键词可用于生成词云")
        return
    
    try:
        # 准备词频字典
        word_freq = {word: weight for word, weight in keywords}
        
        # 创建词云（使用系统默认字体）
        wordcloud = WordCloud(
            width=800, height=400,
            background_color='white',
            max_words=50,
            colormap='viridis',
            font_path=SYSTEM_FONT_PATH,  # 使用系统字体
            prefer_horizontal=0.9,
            min_font_size=10,
            max_font_size=100,
            relative_scaling=0.5,
            random_state=42
        ).generate_from_frequencies(word_freq)
        
        # 显示词云
        plt.figure(figsize=(12, 6))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('Keywords Word Cloud', fontsize=16, fontweight='bold', pad=20)
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"词云生成失败: {e}")
        # 创建简单的条形图作为替代
        try:
            top_words = keywords[:20]
            words = [item[0] for item in top_words]
            weights = [item[1] for item in top_words]
            
            plt.figure(figsize=(12, 8))
            bars = plt.barh(words, weights, color='steelblue', alpha=0.7)
            plt.xlabel('Weight')
            plt.title('Top Keywords (Alternative to Word Cloud)')
            plt.gca().invert_yaxis()  # 最高权重在顶部
            
            # 添加数值标签
            for bar in bars:
                width = bar.get_width()
                plt.text(width + 0.001, bar.get_y() + bar.get_height()/2,
                        f'{width:.3f}', ha='left', va='center')
            
            plt.tight_layout()
            plt.show()
        except Exception as backup_error:
            print(f"备用图表也失败: {backup_error}")'''

    # 修复编码问题的代码
    encoding_fix_code = '''        # 尝试多种编码加载情感词典
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if positive_path.endswith('.csv'):
                    df = pd.read_csv(positive_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = 1.0
                else:
                    with open(positive_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = 1.0
                print(f"成功使用 {encoding} 编码加载正面词典")
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue'''

    # 处理每个cell
    for cell in notebook['cells']:
        if 'source' in cell and isinstance(cell['source'], list):
            source_text = ''.join(cell['source'])
            
            # 1. 修复字体设置
            if '# 设置matplotlib中文字体支持' in source_text:
                # 找到字体设置的开始和结束
                start_idx = -1
                end_idx = -1
                for i, line in enumerate(cell['source']):
                    if '# 设置matplotlib中文字体支持' in line:
                        start_idx = i
                    if start_idx != -1 and 'CHINESE_FONT_AVAILABLE = False' in line:
                        end_idx = i + 1
                        break
                
                if start_idx != -1 and end_idx != -1:
                    # 替换字体设置代码
                    new_lines = [line + '\n' for line in new_font_setup.split('\n')]
                    cell['source'][start_idx:end_idx] = new_lines
                    print("已修复字体设置代码")
            
            # 2. 修复WordCloud函数
            if 'def create_wordcloud(' in source_text and 'font_path=None' in source_text:
                # 替换整个函数
                start_idx = -1
                end_idx = -1
                for i, line in enumerate(cell['source']):
                    if 'def create_wordcloud(' in line:
                        start_idx = i
                    if start_idx != -1 and i > start_idx and line.strip() == '' and i < len(cell['source']) - 1:
                        if not cell['source'][i+1].startswith('    ') and not cell['source'][i+1].startswith('\t'):
                            end_idx = i
                            break
                
                if start_idx != -1:
                    if end_idx == -1:
                        end_idx = len(cell['source'])
                    
                    # 替换函数代码
                    new_lines = [line + '\n' for line in new_wordcloud_function.split('\n')]
                    cell['source'][start_idx:end_idx] = new_lines
                    print("已修复WordCloud函数")
            
            # 3. 修复编码问题
            if 'df = pd.read_csv(positive_path)' in source_text and 'encoding' not in source_text:
                for i, line in enumerate(cell['source']):
                    if 'df = pd.read_csv(positive_path)' in line:
                        # 替换编码相关的代码块
                        new_lines = [line + '\n' for line in encoding_fix_code.split('\n')]
                        # 找到需要替换的范围
                        end_replace = i + 10  # 替换接下来的10行
                        if end_replace > len(cell['source']):
                            end_replace = len(cell['source'])
                        cell['source'][i:end_replace] = new_lines
                        print("已修复编码问题")
                        break
    
    # 保存修复后的文件
    with open('研报情感分析完整版_字体修复版.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)
    
    print("修复完成！")
    print("新文件: 研报情感分析完整版_字体修复版.ipynb")
    print("")
    print("修复内容:")
    print("1. 使用系统默认字体，避免中文显示问题")
    print("2. 修复WordCloud字体设置，解决词云显示圈圈问题")
    print("3. 修复情感词典编码问题")
    print("4. 保留所有原版功能")

if __name__ == "__main__":
    fix_notebook_font_and_errors()
