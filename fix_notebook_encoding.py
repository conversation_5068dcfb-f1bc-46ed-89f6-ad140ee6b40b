#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复notebook中的编码问题
"""

import json
import re

def fix_notebook_encoding():
    """修复notebook中的编码问题"""
    
    # 读取notebook
    with open('情感分析完整版.ipynb', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复正面词典加载部分
    old_positive_pattern = r'(# 加载正面词典\n.*?if os\.path\.exists\(positive_path\):\n.*?try:\n.*?if positive_path\.endswith\(\'\.csv\'\):\n.*?df = pd\.read_csv\(positive_path\))(.*?)(print\(f"✅ 加载正面词典: \{positive_path\}"\)\n.*?except Exception as e:\n.*?print\(f"⚠️ 加载正面词典失败: \{e\}"\))'
    
    new_positive_code = '''# 加载正面词典
    if os.path.exists(positive_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if positive_path.endswith('.csv'):
                    df = pd.read_csv(positive_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = 1.0
                else:
                    with open(positive_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = 1.0
                print(f"✅ 使用 {encoding} 编码加载正面词典: {positive_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载正面词典: {positive_path}")'''
    
    # 替换正面词典加载代码
    content = content.replace(
        '"                df = pd.read_csv(positive_path)\\n",',
        '"                # 尝试多种编码\\n",\n' +
        '    "                for encoding in [\'utf-8\', \'gbk\', \'gb2312\', \'utf-8-sig\']:\\n",\n' +
        '    "                    try:\\n",\n' +
        '    "                        df = pd.read_csv(positive_path, encoding=encoding)\\n",'
    )
    
    # 替换负面词典加载代码
    content = content.replace(
        '"                df = pd.read_csv(negative_path)\\n",',
        '"                # 尝试多种编码\\n",\n' +
        '    "                for encoding in [\'utf-8\', \'gbk\', \'gb2312\', \'utf-8-sig\']:\\n",\n' +
        '    "                    try:\\n",\n' +
        '    "                        df = pd.read_csv(negative_path, encoding=encoding)\\n",'
    )
    
    # 保存修复后的文件
    with open('情感分析完整版.ipynb', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Notebook编码问题修复完成")

def create_complete_fixed_function():
    """创建完整的修复后的情感词典加载函数"""
    
    fixed_code = '''def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:
    """
    加载情感词典（支持多种编码）
    
    参数:
        positive_path: 正面词典文件路径
        negative_path: 负面词典文件路径
    
    返回:
        情感词典，正面词为正值，负面词为负值
    """
    sentiment_dict = {}
    
    # 默认情感词
    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']
    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']
    
    # 添加默认词
    for word in default_positive:
        sentiment_dict[word] = 1.0
    for word in default_negative:
        sentiment_dict[word] = -1.0
    
    # 加载正面词典
    if os.path.exists(positive_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if positive_path.endswith('.csv'):
                    df = pd.read_csv(positive_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = 1.0
                else:
                    with open(positive_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = 1.0
                print(f"✅ 使用 {encoding} 编码加载正面词典: {positive_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载正面词典: {positive_path}")
    
    # 加载负面词典
    if os.path.exists(negative_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if negative_path.endswith('.csv'):
                    df = pd.read_csv(negative_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = -1.0
                else:
                    with open(negative_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = -1.0
                print(f"✅ 使用 {encoding} 编码加载负面词典: {negative_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载负面词典: {negative_path}")
    
    print(f"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词")
    return sentiment_dict'''
    
    return fixed_code

def manual_fix_notebook():
    """手动修复notebook"""
    
    # 读取notebook
    with open('情感分析完整版.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # 找到情感词典加载函数的cell
    for cell in notebook['cells']:
        if cell['cell_type'] == 'code' and 'source' in cell:
            source_lines = cell['source']
            
            # 检查是否是情感分析模块
            if any('def load_sentiment_dict' in line for line in source_lines):
                print("找到情感词典加载函数，开始修复...")
                
                # 创建新的源代码
                new_source = [
                    "def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:\\n",
                    "    \\\"\\\"\\\"\\n",
                    "    加载情感词典（支持多种编码）\\n",
                    "    \\n",
                    "    参数:\\n",
                    "        positive_path: 正面词典文件路径\\n",
                    "        negative_path: 负面词典文件路径\\n",
                    "    \\n",
                    "    返回:\\n",
                    "        情感词典，正面词为正值，负面词为负值\\n",
                    "    \\\"\\\"\\\"\\n",
                    "    sentiment_dict = {}\\n",
                    "    \\n",
                    "    # 默认情感词\\n",
                    "    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']\\n",
                    "    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']\\n",
                    "    \\n",
                    "    # 添加默认词\\n",
                    "    for word in default_positive:\\n",
                    "        sentiment_dict[word] = 1.0\\n",
                    "    for word in default_negative:\\n",
                    "        sentiment_dict[word] = -1.0\\n",
                    "    \\n",
                    "    # 加载正面词典\\n",
                    "    if os.path.exists(positive_path):\\n",
                    "        loaded = False\\n",
                    "        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
                    "            try:\\n",
                    "                if positive_path.endswith('.csv'):\\n",
                    "                    df = pd.read_csv(positive_path, encoding=encoding)\\n",
                    "                    if not df.empty:\\n",
                    "                        words = df.iloc[:, 0].tolist()\\n",
                    "                        for word in words:\\n",
                    "                            if isinstance(word, str) and word.strip():\\n",
                    "                                sentiment_dict[word.strip()] = 1.0\\n",
                    "                else:\\n",
                    "                    with open(positive_path, 'r', encoding=encoding) as f:\\n",
                    "                        for line in f:\\n",
                    "                            word = line.strip()\\n",
                    "                            if word:\\n",
                    "                                sentiment_dict[word] = 1.0\\n",
                    "                print(f\\\"✅ 使用 {encoding} 编码加载正面词典: {positive_path}\\\")\\n",
                    "                loaded = True\\n",
                    "                break\\n",
                    "            except (UnicodeDecodeError, pd.errors.EmptyDataError):\\n",
                    "                continue\\n",
                    "        \\n",
                    "        if not loaded:\\n",
                    "            print(f\\\"⚠️ 无法加载正面词典: {positive_path}\\\")\\n",
                    "    \\n",
                    "    # 加载负面词典\\n",
                    "    if os.path.exists(negative_path):\\n",
                    "        loaded = False\\n",
                    "        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
                    "            try:\\n",
                    "                if negative_path.endswith('.csv'):\\n",
                    "                    df = pd.read_csv(negative_path, encoding=encoding)\\n",
                    "                    if not df.empty:\\n",
                    "                        words = df.iloc[:, 0].tolist()\\n",
                    "                        for word in words:\\n",
                    "                            if isinstance(word, str) and word.strip():\\n",
                    "                                sentiment_dict[word.strip()] = -1.0\\n",
                    "                else:\\n",
                    "                    with open(negative_path, 'r', encoding=encoding) as f:\\n",
                    "                        for line in f:\\n",
                    "                            word = line.strip()\\n",
                    "                            if word:\\n",
                    "                                sentiment_dict[word] = -1.0\\n",
                    "                print(f\\\"✅ 使用 {encoding} 编码加载负面词典: {negative_path}\\\")\\n",
                    "                loaded = True\\n",
                    "                break\\n",
                    "            except (UnicodeDecodeError, pd.errors.EmptyDataError):\\n",
                    "                continue\\n",
                    "        \\n",
                    "        if not loaded:\\n",
                    "            print(f\\\"⚠️ 无法加载负面词典: {negative_path}\\\")\\n",
                    "    \\n",
                    "    print(f\\\"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词\\\")\\n",
                    "    return sentiment_dict\\n",
                    "\\n"
                ]
                
                # 找到函数结束位置，保留其他代码
                start_idx = -1
                end_idx = -1
                for i, line in enumerate(source_lines):
                    if 'def load_sentiment_dict' in line:
                        start_idx = i
                    if start_idx >= 0 and 'return sentiment_dict' in line:
                        end_idx = i + 1
                        break
                
                if start_idx >= 0 and end_idx >= 0:
                    # 保留函数前后的代码
                    before_function = source_lines[:start_idx]
                    after_function = source_lines[end_idx:]
                    
                    # 组合新的源代码
                    cell['source'] = before_function + new_source + after_function
                    print("✅ 情感词典加载函数已修复")
                    break
    
    # 保存修复后的notebook
    with open('情感分析完整版.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)
    
    print("✅ Notebook修复完成")

if __name__ == "__main__":
    print("🔧 开始修复notebook编码问题...")
    manual_fix_notebook()
    print("✅ 修复完成！")
