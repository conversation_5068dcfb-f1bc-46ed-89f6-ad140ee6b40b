@echo off
chcp 65001 >nul
echo.
echo ==========================================
echo    研报情感分析系统 - 一键环境配置
echo ==========================================
echo.

echo 🚀 正在创建新的Python环境...
echo.

REM 检查conda是否可用
conda --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到conda，请先安装Anaconda或Miniconda
    echo 📥 下载地址: https://www.anaconda.com/products/distribution
    pause
    exit /b 1
)

REM 删除已存在的环境（如果有）
echo 🧹 清理旧环境...
conda env remove -n sentiment_analysis -y >nul 2>&1

REM 创建新环境
echo 📦 创建新环境 sentiment_analysis...
conda create -n sentiment_analysis python=3.9 -y
if %errorlevel% neq 0 (
    echo ❌ 环境创建失败
    pause
    exit /b 1
)

REM 激活环境并安装包
echo 🔧 激活环境并安装依赖包...
call conda activate sentiment_analysis

REM 配置镜像源
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

REM 升级pip
python -m pip install --upgrade pip

echo.
echo 📚 安装核心依赖包...
pip install numpy pandas matplotlib seaborn scikit-learn
pip install pdfplumber PyMuPDF
pip install jieba textrank4zh
pip install wordcloud tqdm requests

echo.
echo 🤖 安装深度学习库（兼容版本）...
pip install torch==2.0.1 --index-url https://download.pytorch.org/whl/cpu
pip install transformers==4.30.0 accelerate==0.20.3

echo.
echo 📋 安装Jupyter...
pip install jupyter ipykernel
python -m ipykernel install --user --name sentiment_analysis --display-name "Python (情感分析)"

echo.
echo 🧪 测试安装结果...
python -c "
try:
    import pandas, numpy, jieba, matplotlib.pyplot, wordcloud
    print('✅ 基础库测试通过')
    
    import torch
    from transformers import AutoTokenizer
    print('✅ 深度学习库测试通过')
    
    from textrank4zh import TextRank4Keyword
    print('✅ TextRank4zh测试通过')
    
    print('🎉 所有库安装成功！')
except Exception as e:
    print(f'⚠️ 测试失败: {e}')
"

echo.
echo ==========================================
echo 🎉 安装完成！
echo ==========================================
echo.
echo 📋 使用方法:
echo 1. 打开 Anaconda Prompt
echo 2. 运行: conda activate sentiment_analysis  
echo 3. 运行: jupyter notebook
echo 4. 在Jupyter中选择内核: Python (情感分析)
echo 5. 打开您的notebook文件
echo.
echo 💡 提示: 如果遇到问题，请查看 手动安装指南.md
echo.
pause
