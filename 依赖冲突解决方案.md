# 🔧 依赖冲突解决方案

## 问题诊断

根据您的错误信息，问题是：
```
deprecated() got an unexpected keyword argument 'name'
```

这是由于 `cryptography` 和 `pyOpenSSL` 库版本不兼容导致的，影响了 `transformers` 库的正常导入。

## 🚀 快速解决方案

### 方案1: 使用修复版notebook（推荐）

我已经创建了一个修复版本：`研报情感分析_依赖修复版.ipynb`

这个版本包含了：
- ✅ 兼容性补丁，自动修复依赖冲突
- ✅ 完整的错误处理
- ✅ 详细的状态报告

### 方案2: 手动修复原文件

在原notebook的第一个单元格**最前面**添加以下代码：

```python
# 兼容性补丁 - 修复依赖冲突
import warnings
warnings.filterwarnings('ignore', category=DeprecationWarning)
warnings.filterwarnings('ignore', category=FutureWarning)

# 修复 cryptography 和 pyOpenSSL 的兼容性问题
try:
    import cryptography
    from cryptography import utils
    
    # 检查是否需要修复 deprecated 函数
    if hasattr(utils, 'deprecated'):
        original_deprecated = utils.deprecated
        
        def patched_deprecated(reason, name=None, **kwargs):
            # 移除不支持的 name 参数，只保留 reason
            return original_deprecated(reason)
        
        utils.deprecated = patched_deprecated
        print("✅ cryptography 兼容性补丁已应用")
    else:
        # 如果没有 deprecated 函数，创建一个简单的
        def deprecated(reason, name=None, **kwargs):
            def decorator(func):
                return func
            return decorator
        utils.deprecated = deprecated
        print("✅ cryptography deprecated 函数已创建")
        
except ImportError:
    print("⚠️ cryptography 未安装，跳过补丁")
except Exception as e:
    print(f"⚠️ cryptography 补丁失败: {e}")

print("🩹 兼容性补丁加载完成")
```

### 方案3: 升级依赖包

如果上述方案不工作，请执行：

```bash
pip install --upgrade cryptography pyopenssl transformers torch accelerate
```

然后重启Jupyter内核。

## 🎯 预期效果

修复后，您应该看到：
- ✅ `FinBERT功能已启用（依赖冲突已修复）`
- ✅ `TextRank4zh正常工作`
- ✅ `词云图正确显示中文关键词`

## 📋 使用步骤

1. **选择方案**：推荐使用 `研报情感分析_依赖修复版.ipynb`
2. **打开notebook**：在Jupyter中打开文件
3. **运行单元格**：按顺序运行所有单元格
4. **检查状态**：查看库导入状态报告

## 🔍 故障排除

如果仍有问题：

1. **重启内核**：Kernel → Restart & Clear Output
2. **检查Python版本**：确保使用Python 3.7+
3. **虚拟环境**：考虑使用新的虚拟环境
4. **手动安装**：
   ```bash
   pip uninstall cryptography pyopenssl
   pip install cryptography==41.0.7 pyopenssl==23.3.0
   pip install transformers torch
   ```

## 📊 技术原理

**问题原因**：
- `cryptography` 库更新了 `deprecated` 函数的签名
- 旧版本的 `pyOpenSSL` 仍使用旧的调用方式
- `transformers` 库依赖这些库，导致导入失败

**解决原理**：
- 动态修补 `deprecated` 函数
- 移除不兼容的参数
- 保持向后兼容性

## 🎉 结论

使用任一方案都能解决依赖冲突问题，让您的研报情感分析系统正常运行，包括：
- 🤖 FinBERT深度学习情感分析
- 🔤 TextRank4zh关键词提取
- 🎨 中文词云图显示
- 📊 完整的可视化功能
