# 基础库
import os
import sys
import time
import re
import warnings
from datetime import datetime
from collections import Counter, defaultdict
import concurrent.futures
from typing import List, Tuple, Dict, Optional

# 数据处理
import pandas as pd
import numpy as np

# PDF处理
import pdfplumber
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("警告: PyMuPDF不可用，将使用其他PDF提取方法")

try:
    import camelot
    CAMELOT_AVAILABLE = True
except ImportError:
    CAMELOT_AVAILABLE = False
    print("警告: camelot-py不可用，高级表格提取功能将不可用")

# 文本处理
import jieba
import jieba.analyse
from tqdm import tqdm

# TextRank
try:
    from textrank4zh import TextRank4Keyword, TextRank4Sentence
    TEXTRANK4ZH_AVAILABLE = True
except ImportError:
    TEXTRANK4ZH_AVAILABLE = False
    print("警告: textrank4zh不可用，将使用jieba的TextRank")

# 机器学习
from sklearn.feature_extraction.text import TfidfVectorizer

# 深度学习 - 修复版本，避免依赖冲突
try:
    # 暂时禁用transformers以避免依赖冲突
    # import torch
    # from transformers import AutoTokenizer, AutoModelForSequenceClassification
    TRANSFORMERS_AVAILABLE = False
    print("注意: 为避免依赖冲突，暂时禁用FinBERT功能，使用多种传统方法进行情感分析")
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    print("警告: transformers不可用，FinBERT功能将不可用")

# 可视化
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from wordcloud import WordCloud

# 设置matplotlib中文字体支持
import platform
system = platform.system()

# 根据操作系统设置中文字体
if system == 'Windows':
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
elif system == 'Darwin':  # macOS
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'sans-serif']
else:  # Linux
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'sans-serif']

plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 测试中文字体
try:
    fig, ax = plt.subplots(figsize=(1, 1))
    ax.text(0.5, 0.5, '测试中文', ha='center', va='center')
    plt.close(fig)
    print("中文字体设置成功")
    CHINESE_FONT_AVAILABLE = True
except Exception as e:
    print(f"中文字体设置可能有问题: {e}，将使用英文标签")
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']
    CHINESE_FONT_AVAILABLE = False

# 忽略警告
warnings.filterwarnings('ignore')

print("所有必要的库已导入完成")
print(f"PyMuPDF可用: {PYMUPDF_AVAILABLE}")
print(f"Camelot可用: {CAMELOT_AVAILABLE}")
print(f"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}")
print(f"Transformers可用: {TRANSFORMERS_AVAILABLE}")

def extract_text_with_pymupdf(pdf_path: str) -> Tuple[str, List[str]]:
    """
    使用PyMuPDF提取PDF文本
    
    参数:
        pdf_path: PDF文件路径
    
    返回:
        (完整文本, 按页分割的文本列表)
    """
    if not PYMUPDF_AVAILABLE:
        return None, []
    
    try:
        doc = fitz.open(pdf_path)
        all_text = ""
        page_texts = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            
            if text.strip():
                all_text += text + "\n"
                page_texts.append(text)
        
        doc.close()
        return all_text, page_texts
        
    except Exception as e:
        print(f"PyMuPDF提取失败: {e}")
        return None, []

def extract_text_with_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:
    """
    使用pdfplumber提取PDF文本和表格
    
    参数:
        pdf_path: PDF文件路径
    
    返回:
        (完整文本, 表格列表)
    """
    try:
        all_text = ""
        all_tables = []
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                # 提取文本
                text = page.extract_text()
                if text:
                    all_text += text + "\n"
                
                # 提取表格
                tables = page.extract_tables()
                for table in tables:
                    if table and len(table) > 1:
                        try:
                            df = pd.DataFrame(table[1:], columns=table[0])
                            all_tables.append(df)
                        except Exception as e:
                            print(f"表格处理失败: {e}")
        
        return all_text, all_tables
        
    except Exception as e:
        print(f"pdfplumber提取失败: {e}")
        return "", []

def extract_text_and_tables_from_pdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:
    """
    从PDF文件中提取文本和表格，使用多种方法确保提取完整性
    
    参数:
        pdf_path: PDF文件路径
    
    返回:
        (提取的文本, 表格列表)
    """
    print(f"开始提取PDF文件: {os.path.basename(pdf_path)}")
    
    # 方法1: PyMuPDF提取文本
    pymupdf_text, _ = extract_text_with_pymupdf(pdf_path)
    
    # 方法2: pdfplumber提取文本和表格
    pdfplumber_text, tables = extract_text_with_pdfplumber(pdf_path)
    
    # 选择最佳文本提取结果
    if pymupdf_text and len(pymupdf_text.strip()) > len(pdfplumber_text.strip()):
        best_text = pymupdf_text
        print(f"使用PyMuPDF提取的文本 (长度: {len(best_text)} 字符)")
    else:
        best_text = pdfplumber_text
        print(f"使用pdfplumber提取的文本 (长度: {len(best_text)} 字符)")
    
    print(f"📊 提取到 {len(tables)} 个表格")
    
    return best_text, tables

print("PDF文本提取模块已定义")

def load_stopwords(stopwords_path: str) -> set:
    """
    加载停用词
    
    参数:
        stopwords_path: 停用词文件路径
    
    返回:
        停用词集合
    """
    stopwords = set()
    
    # 默认停用词
    default_stopwords = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
        '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'
    }
    stopwords.update(default_stopwords)
    
    # 从文件加载停用词
    if os.path.exists(stopwords_path):
        try:
            with open(stopwords_path, 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:
                        stopwords.add(word)
            print(f"从文件加载了 {len(stopwords)} 个停用词")
        except Exception as e:
            print(f"加载停用词文件失败: {e}，使用默认停用词")
    else:
        print(f"停用词文件不存在: {stopwords_path}，使用默认停用词")
    
    return stopwords

def clean_text(text: str) -> str:
    """
    清洗文本，去除特殊字符等
    
    参数:
        text: 待清洗的文本
    
    返回:
        清洗后的文本
    """
    # 去除URL
    text = re.sub(r'https?://\S+|www\.\S+', '', text)
    
    # 去除HTML标签
    text = re.sub(r'<.*?>', '', text)
    
    # 去除邮箱
    text = re.sub(r'\S*@\S*\s?', '', text)
    
    # 保留中文、英文、数字和基本标点
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9.,，。、；：''""（）()？?!！\s]+', ' ', text)
    
    # 去除多余的空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def preprocess_text(text: str, stopwords_path: str, min_word_len: int = 2) -> Tuple[List[str], str]:
    """
    文本预处理：分词、去停用词、过滤
    
    参数:
        text: 待处理的文本
        stopwords_path: 停用词文件路径
        min_word_len: 最小词长度
    
    返回:
        (过滤后的词列表, 过滤后的文本)
    """
    print("开始文本预处理...")
    
    if not text or len(text.strip()) == 0:
        print("输入文本为空")
        return [], ""
    
    # 清洗文本
    cleaned_text = clean_text(text)
    print(f"文本清洗完成，长度: {len(cleaned_text)} 字符")
    
    # 加载停用词
    stopwords = load_stopwords(stopwords_path)
    
    # 分词
    print("开始分词...")
    words = jieba.lcut(cleaned_text)
    print(f"分词完成，共 {len(words)} 个词")
    
    # 过滤词语
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= min_word_len and 
            word not in stopwords and 
            not word.isdigit() and 
            not re.match(r'^[\W_]+$', word)):
            filtered_words.append(word)
    
    # 重新组合文本
    filtered_text = ' '.join(filtered_words)
    
    print(f"文本预处理完成，过滤后共 {len(filtered_words)} 个有效词")
    
    return filtered_words, filtered_text

print("文本预处理模块已定义")

def extract_keywords_textrank4zh(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    使用TextRank4zh提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    if not TEXTRANK4ZH_AVAILABLE:
        return []
    
    try:
        tr4w = TextRank4Keyword()
        tr4w.analyze(text=text, lower=True, window=2)
        keywords = tr4w.get_keywords(num=num_keywords, word_min_len=2)
        return [(item.word, item.weight) for item in keywords]
    except Exception as e:
        print(f"TextRank4zh提取失败: {e}")
        return []

def extract_keywords_jieba(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    使用jieba的TextRank提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    try:
        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)
        return list(keywords)
    except Exception as e:
        print(f"jieba TextRank提取失败: {e}")
        return []

def extract_keywords_tfidf(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    使用TF-IDF提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    try:
        # 分词
        words = jieba.lcut(text)
        text_processed = ' '.join(words)
        
        # TF-IDF
        vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))
        tfidf_matrix = vectorizer.fit_transform([text_processed])
        
        # 获取特征名和权重
        feature_names = vectorizer.get_feature_names_out()
        tfidf_scores = tfidf_matrix.toarray()[0]
        
        # 排序并返回前num_keywords个
        word_scores = list(zip(feature_names, tfidf_scores))
        word_scores.sort(key=lambda x: x[1], reverse=True)
        
        return word_scores[:num_keywords]
    except Exception as e:
        print(f"TF-IDF提取失败: {e}")
        return []

def extract_keywords(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    综合多种方法提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    print("开始提取关键词...")
    
    all_keywords = defaultdict(float)
    
    # 方法1: TextRank4zh
    if TEXTRANK4ZH_AVAILABLE:
        keywords_tr4zh = extract_keywords_textrank4zh(text, num_keywords)
        if keywords_tr4zh:
            print(f"TextRank4zh提取到 {len(keywords_tr4zh)} 个关键词")
            for word, weight in keywords_tr4zh:
                all_keywords[word] += weight * 0.4
    
    # 方法2: jieba TextRank
    keywords_jieba = extract_keywords_jieba(text, num_keywords)
    if keywords_jieba:
        print(f"jieba TextRank提取到 {len(keywords_jieba)} 个关键词")
        for word, weight in keywords_jieba:
            all_keywords[word] += weight * 0.3
    
    # 方法3: TF-IDF
    keywords_tfidf = extract_keywords_tfidf(text, num_keywords)
    if keywords_tfidf:
        print(f"TF-IDF提取到 {len(keywords_tfidf)} 个关键词")
        for word, weight in keywords_tfidf:
            all_keywords[word] += weight * 0.3
    
    # 合并并排序
    if not all_keywords:
        print("所有方法都未能提取到关键词")
        return []
    
    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)
    result = sorted_keywords[:num_keywords]
    
    print(f"关键词提取完成，共 {len(result)} 个关键词")
    
    return result

def extract_keywords_with_sentences(text: str, keywords: List[Tuple[str, float]], num_sentences: int = 2) -> Dict[str, List[str]]:
    """
    提取包含关键词的代表性句子
    
    参数:
        text: 原始文本
        keywords: 关键词列表
        num_sentences: 每个关键词返回的句子数量
    
    返回:
        关键词到句子列表的映射
    """
    # 分割句子
    sentences = re.split(r'[。！？!?；;]+', text)
    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
    
    keyword_sentences = {}
    
    for keyword, weight in keywords:
        matching_sentences = []
        
        for sentence in sentences:
            if keyword in sentence:
                matching_sentences.append(sentence)
        
        # 按句子长度排序，选择较长的句子作为代表性句子
        matching_sentences.sort(key=len, reverse=True)
        keyword_sentences[keyword] = matching_sentences[:num_sentences]
    
    return keyword_sentences

def extract_sentiment_keywords(text: str, sentiment_dict: Dict[str, float], num_keywords: int = 20) -> Tuple[List[Tuple[str, float, float]], List[Tuple[str, float, float]]]:
    """
    专门提取有情感意义的关键词
    
    参数:
        text: 待分析的文本
        sentiment_dict: 情感词典
        num_keywords: 每种情感类型提取的关键词数量
    
    返回:
        (正面情感关键词列表, 负面情感关键词列表)
        每个元素为(词, 情感得分, 词频权重)元组
    """
    print("开始提取有情感意义的关键词...")
    
    # 分词并统计词频
    words = jieba.lcut(text)
    word_freq = Counter(words)
    
    # 计算总词数用于归一化
    total_words = len(words)
    
    positive_keywords = []
    negative_keywords = []
    
    # 遍历所有词，找出有情感意义的词
    for word, freq in word_freq.items():
        if len(word) >= 2 and word in sentiment_dict:
            sentiment_score = sentiment_dict[word]
            # 计算词频权重（归一化）
            freq_weight = freq / total_words
            
            if sentiment_score > 0:
                positive_keywords.append((word, sentiment_score, freq_weight))
            elif sentiment_score < 0:
                negative_keywords.append((word, abs(sentiment_score), freq_weight))
    
    # 按情感强度和词频的综合得分排序
    positive_keywords.sort(key=lambda x: x[1] * x[2], reverse=True)
    negative_keywords.sort(key=lambda x: x[1] * x[2], reverse=True)
    
    # 取前num_keywords个
    positive_keywords = positive_keywords[:num_keywords]
    negative_keywords = negative_keywords[:num_keywords]
    
    print(f"情感关键词提取完成")
    print(f"  • 正面情感关键词: {len(positive_keywords)} 个")
    print(f"  • 负面情感关键词: {len(negative_keywords)} 个")
    
    return positive_keywords, negative_keywords

def extract_contextual_sentiment_keywords(text: str, keywords: List[Tuple[str, float]], window_size: int = 10) -> List[Tuple[str, float, List[str]]]:
    """
    基于上下文提取情感关键词
    
    参数:
        text: 待分析的文本
        keywords: 基础关键词列表
        window_size: 上下文窗口大小
    
    返回:
        关键词及其情感上下文列表
        每个元素为(关键词, 上下文情感得分, 情感词列表)元组
    """
    print(" 开始基于上下文提取情感关键词...")
    
    # 预定义情感词
    positive_words = {
        '好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', 
        '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新',
        '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣'
    }
    
    negative_words = {
        '差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少',
        '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退',
        '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境'
    }
    
    # 分词
    words = jieba.lcut(text)
    
    contextual_sentiment_keywords = []
    
    for keyword, weight in keywords:
        # 找到关键词在文本中的所有位置
        keyword_positions = []
        for i, word in enumerate(words):
            if keyword in word or word in keyword:
                keyword_positions.append(i)
        
        if not keyword_positions:
            continue
        
        # 分析每个关键词位置的上下文
        context_sentiment_scores = []
        context_sentiment_words = []
        
        for pos in keyword_positions:
            # 定义上下文窗口
            start = max(0, pos - window_size)
            end = min(len(words), pos + window_size + 1)
            context_words = words[start:end]
            
            # 计算上下文情感得分
            pos_count = sum(1 for w in context_words if w in positive_words)
            neg_count = sum(1 for w in context_words if w in negative_words)
            
            if len(context_words) > 0:
                context_score = (pos_count - neg_count) / len(context_words)
                context_sentiment_scores.append(context_score)
                
                # 收集上下文中的情感词
                sentiment_words_in_context = [w for w in context_words if w in positive_words or w in negative_words]
                context_sentiment_words.extend(sentiment_words_in_context)
        
        # 计算平均上下文情感得分
        if context_sentiment_scores:
            avg_context_sentiment = np.mean(context_sentiment_scores)
            unique_sentiment_words = list(set(context_sentiment_words))
            
            contextual_sentiment_keywords.append((keyword, avg_context_sentiment, unique_sentiment_words))
    
    # 按上下文情感得分的绝对值排序（情感强度）
    contextual_sentiment_keywords.sort(key=lambda x: abs(x[1]), reverse=True)
    
    print(f"上下文情感关键词提取完成，共 {len(contextual_sentiment_keywords)} 个")
    
    return contextual_sentiment_keywords

print("关键词提取模块已定义")

def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:
    """
    加载情感词典
    
    参数:
        positive_path: 正面词典文件路径
        negative_path: 负面词典文件路径
    
    返回:
        情感词典，正面词为正值，负面词为负值
    """
    sentiment_dict = {}
    
    # 默认情感词
    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定']
    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定']
    
    # 添加默认词
    for word in default_positive:
        sentiment_dict[word] = 1.0
    for word in default_negative:
        sentiment_dict[word] = -1.0
    
    # 加载正面词典
    if os.path.exists(positive_path):
        try:
            if positive_path.endswith('.csv'):
                # 尝试多种编码
                loaded = False
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        df = pd.read_csv(positive_path, encoding=encoding)
                        if not df.empty:
                            words = df.iloc[:, 0].tolist()
                            for word in words:
                                if isinstance(word, str) and word.strip():
                                    sentiment_dict[word.strip()] = 1.0
                        print(f"使用 {encoding} 编码加载正面词典: {positive_path}")
                        loaded = True
                        break
                    except (UnicodeDecodeError, pd.errors.EmptyDataError):
                        continue
                if not loaded:
                    print(f"无法加载正面词典: {positive_path}")
            else:
                # 尝试多种编码
                loaded = False
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        with open(positive_path, 'r', encoding=encoding) as f:
                            for line in f:
                                word = line.strip()
                                if word:
                                    sentiment_dict[word] = 1.0
                        print(f"使用 {encoding} 编码加载正面词典: {positive_path}")
                        loaded = True
                        break
                    except UnicodeDecodeError:
                        continue
                if not loaded:
                    print(f"无法加载正面词典: {positive_path}")
        except Exception as e:
            print(f"加载正面词典失败: {e}")
    
    # 加载负面词典
    if os.path.exists(negative_path):
        try:
            if negative_path.endswith('.csv'):
                # 尝试多种编码
                loaded = False
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        df = pd.read_csv(negative_path, encoding=encoding)
                        if not df.empty:
                            words = df.iloc[:, 0].tolist()
                            for word in words:
                                if isinstance(word, str) and word.strip():
                                    sentiment_dict[word.strip()] = -1.0
                        print(f"使用 {encoding} 编码加载负面词典: {negative_path}")
                        loaded = True
                        break
                    except (UnicodeDecodeError, pd.errors.EmptyDataError):
                        continue
                if not loaded:
                    print(f"无法加载负面词典: {negative_path}")
            else:
                # 尝试多种编码
                loaded = False
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        with open(negative_path, 'r', encoding=encoding) as f:
                            for line in f:
                                word = line.strip()
                                if word:
                                    sentiment_dict[word] = -1.0
                        print(f"使用 {encoding} 编码加载负面词典: {negative_path}")
                        loaded = True
                        break
                    except UnicodeDecodeError:
                        continue
                if not loaded:
                    print(f"无法加载负面词典: {negative_path}")
        except Exception as e:
            print(f"加载负面词典失败: {e}")
    
    print(f"情感词典加载完成，共 {len(sentiment_dict)} 个词")
    return sentiment_dict

def sentiment_analysis_by_dict(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]], List[Tuple[str, float]]]:
    """
    基于情感词典的情感分析
    
    参数:
        text: 待分析的文本
        keywords: 关键词列表
        sentiment_dict: 情感词典
    
    返回:
        (整体情感得分, 关键词情感得分列表, 匹配的情感词列表)
    """
    print("开始基于词典的情感分析...")
    
    # 分词
    words = jieba.lcut(text)
    
    # 计算整体情感得分
    total_score = 0
    matched_words = []
    
    for word in words:
        if word in sentiment_dict:
            score = sentiment_dict[word]
            total_score += score
            matched_words.append((word, score))
    
    # 归一化整体得分
    if len(words) > 0:
        overall_score = total_score / len(words)
    else:
        overall_score = 0
    
    # 计算关键词情感得分
    keyword_scores = []
    for keyword, weight in keywords:
        if keyword in sentiment_dict:
            score = sentiment_dict[keyword]
        else:
            # 如果关键词不在词典中，检查是否包含情感词
            score = 0
            for word in sentiment_dict:
                if word in keyword:
                    score += sentiment_dict[word] * 0.5
        
        keyword_scores.append((keyword, score, weight))
    
    print(f"词典情感分析完成，整体得分: {overall_score:.4f}，匹配 {len(matched_words)} 个情感词")
    
    return overall_score, keyword_scores, matched_words

print("情感分析模块已定义")

def sentiment_analysis_by_finbert(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:
    """
    基于FinBERT的情感分析
    
    参数:
        text: 待分析的文本
        keywords: 关键词列表
    
    返回:
        (整体情感得分, 关键词情感得分列表)
    """
    if not TRANSFORMERS_AVAILABLE:
        print("transformers不可用，使用简化的情感分析")
        return sentiment_analysis_by_snownlp(text, keywords)
    
    print("开始基于FinBERT的情感分析...")
    
    try:
        # 检查本地FinBERT模型
        finbert_dir = os.path.join('src', 'finbert')
        
        if os.path.exists(finbert_dir) and os.path.exists(os.path.join(finbert_dir, 'pytorch_model.bin')):
            print("使用本地FinBERT模型")
            model_path = finbert_dir
        else:
            print("使用在线FinBERT模型")
            model_path = 'ProsusAI/finbert'
        
        # 加载模型和分词器
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        model = AutoModelForSequenceClassification.from_pretrained(model_path)
        
        # 分析整体文本
        # 截断文本以适应模型输入限制
        max_length = 512
        if len(text) > max_length * 2:
            # 取开头和结尾部分
            text_for_analysis = text[:max_length] + text[-max_length:]
        else:
            text_for_analysis = text
        
        inputs = tokenizer(text_for_analysis, return_tensors='pt', truncation=True, max_length=max_length, padding=True)
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits = outputs.logits
            probabilities = torch.softmax(logits, dim=1)
            
            # FinBERT输出: [negative, neutral, positive]
            # 计算情感得分: positive - negative
            overall_score = probabilities[0][2].item() - probabilities[0][0].item()
        
        # 分析关键词情感
        keyword_scores = []
        
        for keyword, weight in keywords:
            # 为关键词创建上下文
            keyword_context = f"这个研报提到了{keyword}。{keyword}在金融分析中很重要。"
            
            inputs = tokenizer(keyword_context, return_tensors='pt', truncation=True, max_length=128, padding=True)
            
            with torch.no_grad():
                outputs = model(**inputs)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=1)
                
                # 计算关键词情感得分
                score = probabilities[0][2].item() - probabilities[0][0].item()
                # 降低单独关键词的得分权重
                score = score * 0.7
            
            keyword_scores.append((keyword, score, weight))
        
        print(f"FinBERT情感分析完成，整体得分: {overall_score:.4f}")
        
        return overall_score, keyword_scores
        
    except Exception as e:
        print(f" FinBERT分析失败: {e}，使用备选方法")
        return sentiment_analysis_by_snownlp(text, keywords)

def sentiment_analysis_by_snownlp(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:
    """
    使用SnowNLP进行情感分析（备选方案）
    
    参数:
        text: 待分析的文本
        keywords: 关键词列表
    
    返回:
        (整体情感得分, 关键词情感得分列表)
    """
    try:
        from snownlp import SnowNLP
        
        # 分析整体文本
        s = SnowNLP(text)
        # SnowNLP返回[0,1]，转换为[-1,1]
        overall_score = 2 * s.sentiments - 1
        
        # 分析关键词
        keyword_scores = []
        for keyword, weight in keywords:
            s = SnowNLP(keyword)
            score = 2 * s.sentiments - 1
            keyword_scores.append((keyword, score, weight))
        
        print(f"SnowNLP情感分析完成，整体得分: {overall_score:.4f}")
        return overall_score, keyword_scores
        
    except ImportError:
        print("SnowNLP不可用，使用简单规则")
        # 简单的规则基础情感分析
        positive_words = ['好', '优秀', '增长', '上涨', '盈利', '成功', '发展', '提升']
        negative_words = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '下降']
        
        words = text.split()
        pos_count = sum(1 for word in words if any(pw in word for pw in positive_words))
        neg_count = sum(1 for word in words if any(nw in word for nw in negative_words))
        
        if len(words) > 0:
            overall_score = (pos_count - neg_count) / len(words)
        else:
            overall_score = 0
        
        keyword_scores = [(keyword, 0, weight) for keyword, weight in keywords]
        
        return overall_score, keyword_scores

def sentiment_analysis_by_tfidf_sentiment(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]]]:
    """
    基于TF-IDF和情感词典结合的情感分析
    
    参数:
        text: 待分析的文本
        keywords: 关键词列表
        sentiment_dict: 情感词典
    
    返回:
        (整体情感得分, 关键词情感得分列表)
    """
    print("开始基于TF-IDF和情感词典的情感分析...")
    
    try:
        # 分词
        words = jieba.lcut(text)
        
        # 计算TF-IDF权重
        from sklearn.feature_extraction.text import TfidfVectorizer
        
        # 创建文档（将分词结果重新组合）
        document = ' '.join(words)
        vectorizer = TfidfVectorizer(max_features=1000)
        tfidf_matrix = vectorizer.fit_transform([document])
        feature_names = vectorizer.get_feature_names_out()
        tfidf_scores = tfidf_matrix.toarray()[0]
        
        # 创建词到TF-IDF得分的映射
        word_tfidf = dict(zip(feature_names, tfidf_scores))
        
        # 计算加权情感得分
        total_weighted_score = 0
        total_weight = 0
        
        for word in words:
            if word in sentiment_dict:
                sentiment_score = sentiment_dict[word]
                tfidf_weight = word_tfidf.get(word, 0.001)  # 默认小权重
                
                weighted_score = sentiment_score * tfidf_weight
                total_weighted_score += weighted_score
                total_weight += tfidf_weight
        
        # 计算整体得分
        if total_weight > 0:
            overall_score = total_weighted_score / total_weight
        else:
            overall_score = 0
        
        # 计算关键词情感得分
        keyword_scores = []
        for keyword, weight in keywords:
            # 检查关键词是否在情感词典中
            if keyword in sentiment_dict:
                sentiment_score = sentiment_dict[keyword]
            else:
                # 检查关键词的组成部分
                sentiment_score = 0
                word_count = 0
                for word in jieba.lcut(keyword):
                    if word in sentiment_dict:
                        sentiment_score += sentiment_dict[word]
                        word_count += 1
                
                if word_count > 0:
                    sentiment_score = sentiment_score / word_count
            
            # 获取TF-IDF权重
            tfidf_weight = word_tfidf.get(keyword, weight * 0.1)  # 使用关键词权重作为备选
            
            # 结合TF-IDF权重调整情感得分
            adjusted_score = sentiment_score * (1 + tfidf_weight)
            
            keyword_scores.append((keyword, adjusted_score, weight))
        
        print(f"TF-IDF情感分析完成，整体得分: {overall_score:.4f}")
        return overall_score, keyword_scores
        
    except Exception as e:
        print(f"TF-IDF情感分析失败: {e}，使用简化方法")
        # 简化的备选方法
        words = jieba.lcut(text)
        sentiment_words = [w for w in words if w in sentiment_dict]
        
        if sentiment_words:
            overall_score = sum(sentiment_dict[w] for w in sentiment_words) / len(sentiment_words)
        else:
            overall_score = 0
        
        keyword_scores = [(keyword, sentiment_dict.get(keyword, 0), weight) for keyword, weight in keywords]
        
        return overall_score, keyword_scores

def compare_sentiment_results(dict_score: float, finbert_score: float, 
                            dict_keywords: List[Tuple[str, float, float]], 
                            finbert_keywords: List[Tuple[str, float, float]]) -> Tuple[pd.DataFrame, float]:
    """
    比较两种情感分析方法的结果
    
    参数:
        dict_score: 词典方法整体得分
        finbert_score: FinBERT方法整体得分
        dict_keywords: 词典方法关键词得分
        finbert_keywords: FinBERT方法关键词得分
    
    返回:
        (比较结果DataFrame, 一致率)
    """
    print("开始比较两种情感分析方法的结果...")
    
    # 创建关键词映射
    dict_map = {kw: (score, weight) for kw, score, weight in dict_keywords}
    finbert_map = {kw: (score, weight) for kw, score, weight in finbert_keywords}
    
    # 获取所有关键词
    all_keywords = set(dict_map.keys()) | set(finbert_map.keys())
    
    # 创建比较数据
    comparison_data = []
    
    for keyword in all_keywords:
        dict_score_kw, dict_weight = dict_map.get(keyword, (0, 0))
        finbert_score_kw, finbert_weight = finbert_map.get(keyword, (0, 0))
        
        # 确定情感倾向
        def get_sentiment(score):
            if score > 0.1:
                return '正面'
            elif score < -0.1:
                return '负面'
            else:
                return '中性'
        
        dict_sentiment = get_sentiment(dict_score_kw)
        finbert_sentiment = get_sentiment(finbert_score_kw)
        
        comparison_data.append({
            '关键词': keyword,
            '词典情感得分': dict_score_kw,
            '词典情感倾向': dict_sentiment,
            'FinBERT情感得分': finbert_score_kw,
            'FinBERT情感倾向': finbert_sentiment,
            '权重': max(dict_weight, finbert_weight),
            '得分差异': abs(dict_score_kw - finbert_score_kw),
            '倾向一致': dict_sentiment == finbert_sentiment
        })
    
    # 创建DataFrame
    comparison_df = pd.DataFrame(comparison_data)
    
    # 计算一致率
    if len(comparison_df) > 0:
        agreement_rate = comparison_df['倾向一致'].mean()
    else:
        agreement_rate = 0
    
    print(f"情感分析比较完成，一致率: {agreement_rate:.2%}")
    
    return comparison_df, agreement_rate

print("FinBERT和比较分析模块已定义")

# 定义颜色方案
COLORS = {
    'positive':'#2E8B57',  # 海绿色
    'negative': '#DC143C',  # 深红色
    'neutral': '#708090',   # 石板灰
    'background': '#F8F9FA',
    'highlight': '#4169E1',  # 皇家蓝
    'secondary': '#FFD700'   # 金色
}

def create_sentiment_comparison_chart(dict_score: float, finbert_score: float) -> None:
    """
    创建情感分析方法对比图表
    
    参数:
        dict_score: 词典方法得分
        finbert_score: FinBERT方法得分
    """
    fig, ax = plt.subplots(figsize=(10, 6))
    
    methods = ['Dictionary Analysis', 'FinBERT Analysis']
    scores = [dict_score, finbert_score]
    
    # 确定颜色
    colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral'] for s in scores]
    
    # 创建条形图
    bars = ax.bar(methods, scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + (0.02 if height >= 0 else -0.05),
                f'{score:.3f}', ha='center', va='bottom' if height >= 0 else 'top',
                fontweight='bold', fontsize=12)
    
    # 添加零线
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 设置标题和标签
    ax.set_title('Sentiment Analysis Methods Comparison', fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel('Sentiment Score\n(Negative < 0 < Positive)', fontsize=12, fontweight='bold')
    
    # 设置y轴范围
    y_max = max(abs(min(scores)), abs(max(scores)), 0.5)
    ax.set_ylim(-y_max*1.2, y_max*1.2)
    
    # 添加网格
    ax.grid(axis='y', alpha=0.3, linestyle='--')
    
    # 添加解释文本
    explanation = f"Dictionary Score: {dict_score:.3f} ({'Positive' if dict_score > 0.1 else 'Negative' if dict_score < -0.1 else 'Neutral'})\n"
    explanation += f"FinBERT Score: {finbert_score:.3f} ({'Positive' if finbert_score > 0.1 else 'Negative' if finbert_score < -0.1 else 'Neutral'})"
    
    plt.figtext(0.5, 0.02, explanation, ha='center', fontsize=11,
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

def create_keyword_sentiment_chart(keyword_scores: List[Tuple[str, float, float]], method_name: str, top_n: int = 15) -> None:
    """
    创建关键词情感分布图表
    
    参数:
        keyword_scores: 关键词情感得分列表
        method_name: 方法名称
        top_n: 显示的关键词数量
    """
    if not keyword_scores:
        print(f"No keyword sentiment data available for {method_name}")
        return
    
    # 按权重排序并选择前top_n个
    sorted_keywords = sorted(keyword_scores, key=lambda x: x[2], reverse=True)[:top_n]
    
    keywords = [item[0] for item in sorted_keywords]
    scores = [item[1] for item in sorted_keywords]
    weights = [item[2] for item in sorted_keywords]
    
    # 按情感得分排序
    sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i])
    sorted_keywords = [keywords[i] for i in sorted_indices]
    sorted_scores = [scores[i] for i in sorted_indices]
    sorted_weights = [weights[i] for i in sorted_indices]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 确定颜色
    colors = [COLORS['positive'] if s > 0.05 else COLORS['negative'] if s < -0.05 else COLORS['neutral'] for s in sorted_scores]
    
    # 创建水平条形图
    bars = ax.barh(sorted_keywords, sorted_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # 添加数值标签
    for i, (bar, weight) in enumerate(zip(bars, sorted_weights)):
        width = bar.get_width()
        ax.text(width + (0.02 if width >= 0 else -0.02),
                bar.get_y() + bar.get_height()/2,
                f'{width:.3f}',
                ha='left' if width >= 0 else 'right',
                va='center', fontweight='bold', fontsize=9)
    
    # 添加零线
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    
    # 设置标题和标签
    ax.set_title(f'Keyword Sentiment Distribution ({method_name})', fontsize=14, fontweight='bold', pad=20)
    ax.set_xlabel('Sentiment Score', fontsize=12, fontweight='bold')
    
    # 设置x轴范围
    if sorted_scores:
        x_max = max(abs(min(sorted_scores)), abs(max(sorted_scores)), 0.3)
        ax.set_xlim(-x_max*1.2, x_max*1.2)
    
    # 添加网格
    ax.grid(axis='x', alpha=0.3, linestyle='--')
    
    # 统计信息
    pos_count = sum(1 for s in sorted_scores if s > 0.05)
    neg_count = sum(1 for s in sorted_scores if s < -0.05)
    neu_count = len(sorted_scores) - pos_count - neg_count
    
    stats_text = f"Positive: {pos_count}, Negative: {neg_count}, Neutral: {neu_count}"
    plt.figtext(0.02, 0.02, stats_text, fontsize=10,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7))
    
    plt.tight_layout()
    plt.show()

def create_agreement_chart(comparison_df: pd.DataFrame) -> None:
    """
    创建两种方法一致性分析图表
    
    参数:
        comparison_df: 比较结果DataFrame
    """
    if comparison_df.empty:
        print("No comparison data available")
        return
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 1. 一致性饼图
    agreement_counts = comparison_df['倾向一致'].value_counts()
    labels = ['Consistent', 'Inconsistent']
    sizes = [agreement_counts.get(True, 0), agreement_counts.get(False, 0)]
    colors = [COLORS['highlight'], COLORS['neutral']]
    
    wedges, texts, autotexts = ax1.pie(sizes, labels=labels, autopct='%1.1f%%',
                                      colors=colors, startangle=90, explode=(0.05, 0))
    
    for autotext in autotexts:
        autotext.set_fontweight('bold')
    
    ax1.set_title('Sentiment Analysis Agreement', fontsize=14, fontweight='bold')
    
    # 2. 情感分布散点图
    dict_scores = comparison_df['词典情感得分'].values
    finbert_scores = comparison_df['FinBERT情感得分'].values
    
    # 根据一致性着色
    colors_scatter = [COLORS['highlight'] if agree else COLORS['secondary'] 
                     for agree in comparison_df['倾向一致']]
    
    ax2.scatter(dict_scores, finbert_scores, c=colors_scatter, alpha=0.7, s=50)
    
    # 添加对角线
    lims = [min(ax2.get_xlim()[0], ax2.get_ylim()[0]),
            max(ax2.get_xlim()[1], ax2.get_ylim()[1])]
    ax2.plot(lims, lims, 'k--', alpha=0.5, zorder=0)
    
    # 添加象限线
    ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.3)
    ax2.axvline(x=0, color='gray', linestyle='-', alpha=0.3)
    
    ax2.set_xlabel('Dictionary Sentiment Score', fontweight='bold')
    ax2.set_ylabel('FinBERT Sentiment Score', fontweight='bold')
    ax2.set_title('Sentiment Score Correlation', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 计算相关系数
    if len(dict_scores) > 1:
        correlation = np.corrcoef(dict_scores, finbert_scores)[0, 1]
        ax2.text(0.05, 0.95, f'Correlation: {correlation:.3f}', 
                transform=ax2.transAxes, fontsize=11,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

def create_wordcloud(keywords: List[Tuple[str, float]]) -> None:
    """
    创建关键词云图
    
    参数:
        keywords: 关键词列表
    """
    if not keywords:
        print("No keywords available for word cloud")
        return
    
    try:
        # 准备词频字典
        word_freq = {word: weight for word, weight in keywords}
        
        # 创建词云
        wordcloud = WordCloud(
            width=800, height=400,
            background_color='white',
            max_words=50,
            colormap='viridis',
            font_path=None  # 使用系统默认字体
        ).generate_from_frequencies(word_freq)
        
        # 显示词云
        plt.figure(figsize=(12, 6))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('Keywords Word Cloud', fontsize=16, fontweight='bold', pad=20)
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"Word cloud generation failed: {e}")
        # 创建简单的条形图作为替代
        top_words = keywords[:20]
        words = [item[0] for item in top_words]
        weights = [item[1] for item in top_words]
        
        plt.figure(figsize=(12, 8))
        plt.barh(words, weights, color=COLORS['highlight'], alpha=0.7)
        plt.xlabel('Weight')
        plt.title('Top Keywords (Alternative to Word Cloud)')
        plt.tight_layout()
        plt.show()

def create_sentiment_keywords_chart(positive_keywords: List[Tuple[str, float, float]], 
                                   negative_keywords: List[Tuple[str, float, float]], 
                                   top_n: int = 10) -> None:
    """
    创建情感关键词分布图表
    
    参数:
        positive_keywords: 正面情感关键词列表
        negative_keywords: 负面情感关键词列表
        top_n: 每种情感显示的关键词数量
    """
    if not positive_keywords and not negative_keywords:
        print("No sentiment keywords available for visualization")
        return
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # 正面情感关键词
    if positive_keywords:
        top_positive = positive_keywords[:top_n]
        pos_words = [item[0] for item in top_positive]
        pos_scores = [item[1] * item[2] for item in top_positive]  # 情感强度 × 词频权重
        
        bars1 = ax1.barh(pos_words, pos_scores, color=COLORS['positive'], alpha=0.8)
        ax1.set_title('Top Positive Sentiment Keywords', fontsize=14, fontweight='bold')
        ax1.set_xlabel('Sentiment Strength × Frequency Weight', fontweight='bold')
        
        # 添加数值标签
        for bar, score in zip(bars1, pos_scores):
            width = bar.get_width()
            ax1.text(width + width*0.01, bar.get_y() + bar.get_height()/2,
                    f'{score:.4f}', ha='left', va='center', fontweight='bold', fontsize=9)
        
        ax1.grid(axis='x', alpha=0.3)
    else:
        ax1.text(0.5, 0.5, 'No Positive Keywords Found', ha='center', va='center', 
                transform=ax1.transAxes, fontsize=12, style='italic')
        ax1.set_title('Top Positive Sentiment Keywords', fontsize=14, fontweight='bold')
    
    # 负面情感关键词
    if negative_keywords:
        top_negative = negative_keywords[:top_n]
        neg_words = [item[0] for item in top_negative]
        neg_scores = [item[1] * item[2] for item in top_negative]  # 情感强度 × 词频权重
        
        bars2 = ax2.barh(neg_words, neg_scores, color=COLORS['negative'], alpha=0.8)
        ax2.set_title('Top Negative Sentiment Keywords', fontsize=14, fontweight='bold')
        ax2.set_xlabel('Sentiment Strength × Frequency Weight', fontweight='bold')
        
        # 添加数值标签
        for bar, score in zip(bars2, neg_scores):
            width = bar.get_width()
            ax2.text(width + width*0.01, bar.get_y() + bar.get_height()/2,
                    f'{score:.4f}', ha='left', va='center', fontweight='bold', fontsize=9)
        
        ax2.grid(axis='x', alpha=0.3)
    else:
        ax2.text(0.5, 0.5, 'No Negative Keywords Found', ha='center', va='center', 
                transform=ax2.transAxes, fontsize=12, style='italic')
        ax2.set_title('Top Negative Sentiment Keywords', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.show()

def create_contextual_sentiment_chart(contextual_keywords: List[Tuple[str, float, List[str]]], 
                                     top_n: int = 15) -> None:
    """
    创建上下文情感关键词图表
    
    参数:
        contextual_keywords: 上下文情感关键词列表
        top_n: 显示的关键词数量
    """
    if not contextual_keywords:
        print("No contextual sentiment keywords available for visualization")
        return
    
    # 取前top_n个关键词
    top_keywords = contextual_keywords[:top_n]
    
    keywords = [item[0] for item in top_keywords]
    scores = [item[1] for item in top_keywords]
    sentiment_words_lists = [item[2] for item in top_keywords]
    
    # 按情感得分排序
    sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i])
    sorted_keywords = [keywords[i] for i in sorted_indices]
    sorted_scores = [scores[i] for i in sorted_indices]
    sorted_sentiment_words = [sentiment_words_lists[i] for i in sorted_indices]
    
    # 创建图表
    fig, ax = plt.subplots(figsize=(14, 10))
    
    # 确定颜色
    colors = [COLORS['positive'] if s > 0.02 else COLORS['negative'] if s < -0.02 else COLORS['neutral'] for s in sorted_scores]
    
    # 创建水平条形图
    bars = ax.barh(sorted_keywords, sorted_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)
    
    # 添加数值标签和情感词信息
    for i, (bar, sentiment_words) in enumerate(zip(bars, sorted_sentiment_words)):
        width = bar.get_width()
        
        # 添加得分标签
        ax.text(width + (0.005 if width >= 0 else -0.005),
                bar.get_y() + bar.get_height()/2,
                f'{width:.3f}',
                ha='left' if width >= 0 else 'right',
                va='center', fontweight='bold', fontsize=9)
        
        # 添加情感词信息（显示前3个）
        if sentiment_words:
            sentiment_text = ', '.join(sentiment_words[:3])
            if len(sentiment_words) > 3:
                sentiment_text += '...'
            
            ax.text(-0.01 if width >= 0 else 0.01,
                    bar.get_y() + bar.get_height()/2,
                    f'({sentiment_text})',
                    ha='right' if width >= 0 else 'left',
                    va='center', fontsize=8, style='italic', alpha=0.7)
    
    # 添加零线
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)
    
    # 设置标题和标签
    ax.set_title('Keywords with Contextual Sentiment Analysis', fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Contextual Sentiment Score\n(Based on surrounding sentiment words)', fontsize=12, fontweight='bold')
    
    # 添加网格
    ax.grid(axis='x', alpha=0.3, linestyle='--')
    
    # 添加说明
    explanation = "Note: Scores based on sentiment words in ±10 word context window.\nParentheses show example sentiment words found near each keyword."
    plt.figtext(0.02, 0.02, explanation, fontsize=10, style='italic',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.3))
    
    plt.tight_layout()
    plt.show()

def create_sentiment_summary_chart(positive_keywords: List[Tuple[str, float, float]], 
                                  negative_keywords: List[Tuple[str, float, float]]) -> None:
    """
    创建情感关键词总结图表
    
    参数:
        positive_keywords: 正面情感关键词列表
        negative_keywords: 负面情感关键词列表
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 情感关键词数量对比
    counts = [len(positive_keywords), len(negative_keywords)]
    labels = ['Positive Keywords', 'Negative Keywords']
    colors = [COLORS['positive'], COLORS['negative']]
    
    bars1 = ax1.bar(labels, counts, color=colors, alpha=0.8)
    ax1.set_title('Sentiment Keywords Count', fontweight='bold')
    ax1.set_ylabel('Number of Keywords')
    
    for bar, count in zip(bars1, counts):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{count}', ha='center', va='bottom', fontweight='bold')
    
    # 2. 情感强度分布
    if positive_keywords:
        pos_strengths = [item[1] for item in positive_keywords]
        ax2.hist(pos_strengths, bins=10, color=COLORS['positive'], alpha=0.7, label='Positive')
    
    if negative_keywords:
        neg_strengths = [item[1] for item in negative_keywords]
        ax2.hist(neg_strengths, bins=10, color=COLORS['negative'], alpha=0.7, label='Negative')
    
    ax2.set_title('Sentiment Strength Distribution', fontweight='bold')
    ax2.set_xlabel('Sentiment Strength')
    ax2.set_ylabel('Frequency')
    ax2.legend()
    ax2.grid(alpha=0.3)
    
    # 3. 词频权重分布
    if positive_keywords:
        pos_freqs = [item[2] for item in positive_keywords]
        ax3.scatter(range(len(pos_freqs)), pos_freqs, color=COLORS['positive'], alpha=0.7, label='Positive')
    
    if negative_keywords:
        neg_freqs = [item[2] for item in negative_keywords]
        ax3.scatter(range(len(neg_freqs)), neg_freqs, color=COLORS['negative'], alpha=0.7, label='Negative')
    
    ax3.set_title('Frequency Weight Distribution', fontweight='bold')
    ax3.set_xlabel('Keyword Index')
    ax3.set_ylabel('Frequency Weight')
    ax3.legend()
    ax3.grid(alpha=0.3)
    
    # 4. 综合得分（强度×频率）分布
    if positive_keywords:
        pos_combined = [item[1] * item[2] for item in positive_keywords]
        ax4.bar(range(len(pos_combined)), pos_combined, color=COLORS['positive'], alpha=0.7, label='Positive')
    
    if negative_keywords:
        neg_combined = [item[1] * item[2] for item in negative_keywords]
        neg_start = len(positive_keywords) if positive_keywords else 0
        ax4.bar(range(neg_start, neg_start + len(neg_combined)), neg_combined, 
                color=COLORS['negative'], alpha=0.7, label='Negative')
    
    ax4.set_title('Combined Score (Strength × Frequency)', fontweight='bold')
    ax4.set_xlabel('Keyword Index')
    ax4.set_ylabel('Combined Score')
    ax4.legend()
    ax4.grid(alpha=0.3)
    
    plt.tight_layout()
    plt.show()

print("可视化模块已定义")

def analyze_financial_report(pdf_path: str, 
                           positive_dict_path: str = None, 
                           negative_dict_path: str = None,
                           stopwords_path: str = None,
                           num_keywords: int = 20) -> Dict:
    """
    完整的研报情感分析流程
    
    参数:
        pdf_path: PDF文件路径
        positive_dict_path: 正面词典路径
        negative_dict_path: 负面词典路径
        stopwords_path: 停用词文件路径
        num_keywords: 提取的关键词数量
    
    返回:
        分析结果字典
    """
    print("开始研报情感分析流程")
    print("=" * 60)
    
    results = {
        'pdf_path': pdf_path,
        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'success': False,
        'error': None
    }
    
    try:
        # 步骤1: PDF文本提取
        print("\n 步骤1: PDF文本和表格提取")
        text, tables = extract_text_and_tables_from_pdf(pdf_path)
        
        if not text or len(text.strip()) < 100:
            raise ValueError("PDF文本提取失败或内容过少")
        
        results['text_length'] = len(text)
        results['tables_count'] = len(tables)
        results['text_sample'] = text[:500] + "..." if len(text) > 500 else text
        
        print(f"文本提取完成: {len(text)} 字符, {len(tables)} 个表格")
        
        # 步骤2: 文本预处理
        print("\n 步骤2: 文本预处理")
        if not stopwords_path:
            stopwords_path = os.path.join('data', 'stopwords.txt')
        
        filtered_words, filtered_text = preprocess_text(text, stopwords_path)
        
        if not filtered_words:
            raise ValueError("文本预处理失败")
        
        results['filtered_words_count'] = len(filtered_words)
        results['filtered_text_length'] = len(filtered_text)
        
        # 步骤3: 关键词提取
        print("\n  步骤3: 关键词提取")
        keywords = extract_keywords(filtered_text, num_keywords)
        
        if not keywords:
            raise ValueError("关键词提取失败")
        
        results['keywords'] = keywords
        results['keywords_count'] = len(keywords)
        
        # 提取关键词代表性句子
        keyword_sentences = extract_keywords_with_sentences(text, keywords)
        results['keyword_sentences'] = keyword_sentences
        
        # 步骤4: 情感分析
        print("\n 步骤4: 情感分析")
        
        # 4.1 词典方法
        print("\n 4.1 基于词典的情感分析")
        if not positive_dict_path:
            positive_dict_path = os.path.join('data', 'CFSD中文金融情感词典', '正面词典.csv')
        if not negative_dict_path:
            negative_dict_path = os.path.join('data', 'CFSD中文金融情感词典', '负面词典.csv')
        
        sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)
        dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(
            filtered_text, keywords, sentiment_dict
        )
        
        results['dict_analysis'] = {
            'overall_score': dict_score,
            'sentiment': '正面' if dict_score > 0.1 else '负面' if dict_score < -0.1 else '中性',
            'keyword_scores': dict_keywords,
            'matched_words': matched_words,
            'matched_words_count': len(matched_words)
        }
        
        # 4.2 FinBERT方法
        print("\n 4.2 基于FinBERT的情感分析")
        finbert_score, finbert_keywords = sentiment_analysis_by_finbert(
            filtered_text, keywords
        )
        
        results['finbert_analysis'] = {
            'overall_score': finbert_score,
            'sentiment': '正面' if finbert_score > 0.1 else '负面' if finbert_score < -0.1 else '中性',
            'keyword_scores': finbert_keywords
        }
        
        # 4.3 结果比较
        print("\n 4.3 两种方法结果比较")
        comparison_df, agreement_rate = compare_sentiment_results(
            dict_score, finbert_score, dict_keywords, finbert_keywords
        )
        
        results['comparison'] = {
            'agreement_rate': agreement_rate,
            'comparison_df': comparison_df,
            'combined_score': (dict_score + finbert_score) / 2,
            'score_difference': abs(dict_score - finbert_score)
        }
        
        # 步骤5: 情感关键词提取
        print("\n 步骤5: 情感关键词提取")
        
        # 5.1 提取有情感意义的关键词
        print("\n 5.1 提取有情感意义的关键词")
        positive_keywords, negative_keywords = extract_sentiment_keywords(
            filtered_text, sentiment_dict, num_keywords=15
        )
        
        results['sentiment_keywords'] = {
            'positive_keywords': positive_keywords,
            'negative_keywords': negative_keywords,
            'positive_count': len(positive_keywords),
            'negative_count': len(negative_keywords)
        }
        
        # 5.2 基于上下文的情感关键词分析
        print("\n 5.2 基于上下文的情感关键词分析")
        contextual_keywords = extract_contextual_sentiment_keywords(
            filtered_text, keywords, window_size=10
        )
        
        results['contextual_sentiment_keywords'] = contextual_keywords
        
        # 综合评估
        combined_score = results['comparison']['combined_score']
        results['final_assessment'] = {
            'combined_score': combined_score,
            'combined_sentiment': '正面' if combined_score > 0.1 else '负面' if combined_score < -0.1 else '中性',
            'confidence': 'high' if agreement_rate > 0.7 else 'medium' if agreement_rate > 0.4 else 'low',
            'agreement_rate': agreement_rate,
            'sentiment_keyword_balance': len(positive_keywords) / (len(positive_keywords) + len(negative_keywords)) if (positive_keywords or negative_keywords) else 0.5
        }
        
        results['success'] = True
        print("\n 研报情感分析完成!")
        
        return results
        
    except Exception as e:
        error_msg = f"分析过程中出错: {str(e)}"
        print(f"\n {error_msg}")
        results['error'] = error_msg
        return results

def display_analysis_results(results: Dict) -> None:
    """
    显示分析结果摘要
    
    参数:
        results: 分析结果字典
    """
    if not results['success']:
        print(f" 分析失败: {results.get('error', '未知错误')}")
        return
    
    print("\n" + "=" * 60)
    print(" 研报情感分析结果摘要")
    print("=" * 60)
    
    # 基本信息
    print(f"\n 文档信息:")
    print(f"  • 文件: {os.path.basename(results['pdf_path'])}")
    print(f"  • 分析时间: {results['timestamp']}")
    print(f"  • 原始文本长度: {results['text_length']:,} 字符")
    print(f"  • 提取表格数量: {results['tables_count']} 个")
    print(f"  • 有效词汇数量: {results['filtered_words_count']:,} 个")
    print(f"  • 关键词数量: {results['keywords_count']} 个")
    
    # 关键词展示
    print(f"\n 前10个关键词:")
    for i, (word, weight) in enumerate(results['keywords'][:10], 1):
        print(f"  {i:2d}. {word:<12} (权重: {weight:.4f})")
    
    # 情感分析结果
    dict_analysis = results['dict_analysis']
    finbert_analysis = results['finbert_analysis']
    final_assessment = results['final_assessment']
    
    print(f"\n 情感分析结果:")
    print(f"  • 词典方法:")
    print(f"    - 情感得分: {dict_analysis['overall_score']:+.4f}")
    print(f"    - 情感倾向: {dict_analysis['sentiment']}")
    print(f"    - 匹配情感词: {dict_analysis['matched_words_count']} 个")
    
    print(f"  • FinBERT方法:")
    print(f"    - 情感得分: {finbert_analysis['overall_score']:+.4f}")
    print(f"    - 情感倾向: {finbert_analysis['sentiment']}")
    
    print(f"  • 综合评估:")
    print(f"    - 综合得分: {final_assessment['combined_score']:+.4f}")
    print(f"    - 综合倾向: {final_assessment['combined_sentiment']}")
    print(f"    - 方法一致率: {final_assessment['agreement_rate']:.1%}")
    print(f"    - 结果可信度: {final_assessment['confidence']}")
    
    # 情感解读
    print(f"\n 结果解读:")
    combined_score = final_assessment['combined_score']
    agreement_rate = final_assessment['agreement_rate']
    
    if combined_score > 0.2:
        sentiment_desc = "研报整体呈现积极正面的情感倾向"
    elif combined_score < -0.2:
        sentiment_desc = "研报整体呈现消极负面的情感倾向"
    else:
        sentiment_desc = "研报整体情感倾向相对中性"
    
    if agreement_rate > 0.7:
        confidence_desc = "两种分析方法高度一致，结果可信度很高"
    elif agreement_rate > 0.4:
        confidence_desc = "两种分析方法基本一致，结果具有一定可信度"
    else:
        confidence_desc = "两种分析方法存在较大分歧，建议进一步人工审核"
    
    print(f"  • {sentiment_desc}")
    print(f"  • {confidence_desc}")
    
    print("\n" + "=" * 60)

print(" 主流程模块已定义")

# 设置文件路径 - 请根据实际情况修改
PDF_PATH = "data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf"
POSITIVE_DICT_PATH = "data/CFSD中文金融情感词典/正面词典.csv"
NEGATIVE_DICT_PATH = "data/CFSD中文金融情感词典/负面词典.csv"
STOPWORDS_PATH = "data/stopwords.txt"

# 检查文件是否存在
files_to_check = {
    "PDF文件": PDF_PATH,
    "正面词典": POSITIVE_DICT_PATH,
    "负面词典": NEGATIVE_DICT_PATH,
    "停用词文件": STOPWORDS_PATH
}

print(" 检查文件存在性:")
for name, path in files_to_check.items():
    exists = os.path.exists(path)
    status = "if exists else"
    print(f"  {status} {name}: {path}")
    if not exists:
        print(f"      请确保文件存在或修改路径")

# 如果PDF文件不存在，尝试查找其他PDF文件
if not os.path.exists(PDF_PATH):
    print("\n🔍 尝试查找其他PDF文件...")
    data_dir = "data"
    if os.path.exists(data_dir):
        pdf_files = [f for f in os.listdir(data_dir) if f.endswith('.pdf')]
        if pdf_files:
            PDF_PATH = os.path.join(data_dir, pdf_files[0])
            print(f"找到PDF文件: {PDF_PATH}")
        else:
            print(" 未找到任何PDF文件")
    else:
        print(" data目录不存在")

print(f"\n 将使用以下文件进行分析:")
print(f"   PDF文件: {PDF_PATH}")
print(f"   正面词典: {POSITIVE_DICT_PATH}")
print(f"   负面词典: {NEGATIVE_DICT_PATH}")
print(f"   停用词: {STOPWORDS_PATH}")

# 执行完整的研报情感分析
if os.path.exists(PDF_PATH):
    print(" 开始执行研报情感分析...")
    
    # 执行分析
    analysis_results = analyze_financial_report(
        pdf_path=PDF_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH,
        stopwords_path=STOPWORDS_PATH,
        num_keywords=20
    )
    
    # 显示结果摘要
    display_analysis_results(analysis_results)
    
else:
    print(" PDF文件不存在，无法执行分析")
    print("请确保PDF文件路径正确，或将PDF文件放在data目录下")
    analysis_results = None

# 显示详细的分析结果
if analysis_results and analysis_results['success']:
    print(" 详细分析结果:")
    print("=" * 80)
    
    # 1. 文本样本
    print("\n 提取的文本样本 (前500字符):")
    print("-" * 50)
    print(analysis_results['text_sample'])
    
    # 2. 关键词及其代表性句子
    print("\n 关键词及其代表性句子:")
    print("-" * 50)
    keyword_sentences = analysis_results['keyword_sentences']
    
    for i, (keyword, weight) in enumerate(analysis_results['keywords'][:10], 1):
        print(f"\n{i:2d}. 关键词: {keyword} (权重: {weight:.4f})")
        sentences = keyword_sentences.get(keyword, [])
        if sentences:
            for j, sentence in enumerate(sentences[:2], 1):
                print(f"    句子{j}: {sentence[:100]}{'...' if len(sentence) > 100 else ''}")
        else:
            print("    (未找到包含该关键词的句子)")
    
    # 3. 匹配的情感词
    print("\n 匹配的情感词 (前20个):")
    print("-" * 50)
    matched_words = analysis_results['dict_analysis']['matched_words']
    
    if matched_words:
        # 按情感得分排序
        sorted_sentiment_words = sorted(matched_words, key=lambda x: abs(x[1]), reverse=True)[:20]
        
        positive_words = [(w, s) for w, s in sorted_sentiment_words if s > 0]
        negative_words = [(w, s) for w, s in sorted_sentiment_words if s < 0]
        
        print(f"正面情感词 ({len(positive_words)} 个):")
        for word, score in positive_words[:10]:
            print(f"  • {word} ({score:+.2f})")
        
        print(f"\n负面情感词 ({len(negative_words)} 个):")
        for word, score in negative_words[:10]:
            print(f"  • {word} ({score:+.2f})")
    else:
        print("未匹配到情感词")
    
    # 4. 比较分析结果表格
    print("\n 两种方法比较结果 (前15个关键词):")
    print("-" * 50)
    comparison_df = analysis_results['comparison']['comparison_df']
    
    if not comparison_df.empty:
        # 按权重排序显示前15个
        top_comparison = comparison_df.nlargest(15, '权重')
        
        print(f"{'关键词':<12} {'词典得分':<8} {'词典倾向':<6} {'FinBERT得分':<10} {'FinBERT倾向':<8} {'一致':<4} {'权重':<8}")
        print("-" * 70)
        
        for _, row in top_comparison.iterrows():
            consistent = '是' if row['倾向一致'] else '否'
            print(f"{row['关键词']:<12} {row['词典情感得分']:>7.3f} {row['词典情感倾向']:<6} {row['FinBERT情感得分']:>9.3f} {row['FinBERT情感倾向']:<8} {consistent:<4} {row['权重']:>7.4f}")
    
    # 5. 情感关键词分析结果
    if 'sentiment_keywords' in analysis_results:
        print("\n 情感关键词分析结果:")
        print("-" * 50)
        
        sentiment_keywords = analysis_results['sentiment_keywords']
        positive_keywords = sentiment_keywords['positive_keywords']
        negative_keywords = sentiment_keywords['negative_keywords']
        
        print(f"正面情感关键词 ({len(positive_keywords)} 个):")
        if positive_keywords:
            for i, (word, sentiment_score, freq_weight) in enumerate(positive_keywords[:10], 1):
                combined_score = sentiment_score * freq_weight
                print(f"  {i:2d}. {word:<12} 情感强度: {sentiment_score:.3f}, 词频权重: {freq_weight:.6f}, 综合得分: {combined_score:.6f}")
        else:
            print("  (未找到正面情感关键词)")
        
        print(f"\n负面情感关键词 ({len(negative_keywords)} 个):")
        if negative_keywords:
            for i, (word, sentiment_score, freq_weight) in enumerate(negative_keywords[:10], 1):
                combined_score = sentiment_score * freq_weight
                print(f"  {i:2d}. {word:<12} 情感强度: {sentiment_score:.3f}, 词频权重: {freq_weight:.6f}, 综合得分: {combined_score:.6f}")
        else:
            print("  (未找到负面情感关键词)")
    
    # 6. 上下文情感关键词分析结果
    if 'contextual_sentiment_keywords' in analysis_results:
        contextual_keywords = analysis_results['contextual_sentiment_keywords']
        if contextual_keywords:
            print("\n 上下文情感关键词分析 (前10个):")
            print("-" * 50)
            
            for i, (keyword, context_score, sentiment_words) in enumerate(contextual_keywords[:10], 1):
                sentiment_tendency = '正面' if context_score > 0.02 else '负面' if context_score < -0.02 else '中性'
                sentiment_words_str = ', '.join(sentiment_words[:5]) if sentiment_words else '无'
                if len(sentiment_words) > 5:
                    sentiment_words_str += '...'
                
                print(f"  {i:2d}. {keyword:<12} 上下文得分: {context_score:+.4f} ({sentiment_tendency})")
                print(f"      相关情感词: {sentiment_words_str}")
    
    print("\n" + "=" * 80)
else:
    print(" 无法显示详细结果，分析未成功完成")

# 生成可视化图表
if analysis_results and analysis_results['success']:
    print(" 生成可视化图表...")
    
    # 获取分析结果
    dict_score = analysis_results['dict_analysis']['overall_score']
    finbert_score = analysis_results['finbert_analysis']['overall_score']
    dict_keywords = analysis_results['dict_analysis']['keyword_scores']
    finbert_keywords = analysis_results['finbert_analysis']['keyword_scores']
    comparison_df = analysis_results['comparison']['comparison_df']
    keywords = analysis_results['keywords']
    
    try:
        # 1. 情感分析方法对比图
        print("\n 1. 情感分析方法对比")
        create_sentiment_comparison_chart(dict_score, finbert_score)
        
        # 2. 词典方法关键词情感分布
        if dict_keywords:
            print("\n 2. 词典方法 - 关键词情感分布")
            create_keyword_sentiment_chart(dict_keywords, "Dictionary Method", top_n=15)
        
        # 3. FinBERT方法关键词情感分布
        if finbert_keywords:
            print("\n 3. FinBERT方法 - 关键词情感分布")
            create_keyword_sentiment_chart(finbert_keywords, "FinBERT Method", top_n=15)
        
        # 4. 两种方法一致性分析
        if not comparison_df.empty:
            print("\n 4. 两种方法一致性分析")
            create_agreement_chart(comparison_df)
        
        # 5. 关键词云图
        if keywords:
            print("\n 5. 关键词云图")
            create_wordcloud(keywords)
        
        # 6. 情感关键词可视化
        if 'sentiment_keywords' in analysis_results:
            sentiment_keywords = analysis_results['sentiment_keywords']
            positive_keywords = sentiment_keywords['positive_keywords']
            negative_keywords = sentiment_keywords['negative_keywords']
            
            if positive_keywords or negative_keywords:
                print("\n 6. 情感关键词分布")
                create_sentiment_keywords_chart(positive_keywords, negative_keywords, top_n=10)
                
                print("\n 7. 情感关键词统计总结")
                create_sentiment_summary_chart(positive_keywords, negative_keywords)
        
        # 7. 上下文情感关键词分析
        if 'contextual_sentiment_keywords' in analysis_results:
            contextual_keywords = analysis_results['contextual_sentiment_keywords']
            if contextual_keywords:
                print("\n 8. 上下文情感关键词分析")
                create_contextual_sentiment_chart(contextual_keywords, top_n=15)
        
        print("\n 所有可视化图表生成完成!")
        
    except Exception as e:
        print(f"\n 可视化过程中出现错误: {e}")
        print("这可能是由于缺少某些可视化库或字体问题导致的")
        
else:
    print(" 无法生成可视化图表，分析未成功完成")

# 将分析结果导出为DataFrame，便于进一步分析
if analysis_results and analysis_results['success']:
    print("💾 导出分析结果数据...")
    
    # 1. 关键词分析结果
    keywords_data = []
    for keyword, weight in analysis_results['keywords']:
        # 获取词典和FinBERT的情感得分
        dict_score = 0
        finbert_score = 0
        
        # 从词典分析结果中查找
        for kw, score, w in analysis_results['dict_analysis']['keyword_scores']:
            if kw == keyword:
                dict_score = score
                break
        
        # 从FinBERT分析结果中查找
        for kw, score, w in analysis_results['finbert_analysis']['keyword_scores']:
            if kw == keyword:
                finbert_score = score
                break
        
        keywords_data.append({
            '关键词': keyword,
            '权重': weight,
            '词典情感得分': dict_score,
            'FinBERT情感得分': finbert_score,
            '平均情感得分': (dict_score + finbert_score) / 2,
            '得分差异': abs(dict_score - finbert_score)
        })
    
    keywords_df = pd.DataFrame(keywords_data)
    
    print("\n 关键词分析结果表:")
    print(keywords_df.head(10).to_string(index=False, float_format='%.4f'))
    
    # 2. 情感词统计
    matched_words = analysis_results['dict_analysis']['matched_words']
    if matched_words:
        sentiment_stats = {
            '正面情感词数量': len([w for w, s in matched_words if s > 0]),
            '负面情感词数量': len([w for w, s in matched_words if s < 0]),
            '总情感词数量': len(matched_words),
            '正面情感词平均得分': np.mean([s for w, s in matched_words if s > 0]) if any(s > 0 for w, s in matched_words) else 0,
            '负面情感词平均得分': np.mean([s for w, s in matched_words if s < 0]) if any(s < 0 for w, s in matched_words) else 0
        }
        
        print("\n 情感词统计:")
        for key, value in sentiment_stats.items():
            if '得分' in key:
                print(f"  • {key}: {value:+.4f}")
            else:
                print(f"  • {key}: {value}")
    
    # 3. 分析摘要
    print("\n 分析摘要:")
    final_assessment = analysis_results['final_assessment']
    print(f"  • 综合情感得分: {final_assessment['combined_score']:+.4f}")
    print(f"  • 综合情感倾向: {final_assessment['combined_sentiment']}")
    print(f"  • 方法一致率: {final_assessment['agreement_rate']:.1%}")
    print(f"  • 结果可信度: {final_assessment['confidence']}")
    
    # 4. 情感关键词统计
    if 'sentiment_keywords' in analysis_results:
        sentiment_keywords = analysis_results['sentiment_keywords']
        positive_keywords = sentiment_keywords['positive_keywords']
        negative_keywords = sentiment_keywords['negative_keywords']
        
        print("\n 情感关键词统计:")
        print(f"  • 正面情感关键词数量: {len(positive_keywords)}")
        print(f"  • 负面情感关键词数量: {len(negative_keywords)}")
        
        if positive_keywords:
            avg_pos_strength = np.mean([item[1] for item in positive_keywords])
            avg_pos_freq = np.mean([item[2] for item in positive_keywords])
            print(f"  • 正面关键词平均情感强度: {avg_pos_strength:.4f}")
            print(f"  • 正面关键词平均词频权重: {avg_pos_freq:.6f}")
        
        if negative_keywords:
            avg_neg_strength = np.mean([item[1] for item in negative_keywords])
            avg_neg_freq = np.mean([item[2] for item in negative_keywords])
            print(f"  • 负面关键词平均情感强度: {avg_neg_strength:.4f}")
            print(f"  • 负面关键词平均词频权重: {avg_neg_freq:.6f}")
        
        # 情感关键词平衡度
        if 'sentiment_keyword_balance' in final_assessment:
            balance = final_assessment['sentiment_keyword_balance']
            print(f"  • 情感关键词平衡度: {balance:.1%} ({'偏正面' if balance > 0.6 else '偏负面' if balance < 0.4 else '平衡'})")
    
    # 5. 上下文情感关键词统计
    if 'contextual_sentiment_keywords' in analysis_results:
        contextual_keywords = analysis_results['contextual_sentiment_keywords']
        if contextual_keywords:
            print("\n 上下文情感关键词统计:")
            
            positive_context = [kw for kw, score, _ in contextual_keywords if score > 0.02]
            negative_context = [kw for kw, score, _ in contextual_keywords if score < -0.02]
            neutral_context = [kw for kw, score, _ in contextual_keywords if -0.02 <= score <= 0.02]
            
            print(f"  • 上下文正面倾向关键词: {len(positive_context)} 个")
            print(f"  • 上下文负面倾向关键词: {len(negative_context)} 个")
            print(f"  • 上下文中性倾向关键词: {len(neutral_context)} 个")
            
            if contextual_keywords:
                avg_context_score = np.mean([abs(score) for _, score, _ in contextual_keywords])
                print(f"  • 平均上下文情感强度: {avg_context_score:.4f}")
    
    print("\n 数据导出完成!")
    
else:
    print(" 无法导出数据，分析未成功完成")