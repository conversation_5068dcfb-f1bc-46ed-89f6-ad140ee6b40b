#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单修复notebook编码问题
"""

def fix_notebook():
    """修复notebook"""
    
    # 读取文件
    with open('情感分析完整版.ipynb', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复正面词典加载
    old_positive = '"                df = pd.read_csv(positive_path)\\n",'
    new_positive = '''    "                # 尝试多种编码\\n",
    "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
    "                    try:\\n",
    "                        df = pd.read_csv(positive_path, encoding=encoding)\\n",
    "                        if not df.empty:\\n",
    "                            words = df.iloc[:, 0].tolist()\\n",
    "                            for word in words:\\n",
    "                                if isinstance(word, str) and word.strip():\\n",
    "                                    sentiment_dict[word.strip()] = 1.0\\n",
    "                        print(f\\"✅ 使用 {encoding} 编码加载正面词典: {positive_path}\\")\\n",
    "                        loaded = True\\n",
    "                        break\\n",
    "                    except (UnicodeDecodeError, pd.errors.EmptyDataError):\\n",
    "                        continue\\n",
    "                \\n",
    "                if loaded:\\n",
    "                    break\\n",
    "            else:\\n",
    "                # 尝试多种编码\\n",
    "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
    "                    try:\\n",
    "                        with open(positive_path, 'r', encoding=encoding) as f:\\n",
    "                            for line in f:\\n",
    "                                word = line.strip()\\n",
    "                                if word:\\n",
    "                                    sentiment_dict[word] = 1.0\\n",
    "                        print(f\\"✅ 使用 {encoding} 编码加载正面词典: {positive_path}\\")\\n",
    "                        loaded = True\\n",
    "                        break\\n",
    "                    except UnicodeDecodeError:\\n",
    "                        continue\\n",
    "            \\n",
    "            if not loaded:\\n",
    "                print(f\\"⚠️ 无法加载正面词典: {positive_path}\\")\\n",
    "        else:\\n",
    "            print(f\\"⚠️ 正面词典文件不存在: {positive_path}\\")\\n",
    "    \\n",
    "    # 加载负面词典\\n",
    "    if os.path.exists(negative_path):\\n",
    "        loaded = False\\n",
    "        if negative_path.endswith('.csv'):\\n",
    "            # 尝试多种编码\\n",
    "            for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
    "                try:\\n",
    "                    df = pd.read_csv(negative_path, encoding=encoding)\\n",'''
    
    if old_positive in content:
        content = content.replace(old_positive, new_positive)
        print("✅ 修复了正面词典加载")
    
    # 修复负面词典加载
    old_negative = '"                df = pd.read_csv(negative_path)\\n",'
    new_negative = '''    "                # 尝试多种编码\\n",
    "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
    "                    try:\\n",
    "                        df = pd.read_csv(negative_path, encoding=encoding)\\n",'''
    
    if old_negative in content:
        content = content.replace(old_negative, new_negative)
        print("✅ 修复了负面词典加载")
    
    # 保存文件
    with open('情感分析完整版.ipynb', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 修复完成")

if __name__ == "__main__":
    fix_notebook()
