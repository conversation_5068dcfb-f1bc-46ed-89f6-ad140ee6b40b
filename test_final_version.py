#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复版notebook
"""

import os
import json
import re

def test_final_version():
    """测试最终修复版"""
    
    print("测试最终修复版notebook...")
    print("=" * 50)
    
    # 检查文件是否存在
    final_file = '研报情感分析完整版_最终修复版.ipynb'
    if not os.path.exists(final_file):
        print(f"错误: 文件 {final_file} 不存在")
        return False
    
    # 读取文件
    try:
        with open(final_file, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
        print(f"[成功] 成功读取文件: {final_file}")
    except Exception as e:
        print(f"[失败] 读取文件失败: {e}")
        return False
    
    # 检查表情符号
    emoji_count = 0
    total_cells = len(notebook.get('cells', []))
    
    # 常见表情符号模式
    emoji_pattern = re.compile(
        "["
        "\U0001F600-\U0001F64F"  # emoticons
        "\U0001F300-\U0001F5FF"  # symbols & pictographs
        "\U0001F680-\U0001F6FF"  # transport & map symbols
        "\U0001F1E0-\U0001F1FF"  # flags
        "\U00002702-\U000027B0"
        "\U000024C2-\U0001F251"
        "✅❌⚠️📄🔄📝✂️📊🔍📚📈🎨🚀🎉💡🎯🔧📋🆘🎊📁💭🤖🧪"
        "]+", flags=re.UNICODE)
    
    encoding_fixes = 0
    font_fixes = 0
    
    for cell in notebook.get('cells', []):
        if 'source' in cell:
            source_text = ''.join(cell['source']) if isinstance(cell['source'], list) else cell['source']
            
            # 检查表情符号
            emojis = emoji_pattern.findall(source_text)
            emoji_count += len(emojis)
            
            # 检查编码修复
            if 'for encoding in [' in source_text and 'utf-8' in source_text and 'gbk' in source_text:
                encoding_fixes += 1
            
            # 检查字体修复
            if 'platform.system()' in source_text and 'SimHei' in source_text:
                font_fixes += 1
    
    print(f"[统计] 总单元格数: {total_cells}")
    print(f"[统计] 发现表情符号: {emoji_count} 个")
    print(f"[统计] 编码修复代码: {encoding_fixes} 处")
    print(f"[统计] 字体修复代码: {font_fixes} 处")
    
    # 检查关键功能
    has_sentiment_analysis = False
    has_visualization = False
    has_keyword_extraction = False
    
    for cell in notebook.get('cells', []):
        if 'source' in cell:
            source_text = ''.join(cell['source']) if isinstance(cell['source'], list) else cell['source']
            
            if 'sentiment_analysis' in source_text.lower():
                has_sentiment_analysis = True
            if 'matplotlib' in source_text or 'plt.' in source_text:
                has_visualization = True
            if 'jieba' in source_text and 'textrank' in source_text.lower():
                has_keyword_extraction = True
    
    print("\n功能检查:")
    print(f"  情感分析功能: {'[成功] 存在' if has_sentiment_analysis else '[警告] 缺失'}")
    print(f"  可视化功能: {'[成功] 存在' if has_visualization else '[警告] 缺失'}")
    print(f"  关键词提取: {'[成功] 存在' if has_keyword_extraction else '[警告] 缺失'}")
    
    # 总体评估
    print("\n" + "=" * 50)
    print("最终修复版评估结果:")
    
    if emoji_count == 0:
        print("[成功] 表情符号已完全清除")
    else:
        print(f"[警告] 仍有 {emoji_count} 个表情符号")
    
    if encoding_fixes > 0:
        print("[成功] 编码问题已修复")
    else:
        print("[警告] 未发现编码修复代码")
    
    if font_fixes > 0:
        print("[成功] 中文字体问题已修复")
    else:
        print("[警告] 未发现字体修复代码")
    
    if has_sentiment_analysis and has_visualization and has_keyword_extraction:
        print("[成功] 所有核心功能完整保留")
    else:
        print("[警告] 部分功能可能缺失")
    
    # 文件大小检查
    file_size = os.path.getsize(final_file)
    print(f"[信息] 文件大小: {file_size:,} 字节")
    
    if emoji_count == 0 and encoding_fixes > 0 and font_fixes > 0:
        print("\n[完成] 最终修复版质量良好，可以正常使用！")
        print(f"[文件] {final_file}")
        return True
    else:
        print("\n[警告] 最终修复版可能还有问题")
        return False

def compare_versions():
    """比较不同版本"""
    print("\n" + "=" * 50)
    print("版本对比:")
    
    files = [
        '研报情感分析完整版.ipynb',
        '研报情感分析完整版_无表情符号.ipynb', 
        '研报情感分析完整版_最终修复版.ipynb'
    ]
    
    for file in files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"  {file}: {size:,} 字节")
        else:
            print(f"  {file}: 不存在")

def main():
    """主函数"""
    success = test_final_version()
    compare_versions()
    
    print("\n" + "=" * 50)
    if success:
        print("推荐使用: 研报情感分析完整版_最终修复版.ipynb")
        print("特点:")
        print("  - 无表情符号干扰")
        print("  - 编码问题已解决")
        print("  - 中文字体正常显示")
        print("  - 功能完整保留")
    else:
        print("建议检查修复过程或使用其他版本")

if __name__ == "__main__":
    main()
