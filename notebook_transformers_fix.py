
# 在notebook第一个单元格添加这段代码
import warnings
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 修复transformers版本冲突
try:
    # 预先修复cache_utils问题
    import transformers
    import transformers.cache_utils as cache_utils
    
    # 如果缺少OffloadedHybridCache，创建占位符
    if not hasattr(cache_utils, 'OffloadedHybridCache'):
        class OffloadedHybridCache:
            def __init__(self, *args, **kwargs):
                pass
        cache_utils.OffloadedHybridCache = OffloadedHybridCache
        print("✅ 修复了transformers cache_utils兼容性问题")
    
    import torch
    from transformers import AutoTokenizer, AutoModelForSequenceClassification
    TRANSFORMERS_AVAILABLE = True
    print("✅ FinBERT功能已启用（版本冲突已修复）")
    
except Exception as e:
    TRANSFORMERS_AVAILABLE = False
    print(f"⚠️ transformers导入失败: {str(e)[:100]}...")
    print("将使用传统方法进行情感分析")
