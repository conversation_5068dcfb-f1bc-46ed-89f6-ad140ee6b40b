#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复原版notebook，去掉表情符号并修复编码问题
"""

import json
import re

def fix_original_notebook():
    """修复原版notebook"""
    
    print("开始修复原版notebook...")
    
    # 读取原版notebook
    with open('研报情感分析完整版.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # 表情符号映射（简化版）
    emoji_replacements = {
        '✅': '[成功]',
        '❌': '[失败]', 
        '⚠️': '[警告]',
        '📄': '[文档]',
        '🔄': '[处理]',
        '📝': '[文本]',
        '✂️': '[分词]',
        '📊': '[统计]',
        '🔍': '[搜索]',
        '📚': '[词典]',
        '📈': '[结果]',
        '🎨': '[可视化]',
        '🚀': '[开始]',
        '🎉': '[完成]',
        '💡': '[提示]',
        '🎯': '[目标]',
        '🔧': '[修复]',
        '📋': '[报告]',
        '🆘': '[帮助]',
        '🎊': '[庆祝]',
        '📁': '[文件]',
        '💭': '[思考]',
        '🤖': '[AI]',
        '🧪': '[测试]'
    }
    
    def clean_text(text):
        """清理文本"""
        if not isinstance(text, str):
            return text
        
        # 替换表情符号
        for emoji, replacement in emoji_replacements.items():
            text = text.replace(emoji, replacement)
        
        # 移除其他表情符号
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+", flags=re.UNICODE)
        
        text = emoji_pattern.sub('', text)
        return text
    
    # 修复编码问题的代码片段
    encoding_fix_positive = '''                # 尝试多种编码
                loaded = False
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        df = pd.read_csv(positive_path, encoding=encoding)
                        if not df.empty:
                            words = df.iloc[:, 0].tolist()
                            for word in words:
                                if isinstance(word, str) and word.strip():
                                    sentiment_dict[word.strip()] = 1.0
                        print(f"[成功] 使用 {encoding} 编码加载正面词典: {positive_path}")
                        loaded = True
                        break
                    except (UnicodeDecodeError, pd.errors.EmptyDataError):
                        continue
                if not loaded:
                    print(f"[警告] 无法加载正面词典: {positive_path}")'''
    
    encoding_fix_negative = '''                # 尝试多种编码
                loaded = False
                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                    try:
                        df = pd.read_csv(negative_path, encoding=encoding)
                        if not df.empty:
                            words = df.iloc[:, 0].tolist()
                            for word in words:
                                if isinstance(word, str) and word.strip():
                                    sentiment_dict[word.strip()] = -1.0
                        print(f"[成功] 使用 {encoding} 编码加载负面词典: {negative_path}")
                        loaded = True
                        break
                    except (UnicodeDecodeError, pd.errors.EmptyDataError):
                        continue
                if not loaded:
                    print(f"[警告] 无法加载负面词典: {negative_path}")'''
    
    # 中文字体设置代码
    font_fix = '''# 设置matplotlib中文字体支持
import platform
system = platform.system()

# 根据操作系统设置中文字体
if system == 'Windows':
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
elif system == 'Darwin':  # macOS
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'sans-serif']
else:  # Linux
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'sans-serif']

plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 测试中文字体
try:
    fig, ax = plt.subplots(figsize=(1, 1))
    ax.text(0.5, 0.5, '测试中文', ha='center', va='center')
    plt.close(fig)
    print("[成功] 中文字体设置成功")
    CHINESE_FONT_AVAILABLE = True
except Exception as e:
    print(f"[警告] 中文字体设置可能有问题: {e}，将使用英文标签")
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']
    CHINESE_FONT_AVAILABLE = False'''
    
    # 处理每个cell
    for cell in notebook['cells']:
        if 'source' in cell and isinstance(cell['source'], list):
            # 清理表情符号
            cell['source'] = [clean_text(line) for line in cell['source']]
            
            # 修复编码问题
            source_text = ''.join(cell['source'])
            
            # 修复正面词典加载
            if 'df = pd.read_csv(positive_path)' in source_text:
                for i, line in enumerate(cell['source']):
                    if 'df = pd.read_csv(positive_path)' in line:
                        # 替换整个加载逻辑
                        new_lines = encoding_fix_positive.split('\n')
                        cell['source'][i:i+10] = [line + '\n' for line in new_lines]
                        break
            
            # 修复负面词典加载
            if 'df = pd.read_csv(negative_path)' in source_text:
                for i, line in enumerate(cell['source']):
                    if 'df = pd.read_csv(negative_path)' in line:
                        # 替换整个加载逻辑
                        new_lines = encoding_fix_negative.split('\n')
                        cell['source'][i:i+10] = [line + '\n' for line in new_lines]
                        break
            
            # 修复字体设置
            if "plt.rcParams['font.family'] = ['sans-serif']" in source_text:
                for i, line in enumerate(cell['source']):
                    if "plt.rcParams['font.family'] = ['sans-serif']" in line:
                        # 替换字体设置
                        new_lines = font_fix.split('\n')
                        cell['source'][i:i+5] = [line + '\n' for line in new_lines]
                        break
    
    # 保存修复后的文件
    with open('研报情感分析完整版_最终修复版.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)
    
    print("[成功] 原版notebook修复完成")
    print("新文件: 研报情感分析完整版_最终修复版.ipynb")
    print("")
    print("修复内容:")
    print("1. 去除了所有表情符号")
    print("2. 修复了情感词典编码问题")
    print("3. 修复了中文字体显示问题")
    print("4. 保留了所有原版功能")

if __name__ == "__main__":
    fix_original_notebook()
