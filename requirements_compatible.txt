# 研报情感分析系统 - 兼容版本依赖列表
# 使用方法: pip install -r requirements_compatible.txt

# 基础科学计算库
numpy==1.24.3
pandas==2.0.3
matplotlib==3.7.2
seaborn==0.12.2
scikit-learn==1.3.0

# PDF处理库
pdfplumber==0.9.0
PyMuPDF==1.23.3

# 中文文本处理
jieba==0.42.1
textrank4zh==0.3

# 可视化
wordcloud==1.9.2

# 深度学习（兼容版本）
torch==2.0.1
torchvision==0.15.2
torchaudio==2.0.2
transformers==4.30.0
accelerate==0.20.3
tokenizers==0.13.3

# 其他必要库
tqdm==4.65.0
requests==2.31.0

# Jupyter相关
jupyter==1.0.0
ipykernel==6.25.0

# 兼容性库
packaging>=20.0
typing-extensions>=4.0.0
