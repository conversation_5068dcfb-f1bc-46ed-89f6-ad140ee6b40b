#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
研报情感分析系统 - 测试版
修复了依赖冲突问题，提供多种情感分析方法对比
"""

import os
import sys
import re
import warnings
from datetime import datetime
from collections import Counter, defaultdict
from typing import List, Tuple, Dict, Optional

# 数据处理
import pandas as pd
import numpy as np

# PDF处理
import pdfplumber
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("警告: PyMuPDF不可用，将使用其他PDF提取方法")

# 文本处理
import jieba
import jieba.analyse
from tqdm import tqdm

# TextRank
try:
    from textrank4zh import TextRank4Keyword, TextRank4Sentence
    TEXTRANK4ZH_AVAILABLE = True
except ImportError:
    TEXTRANK4ZH_AVAILABLE = False
    print("警告: textrank4zh不可用，将使用jieba的TextRank")

# 机器学习
from sklearn.feature_extraction.text import TfidfVectorizer

# 情感分析
try:
    import snownlp
    SNOWNLP_AVAILABLE = True
except ImportError:
    SNOWNLP_AVAILABLE = False
    print("警告: snownlp不可用，将使用基础情感分析")

# 可视化
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud

# 设置matplotlib
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 忽略警告
warnings.filterwarnings('ignore')

print("✅ 所有必要的库已导入完成")
print(f"PyMuPDF可用: {PYMUPDF_AVAILABLE}")
print(f"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}")
print(f"SnowNLP可用: {SNOWNLP_AVAILABLE}")

class ResearchReportAnalyzer:
    """研报情感分析器"""

    def __init__(self, stopwords_path="data/stopwords.txt"):
        self.stopwords_path = stopwords_path
        self.stopwords = self._load_stopwords()
        self.positive_words = set()
        self.negative_words = set()
        self._load_sentiment_dict()

    def _load_stopwords(self) -> set:
        """加载停用词"""
        stopwords = set()

        # 默认停用词
        default_stopwords = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
            '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'
        }
        stopwords.update(default_stopwords)

        # 从文件加载停用词
        if os.path.exists(self.stopwords_path):
            try:
                with open(self.stopwords_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        word = line.strip()
                        if word:
                            stopwords.add(word)
                print(f"✅ 从文件加载了 {len(stopwords)} 个停用词")
            except Exception as e:
                print(f"⚠️ 加载停用词文件失败: {e}，使用默认停用词")
        else:
            print(f"⚠️ 停用词文件不存在: {self.stopwords_path}，使用默认停用词")

        return stopwords

    def _load_sentiment_dict(self):
        """加载情感词典"""
        # 尝试加载正面词典
        positive_paths = [
            "data/CFSD中文金融情感词典/正面词典.csv",
            "data/正面词典.csv",
            "data/正面词典.txt"
        ]

        for path in positive_paths:
            if os.path.exists(path):
                try:
                    if path.endswith('.csv'):
                        df = pd.read_csv(path, encoding='utf-8')
                        if not df.empty:
                            # 尝试不同的列名
                            for col in df.columns:
                                if '词' in col or 'word' in col.lower():
                                    self.positive_words.update(df[col].dropna().astype(str).tolist())
                                    break
                            else:
                                # 如果没有找到合适的列，使用第一列
                                self.positive_words.update(df.iloc[:, 0].dropna().astype(str).tolist())
                    else:
                        with open(path, 'r', encoding='utf-8') as f:
                            for line in f:
                                word = line.strip()
                                if word:
                                    self.positive_words.add(word)
                    print(f"✅ 加载正面词典: {len(self.positive_words)} 个词")
                    break
                except Exception as e:
                    print(f"⚠️ 加载正面词典失败 {path}: {e}")

        # 尝试加载负面词典
        negative_paths = [
            "data/CFSD中文金融情感词典/负面词典.csv",
            "data/负面词典.csv",
            "data/负面词典.txt"
        ]

        for path in negative_paths:
            if os.path.exists(path):
                try:
                    if path.endswith('.csv'):
                        df = pd.read_csv(path, encoding='utf-8')
                        if not df.empty:
                            # 尝试不同的列名
                            for col in df.columns:
                                if '词' in col or 'word' in col.lower():
                                    self.negative_words.update(df[col].dropna().astype(str).tolist())
                                    break
                            else:
                                # 如果没有找到合适的列，使用第一列
                                self.negative_words.update(df.iloc[:, 0].dropna().astype(str).tolist())
                    else:
                        with open(path, 'r', encoding='utf-8') as f:
                            for line in f:
                                word = line.strip()
                                if word:
                                    self.negative_words.add(word)
                    print(f"✅ 加载负面词典: {len(self.negative_words)} 个词")
                    break
                except Exception as e:
                    print(f"⚠️ 加载负面词典失败 {path}: {e}")

        # 如果没有加载到词典，使用默认词典
        if not self.positive_words:
            self.positive_words = {
                '好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '强劲', '稳定', '乐观', '积极',
                '推荐', '买入', '看好', '超预期', '突破', '创新', '领先', '优势', '机会', '潜力', '价值', '回升', '复苏', '繁荣'
            }
            print(f"✅ 使用默认正面词典: {len(self.positive_words)} 个词")

        if not self.negative_words:
            self.negative_words = {
                '差', '糟糕', '下跌', '亏损', '损失', '风险', '危机', '困难', '问题', '挑战', '压力', '担忧', '悲观', '消极',
                '卖出', '减持', '看空', '低于预期', '下滑', '衰退', '萎缩', '疲软', '恶化', '威胁', '不确定', '波动', '调整'
            }
            print(f"✅ 使用默认负面词典: {len(self.negative_words)} 个词")

    def extract_text_from_pdf(self, pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:
        """从PDF提取文本和表格"""
        print(f"📄 开始提取PDF文件: {os.path.basename(pdf_path)}")

        # 使用pdfplumber提取文本和表格
        try:
            all_text = ""
            all_tables = []

            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    # 提取文本
                    text = page.extract_text()
                    if text:
                        all_text += text + "\n"

                    # 提取表格
                    tables = page.extract_tables()
                    for table in tables:
                        if table and len(table) > 1:
                            try:
                                df = pd.DataFrame(table[1:], columns=table[0])
                                all_tables.append(df)
                            except Exception as e:
                                print(f"表格处理失败: {e}")

            print(f"✅ 提取文本完成 (长度: {len(all_text)} 字符)")
            print(f"📊 提取到 {len(all_tables)} 个表格")

            return all_text, all_tables

        except Exception as e:
            print(f"PDF提取失败: {e}")
            return "", []

    def clean_text(self, text: str) -> str:
        """清洗文本"""
        # 去除URL
        text = re.sub(r'https?://\S+|www\.\S+', '', text)
        # 去除HTML标签
        text = re.sub(r'<.*?>', '', text)
        # 去除邮箱
        text = re.sub(r'\S*@\S*\s?', '', text)
        # 保留中文、英文、数字和基本标点
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9.,，。、；：''""（）()？?!！\\s]+', ' ', text)
        # 去除多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        return text

    def preprocess_text(self, text: str, min_word_len: int = 2) -> Tuple[List[str], str]:
        """文本预处理：分词、去停用词、过滤"""
        print("🔄 开始文本预处理...")

        if not text or len(text.strip()) == 0:
            print("❌ 输入文本为空")
            return [], ""

        # 清洗文本
        cleaned_text = self.clean_text(text)
        print(f"📝 文本清洗完成，长度: {len(cleaned_text)} 字符")

        # 分词
        print("✂️ 开始分词...")
        words = jieba.lcut(cleaned_text)
        print(f"📊 分词完成，共 {len(words)} 个词")

        # 过滤词语
        filtered_words = []
        for word in words:
            word = word.strip()
            if (len(word) >= min_word_len and
                word not in self.stopwords and
                not word.isdigit() and
                not re.match(r'^[\\W_]+$', word)):
                filtered_words.append(word)

        # 重新组合文本
        filtered_text = ' '.join(filtered_words)

        print(f"✅ 文本预处理完成，过滤后共 {len(filtered_words)} 个有效词")

        return filtered_words, filtered_text

    def extract_keywords_multiple_methods(self, text: str, num_keywords: int = 20) -> Dict[str, List[Tuple[str, float]]]:
        """使用多种方法提取关键词"""
        print("🔍 开始关键词提取...")

        results = {}

        # 方法1: TF-IDF
        try:
            print("📊 使用TF-IDF提取关键词...")
            words = jieba.lcut(text)
            filtered_words = [w for w in words if w not in self.stopwords and len(w) > 1]

            if len(filtered_words) > 0:
                # 创建文档
                documents = [' '.join(filtered_words)]

                # TF-IDF向量化
                vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))
                tfidf_matrix = vectorizer.fit_transform(documents)

                # 获取特征名和分数
                feature_names = vectorizer.get_feature_names_out()
                tfidf_scores = tfidf_matrix.toarray()[0]

                # 排序并获取top关键词
                keyword_scores = list(zip(feature_names, tfidf_scores))
                keyword_scores.sort(key=lambda x: x[1], reverse=True)
                results['TF-IDF'] = keyword_scores[:num_keywords]
                print(f"✅ TF-IDF提取完成，获得 {len(results['TF-IDF'])} 个关键词")
        except Exception as e:
            print(f"⚠️ TF-IDF提取失败: {e}")
            results['TF-IDF'] = []

        # 方法2: jieba TextRank
        try:
            print("📊 使用jieba TextRank提取关键词...")
            keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)
            results['jieba_TextRank'] = keywords
            print(f"✅ jieba TextRank提取完成，获得 {len(keywords)} 个关键词")
        except Exception as e:
            print(f"⚠️ jieba TextRank提取失败: {e}")
            results['jieba_TextRank'] = []

        # 方法3: textrank4zh (如果可用)
        if TEXTRANK4ZH_AVAILABLE:
            try:
                print("📊 使用textrank4zh提取关键词...")
                tr4w = TextRank4Keyword()
                tr4w.analyze(text=text, lower=True, window=2)
                keywords = tr4w.get_keywords(num_keywords, word_min_len=2)
                results['textrank4zh'] = [(kw.word, kw.weight) for kw in keywords]
                print(f"✅ textrank4zh提取完成，获得 {len(results['textrank4zh'])} 个关键词")
            except Exception as e:
                print(f"⚠️ textrank4zh提取失败: {e}")
                results['textrank4zh'] = []

        # 方法4: 词频统计
        try:
            print("📊 使用词频统计提取关键词...")
            words = jieba.lcut(text)
            filtered_words = [w for w in words if w not in self.stopwords and len(w) > 1 and not w.isdigit()]
            word_freq = Counter(filtered_words)
            total_words = len(filtered_words)

            # 计算相对频率
            freq_keywords = [(word, count/total_words) for word, count in word_freq.most_common(num_keywords)]
            results['词频统计'] = freq_keywords
            print(f"✅ 词频统计提取完成，获得 {len(freq_keywords)} 个关键词")
        except Exception as e:
            print(f"⚠️ 词频统计提取失败: {e}")
            results['词频统计'] = []

        return results

    def sentiment_analysis_multiple_methods(self, text: str, keywords: List[str] = None) -> Dict[str, Dict]:
        """使用多种方法进行情感分析"""
        print("💭 开始情感分析...")

        results = {}

        # 方法1: 词典法
        try:
            print("📊 使用词典法进行情感分析...")
            words = jieba.lcut(text)

            positive_count = 0
            negative_count = 0
            positive_words_found = []
            negative_words_found = []

            for word in words:
                if word in self.positive_words:
                    positive_count += 1
                    positive_words_found.append(word)
                elif word in self.negative_words:
                    negative_count += 1
                    negative_words_found.append(word)

            total_sentiment_words = positive_count + negative_count

            if total_sentiment_words > 0:
                positive_ratio = positive_count / total_sentiment_words
                negative_ratio = negative_count / total_sentiment_words

                if positive_ratio > negative_ratio:
                    sentiment_label = "正面"
                elif negative_ratio > positive_ratio:
                    sentiment_label = "负面"
                else:
                    sentiment_label = "中性"
            else:
                sentiment_label = "中性"
                positive_ratio = negative_ratio = 0.0

            results['词典法'] = {
                'sentiment': sentiment_label,
                'positive_ratio': positive_ratio,
                'negative_ratio': negative_ratio,
                'positive_count': positive_count,
                'negative_count': negative_count,
                'positive_words': positive_words_found[:10],  # 只显示前10个
                'negative_words': negative_words_found[:10],
                'confidence': abs(positive_ratio - negative_ratio)
            }
            print(f"✅ 词典法分析完成: {sentiment_label} (置信度: {results['词典法']['confidence']:.3f})")

        except Exception as e:
            print(f"⚠️ 词典法分析失败: {e}")
            results['词典法'] = {}

        # 方法2: SnowNLP (如果可用)
        if SNOWNLP_AVAILABLE:
            try:
                print("📊 使用SnowNLP进行情感分析...")
                s = snownlp.SnowNLP(text)
                sentiment_score = s.sentiments

                if sentiment_score > 0.6:
                    sentiment_label = "正面"
                elif sentiment_score < 0.4:
                    sentiment_label = "负面"
                else:
                    sentiment_label = "中性"

                results['SnowNLP'] = {
                    'sentiment': sentiment_label,
                    'score': sentiment_score,
                    'confidence': abs(sentiment_score - 0.5) * 2
                }
                print(f"✅ SnowNLP分析完成: {sentiment_label} (得分: {sentiment_score:.3f})")

            except Exception as e:
                print(f"⚠️ SnowNLP分析失败: {e}")
                results['SnowNLP'] = {}

        # 方法3: 基于关键词的情感分析
        if keywords:
            try:
                print("📊 基于关键词进行情感分析...")
                keyword_sentiments = {}

                for keyword in keywords:
                    # 查找包含关键词的句子
                    sentences = re.split(r'[。！？.!?]', text)
                    keyword_sentences = [s for s in sentences if keyword in s]

                    if keyword_sentences:
                        # 对包含关键词的句子进行情感分析
                        pos_count = neg_count = 0
                        for sentence in keyword_sentences:
                            words = jieba.lcut(sentence)
                            for word in words:
                                if word in self.positive_words:
                                    pos_count += 1
                                elif word in self.negative_words:
                                    neg_count += 1

                        total = pos_count + neg_count
                        if total > 0:
                            sentiment_score = (pos_count - neg_count) / total
                            if sentiment_score > 0.2:
                                sentiment = "正面"
                            elif sentiment_score < -0.2:
                                sentiment = "负面"
                            else:
                                sentiment = "中性"
                        else:
                            sentiment = "中性"
                            sentiment_score = 0.0

                        keyword_sentiments[keyword] = {
                            'sentiment': sentiment,
                            'score': sentiment_score,
                            'positive_count': pos_count,
                            'negative_count': neg_count
                        }

                results['关键词情感'] = keyword_sentiments
                print(f"✅ 关键词情感分析完成，分析了 {len(keyword_sentiments)} 个关键词")

            except Exception as e:
                print(f"⚠️ 关键词情感分析失败: {e}")
                results['关键词情感'] = {}

        return results

    def create_visualizations(self, keywords_results: Dict, sentiment_results: Dict, output_dir: str = "results"):
        """创建可视化图表"""
        print("📊 开始创建可视化图表...")

        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)

        # 1. 关键词对比图
        if keywords_results:
            try:
                fig, axes = plt.subplots(2, 2, figsize=(15, 12))
                fig.suptitle('关键词提取方法对比', fontsize=16, fontweight='bold')

                methods = list(keywords_results.keys())
                for i, method in enumerate(methods[:4]):  # 最多显示4种方法
                    ax = axes[i//2, i%2]

                    if keywords_results[method]:
                        words, scores = zip(*keywords_results[method][:10])

                        # 创建水平条形图
                        y_pos = np.arange(len(words))
                        bars = ax.barh(y_pos, scores, alpha=0.7)

                        # 设置标签
                        ax.set_yticks(y_pos)
                        ax.set_yticklabels(words)
                        ax.set_xlabel('权重/频率')
                        ax.set_title(f'{method} Top 10')
                        ax.grid(axis='x', alpha=0.3)

                        # 添加数值标签
                        for j, (bar, score) in enumerate(zip(bars, scores)):
                            ax.text(bar.get_width() + max(scores)*0.01, bar.get_y() + bar.get_height()/2,
                                   f'{score:.3f}', ha='left', va='center', fontsize=8)
                    else:
                        ax.text(0.5, 0.5, f'{method}\n无数据', ha='center', va='center',
                               transform=ax.transAxes, fontsize=12)
                        ax.set_xticks([])
                        ax.set_yticks([])

                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, 'keywords_comparison.png'), dpi=300, bbox_inches='tight')
                plt.close()
                print("✅ 关键词对比图已保存")

            except Exception as e:
                print(f"⚠️ 创建关键词对比图失败: {e}")

        # 2. 情感分析结果图
        if sentiment_results:
            try:
                fig, axes = plt.subplots(1, 2, figsize=(15, 6))
                fig.suptitle('情感分析结果对比', fontsize=16, fontweight='bold')

                # 情感分布饼图
                sentiment_counts = defaultdict(int)
                methods = []

                for method, result in sentiment_results.items():
                    if method != '关键词情感' and result:
                        sentiment = result.get('sentiment', '中性')
                        sentiment_counts[sentiment] += 1
                        methods.append(method)

                if sentiment_counts:
                    labels = list(sentiment_counts.keys())
                    sizes = list(sentiment_counts.values())
                    colors = {'正面': '#2ecc71', '负面': '#e74c3c', '中性': '#95a5a6'}
                    pie_colors = [colors.get(label, '#bdc3c7') for label in labels]

                    axes[0].pie(sizes, labels=labels, colors=pie_colors, autopct='%1.1f%%', startangle=90)
                    axes[0].set_title('整体情感分布')

                # 方法对比条形图
                if len(methods) > 0:
                    method_scores = []
                    method_names = []

                    for method, result in sentiment_results.items():
                        if method != '关键词情感' and result:
                            if 'confidence' in result:
                                score = result['confidence']
                            elif 'score' in result:
                                score = abs(result['score'] - 0.5) * 2  # 转换为置信度
                            else:
                                score = 0.5

                            method_scores.append(score)
                            method_names.append(method)

                    if method_scores:
                        bars = axes[1].bar(method_names, method_scores, alpha=0.7, color=['#3498db', '#9b59b6', '#f39c12'])
                        axes[1].set_ylabel('置信度')
                        axes[1].set_title('各方法置信度对比')
                        axes[1].set_ylim(0, 1)

                        # 添加数值标签
                        for bar, score in zip(bars, method_scores):
                            axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                                       f'{score:.3f}', ha='center', va='bottom')

                plt.tight_layout()
                plt.savefig(os.path.join(output_dir, 'sentiment_analysis.png'), dpi=300, bbox_inches='tight')
                plt.close()
                print("✅ 情感分析图已保存")

            except Exception as e:
                print(f"⚠️ 创建情感分析图失败: {e}")

        # 3. 词云图
        if keywords_results:
            try:
                # 合并所有关键词
                all_keywords = {}
                for method, keywords in keywords_results.items():
                    for word, score in keywords:
                        if word in all_keywords:
                            all_keywords[word] += score
                        else:
                            all_keywords[word] = score

                if all_keywords:
                    # 创建词云
                    wordcloud = WordCloud(
                        width=800, height=400,
                        background_color='white',
                        max_words=100,
                        font_path=None,  # 使用默认字体
                        colormap='viridis'
                    ).generate_from_frequencies(all_keywords)

                    plt.figure(figsize=(12, 6))
                    plt.imshow(wordcloud, interpolation='bilinear')
                    plt.axis('off')
                    plt.title('关键词词云图', fontsize=16, fontweight='bold', pad=20)
                    plt.tight_layout()
                    plt.savefig(os.path.join(output_dir, 'wordcloud.png'), dpi=300, bbox_inches='tight')
                    plt.close()
                    print("✅ 词云图已保存")

            except Exception as e:
                print(f"⚠️ 创建词云图失败: {e}")

if __name__ == "__main__":
    print("🚀 研报情感分析系统测试版启动")

    # 创建分析器实例
    analyzer = ResearchReportAnalyzer()

    # 测试PDF文件路径
    pdf_path = "data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf"

    if os.path.exists(pdf_path):
        print(f"📁 找到PDF文件: {pdf_path}")

        # 提取文本
        text, tables = analyzer.extract_text_from_pdf(pdf_path)

        if text:
            # 预处理文本
            words, filtered_text = analyzer.preprocess_text(text)

            print(f"📈 预处理结果:")
            print(f"  - 原始文本长度: {len(text)} 字符")
            print(f"  - 过滤后文本长度: {len(filtered_text)} 字符")
            print(f"  - 有效词数量: {len(words)}")
            print(f"  - 提取表格数量: {len(tables)}")

            # 关键词提取
            print("\n" + "="*50)
            keywords_results = analyzer.extract_keywords_multiple_methods(filtered_text, num_keywords=15)

            # 显示关键词提取结果
            print("\n📊 关键词提取结果:")
            for method, keywords in keywords_results.items():
                if keywords:
                    print(f"\n{method}:")
                    for i, (word, score) in enumerate(keywords[:10], 1):
                        print(f"  {i:2d}. {word:<15} ({score:.4f})")

            # 情感分析
            print("\n" + "="*50)
            # 获取前10个关键词用于情感分析
            top_keywords = []
            if keywords_results.get('TF-IDF'):
                top_keywords = [kw[0] for kw in keywords_results['TF-IDF'][:10]]
            elif keywords_results.get('jieba_TextRank'):
                top_keywords = [kw[0] for kw in keywords_results['jieba_TextRank'][:10]]

            sentiment_results = analyzer.sentiment_analysis_multiple_methods(text, top_keywords)

            # 显示情感分析结果
            print("\n💭 情感分析结果:")
            for method, result in sentiment_results.items():
                if result and method != '关键词情感':
                    print(f"\n{method}:")
                    print(f"  情感倾向: {result.get('sentiment', '未知')}")
                    if 'confidence' in result:
                        print(f"  置信度: {result['confidence']:.3f}")
                    if 'score' in result:
                        print(f"  得分: {result['score']:.3f}")
                    if 'positive_count' in result and 'negative_count' in result:
                        print(f"  正面词数: {result['positive_count']}, 负面词数: {result['negative_count']}")
                    if 'positive_words' in result and result['positive_words']:
                        print(f"  正面词示例: {', '.join(result['positive_words'][:5])}")
                    if 'negative_words' in result and result['negative_words']:
                        print(f"  负面词示例: {', '.join(result['negative_words'][:5])}")

            # 关键词情感分析
            if sentiment_results.get('关键词情感'):
                print(f"\n关键词情感分析:")
                for keyword, kw_sentiment in list(sentiment_results['关键词情感'].items())[:5]:
                    print(f"  {keyword}: {kw_sentiment['sentiment']} (得分: {kw_sentiment['score']:.3f})")

            # 创建可视化
            print("\n" + "="*50)
            try:
                analyzer.create_visualizations(keywords_results, sentiment_results)
                print("📊 可视化图表已生成并保存到 results/ 目录")
            except Exception as e:
                print(f"⚠️ 可视化生成失败: {e}")

            # 生成总结报告
            print("\n" + "="*50)
            print("📋 分析总结:")

            # 统计各方法的情感倾向
            sentiment_summary = defaultdict(int)
            for method, result in sentiment_results.items():
                if result and method != '关键词情感':
                    sentiment = result.get('sentiment', '中性')
                    sentiment_summary[sentiment] += 1

            print(f"  📊 情感分析一致性:")
            for sentiment, count in sentiment_summary.items():
                print(f"    {sentiment}: {count} 种方法")

            # 最终情感判断
            if sentiment_summary:
                final_sentiment = max(sentiment_summary.items(), key=lambda x: x[1])[0]
                print(f"  🎯 综合判断: {final_sentiment}")

            # 关键词统计
            all_keywords_flat = []
            for method, keywords in keywords_results.items():
                all_keywords_flat.extend([kw[0] for kw in keywords])

            keyword_freq = Counter(all_keywords_flat)
            print(f"  🔑 高频关键词 (出现在多种方法中):")
            for word, freq in keyword_freq.most_common(10):
                if freq > 1:
                    print(f"    {word}: 出现在 {freq} 种方法中")

            print(f"\n✅ 分析完成！共处理 {len(text)} 字符的文本，提取了 {len(tables)} 个表格")

        else:
            print("❌ 未能提取到文本内容")
    else:
        print(f"❌ PDF文件不存在: {pdf_path}")
        print("请确保PDF文件在正确的路径下")
