@echo off
echo ========================================
echo 研报情感分析系统 - 新环境配置脚本
echo ========================================

echo.
echo 🚀 开始创建新的虚拟环境...

REM 创建新的虚拟环境
conda create -n sentiment_analysis python=3.9 -y
if %errorlevel% neq 0 (
    echo ❌ 创建虚拟环境失败，请检查conda是否安装
    pause
    exit /b 1
)

echo ✅ 虚拟环境创建成功

echo.
echo 🔧 激活虚拟环境并安装依赖...

REM 激活环境
call conda activate sentiment_analysis

REM 配置国内镜像源
echo 📦 配置国内镜像源...
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

REM 升级pip
python -m pip install --upgrade pip

echo.
echo 📚 安装基础科学计算库...
REM 基础科学计算库
pip install numpy==1.24.3
pip install pandas==2.0.3
pip install matplotlib==3.7.2
pip install seaborn==0.12.2
pip install scikit-learn==1.3.0

echo.
echo 📄 安装PDF处理库...
REM PDF处理
pip install pdfplumber==0.9.0
pip install PyMuPDF==1.23.3

echo.
echo 🔤 安装中文文本处理库...
REM 中文文本处理
pip install jieba==0.42.1
pip install textrank4zh==0.3

echo.
echo 🎨 安装可视化库...
REM 可视化
pip install wordcloud==1.9.2

echo.
echo 🤖 安装深度学习库（兼容版本）...
REM 深度学习 - 使用兼容版本
pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu
pip install transformers==4.30.0
pip install accelerate==0.20.3
pip install tokenizers==0.13.3

echo.
echo 🔧 安装其他必要库...
REM 其他必要库
pip install tqdm==4.65.0
pip install requests==2.31.0

echo.
echo 📋 安装Jupyter相关...
REM Jupyter
pip install jupyter==1.0.0
pip install ipykernel==6.25.0

REM 将环境添加到Jupyter
python -m ipykernel install --user --name sentiment_analysis --display-name "Python (情感分析)"

echo.
echo ✅ 所有依赖安装完成！

echo.
echo 🧪 测试关键库导入...
python -c "
import pandas as pd
import numpy as np
import jieba
import matplotlib.pyplot as plt
from wordcloud import WordCloud
print('✅ 基础库导入成功')

try:
    import torch
    from transformers import AutoTokenizer
    print('✅ 深度学习库导入成功')
except Exception as e:
    print(f'⚠️ 深度学习库导入失败: {e}')

try:
    from textrank4zh import TextRank4Keyword
    print('✅ TextRank4zh导入成功')
except Exception as e:
    print(f'⚠️ TextRank4zh导入失败: {e}')

print('🎉 环境配置测试完成！')
"

echo.
echo ========================================
echo 🎉 新环境配置完成！
echo ========================================
echo.
echo 📋 使用说明:
echo 1. 打开Anaconda Prompt
echo 2. 运行: conda activate sentiment_analysis
echo 3. 运行: jupyter notebook
echo 4. 在Jupyter中选择内核: Python (情感分析)
echo 5. 打开您的notebook文件
echo.
echo 🔧 如果遇到问题:
echo - 确保已安装Anaconda或Miniconda
echo - 检查网络连接
echo - 尝试手动运行各个pip install命令
echo.
pause
