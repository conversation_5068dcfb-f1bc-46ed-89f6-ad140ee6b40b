
import sys
print(f"Python版本: {sys.version}")
print(f"环境路径: {sys.prefix}")

test_results = []

# 测试基础库
try:
    import numpy as np
    import pandas as pd
    import matplotlib.pyplot as plt
    test_results.append(("基础科学计算库", True))
except Exception as e:
    test_results.append(("基础科学计算库", False, str(e)))

# 测试PDF处理
try:
    import pdfplumber
    import fitz
    test_results.append(("PDF处理库", True))
except Exception as e:
    test_results.append(("PDF处理库", False, str(e)))

# 测试中文处理
try:
    import jieba
    from textrank4zh import TextRank4Keyword
    test_results.append(("中文文本处理", True))
except Exception as e:
    test_results.append(("中文文本处理", False, str(e)))

# 测试可视化
try:
    from wordcloud import WordCloud
    test_results.append(("可视化库", True))
except Exception as e:
    test_results.append(("可视化库", False, str(e)))

# 测试深度学习（关键）
try:
    import torch
    from transformers import AutoTokenizer, AutoModelForSequenceClassification
    print(f"PyTorch版本: {torch.__version__}")
    test_results.append(("深度学习库", True))
    
    # 测试transformers基本功能
    tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
    test_results.append(("Transformers功能", True))
    
except Exception as e:
    test_results.append(("深度学习库", False, str(e)))

# 输出测试结果
print("\n测试结果:")
success_count = 0
for result in test_results:
    if len(result) == 2:
        name, success = result
        if success:
            print(f"✅ {name}: 成功")
            success_count += 1
        else:
            print(f"❌ {name}: 失败")
    else:
        name, success, error = result
        if success:
            print(f"✅ {name}: 成功")
            success_count += 1
        else:
            print(f"❌ {name}: 失败 - {error}")

print(f"\n总计: {success_count}/{len(test_results)} 项测试通过")

if success_count == len(test_results):
    print("🎉 所有测试通过！环境配置成功！")
else:
    print("⚠️ 部分测试失败，请检查安装")
