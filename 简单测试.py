#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本
"""

import os
import sys
import re
from collections import Counter, defaultdict

# 数据处理
import pandas as pd
import numpy as np

# PDF处理
import pdfplumber

# 文本处理
import jieba
import jieba.analyse

# 机器学习
from sklearn.feature_extraction.text import TfidfVectorizer

# 情感分析
try:
    import snownlp
    SNOWNLP_AVAILABLE = True
except ImportError:
    SNOWNLP_AVAILABLE = False
    print("警告: snownlp不可用")

# 可视化
import matplotlib.pyplot as plt
from wordcloud import WordCloud

print("✅ 库导入完成")
print(f"SnowNLP可用: {SNOWNLP_AVAILABLE}")

def simple_test():
    """简单测试函数"""
    print("🚀 开始简单测试...")
    
    # 测试PDF文件路径
    pdf_path = "data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf"
    
    if os.path.exists(pdf_path):
        print(f"📁 找到PDF文件: {pdf_path}")
        
        # 简单提取文本
        try:
            all_text = ""
            with pdfplumber.open(pdf_path) as pdf:
                for page in pdf.pages[:5]:  # 只处理前5页
                    text = page.extract_text()
                    if text:
                        all_text += text + "\n"
            
            print(f"✅ 提取文本完成 (长度: {len(all_text)} 字符)")
            
            if all_text:
                # 简单分词测试
                words = jieba.lcut(all_text[:1000])  # 只处理前1000字符
                print(f"📊 分词完成，共 {len(words)} 个词")
                
                # 简单关键词提取
                keywords = jieba.analyse.textrank(all_text[:1000], topK=10, withWeight=True)
                print(f"🔍 关键词提取完成，获得 {len(keywords)} 个关键词")
                
                print("\n📝 前10个关键词:")
                for i, (word, score) in enumerate(keywords, 1):
                    print(f"  {i:2d}. {word:<10} ({score:.4f})")
                
                # 简单情感分析
                if SNOWNLP_AVAILABLE:
                    try:
                        s = snownlp.SnowNLP(all_text[:1000])
                        sentiment_score = s.sentiments
                        
                        if sentiment_score > 0.6:
                            sentiment_label = "正面"
                        elif sentiment_score < 0.4:
                            sentiment_label = "负面"
                        else:
                            sentiment_label = "中性"
                        
                        print(f"\n💭 情感分析结果: {sentiment_label} (得分: {sentiment_score:.3f})")
                        
                    except Exception as e:
                        print(f"⚠️ 情感分析失败: {e}")
                
                print("\n✅ 简单测试完成！")
            else:
                print("❌ 未能提取到文本内容")
                
        except Exception as e:
            print(f"❌ PDF处理失败: {e}")
    else:
        print(f"❌ PDF文件不存在: {pdf_path}")

if __name__ == "__main__":
    simple_test()
