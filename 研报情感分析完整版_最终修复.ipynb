# 基础库
import os
import sys
import re
import warnings
from datetime import datetime
from collections import Counter, defaultdict
from typing import List, Tuple, Dict, Optional

# 数据处理
import pandas as pd
import numpy as np

# PDF处理
import pdfplumber
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("警告: PyMuPDF不可用，将使用其他PDF提取方法")

# 文本处理
import jieba
import jieba.analyse

# TextRank
try:
    from textrank4zh import TextRank4Keyword, TextRank4Sentence
    TEXTRANK4ZH_AVAILABLE = True
except ImportError:
    TEXTRANK4ZH_AVAILABLE = False
    print("警告: textrank4zh不可用，将使用jieba的TextRank")

# 机器学习
from sklearn.feature_extraction.text import TfidfVectorizer

# 情感分析
try:
    import snownlp
    SNOWNLP_AVAILABLE = True
except ImportError:
    SNOWNLP_AVAILABLE = False
    print("警告: snownlp不可用，将使用基础情感分析")

# 深度学习 - 修复版本，避免依赖冲突
TRANSFORMERS_AVAILABLE = False
print("注意: 为避免依赖冲突，暂时禁用FinBERT功能，使用多种传统方法进行情感分析")

# 可视化
import matplotlib.pyplot as plt
import seaborn as sns
from wordcloud import WordCloud

# 设置matplotlib
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 忽略警告
warnings.filterwarnings('ignore')

print("✅ 所有必要的库已导入完成")
print(f"PyMuPDF可用: {PYMUPDF_AVAILABLE}")
print(f"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}")
print(f"SnowNLP可用: {SNOWNLP_AVAILABLE}")
print(f"Transformers可用: {TRANSFORMERS_AVAILABLE}")

# 运行已经测试好的研报情感分析脚本
# 这个脚本包含了完整的分析流程
print("🚀 开始运行完整的研报情感分析...")
exec(open('研报情感分析测试版.py').read())
print("\n🎉 分析完成！")

# 显示生成的图表
import os
from IPython.display import Image, display

results_dir = "results"
if os.path.exists(results_dir):
    print("📊 生成的可视化图表：")
    
    # 关键词对比图
    keywords_img = os.path.join(results_dir, "keywords_comparison.png")
    if os.path.exists(keywords_img):
        print("\n🔍 关键词提取方法对比：")
        display(Image(keywords_img))
    
    # 情感分析图
    sentiment_img = os.path.join(results_dir, "sentiment_analysis.png")
    if os.path.exists(sentiment_img):
        print("\n💭 情感分析结果对比：")
        display(Image(sentiment_img))
    
    # 词云图
    wordcloud_img = os.path.join(results_dir, "wordcloud.png")
    if os.path.exists(wordcloud_img):
        print("\n☁️ 关键词词云图：")
        display(Image(wordcloud_img))
else:
    print("⚠️ 结果目录不存在，请先运行分析脚本")