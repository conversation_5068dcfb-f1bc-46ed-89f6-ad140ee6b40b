{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 研报情感分析系统 - 完整版\n", "\n", "本notebook整合了研报情感分析的完整流程，包括：\n", "1. PDF文本提取\n", "2. 文本预处理\n", "3. 关键词提取\n", "4. 情感分析（词典法 + FinBERT）\n", "5. 结果可视化\n", "\n", "所有结果都在notebook内展示，无需外部文件。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库和模块"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["注意: 为避免依赖冲突，暂时禁用FinBERT功能，使用多种传统方法进行情感分析\n", "检测到系统字体: C:/Windows/Fonts/simhei.ttf\n", "字体设置成功\n", "所有必要的库已导入完成\n", "PyMuPDF可用: True\n", "Camelot可用: True\n", "TextRank4zh可用: True\n", "Transformers可用: False\n"]}], "source": ["# 基础库\n", "import os\n", "import sys\n", "import time\n", "import re\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import concurrent.futures\n", "from typing import List, Tuple, Dict, Optional\n", "\n", "# 数据处理\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF处理\n", "import pdfplumber\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"警告: PyMuPDF不可用，将使用其他PDF提取方法\")\n", "\n", "try:\n", "    import camelot\n", "    CAMELOT_AVAILABLE = True\n", "except ImportError:\n", "    CAMELOT_AVAILABLE = False\n", "    print(\"警告: camelot-py不可用，高级表格提取功能将不可用\")\n", "\n", "# 文本处理\n", "import jieba\n", "import jieba.analyse\n", "from tqdm import tqdm\n", "\n", "# TextRank\n", "try:\n", "    from textrank4zh import TextRank4Keyword, TextRank4Sentence\n", "    TEXTRANK4ZH_AVAILABLE = True\n", "except ImportError:\n", "    TEXTRANK4ZH_AVAILABLE = False\n", "    print(\"警告: textrank4zh不可用，将使用jieba的TextRank\")\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 深度学习 - 修复版本，避免依赖冲突\n", "try:\n", "    # 暂时禁用transformers以避免依赖冲突\n", "    # import torch\n", "    # from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "    TRANSFORMERS_AVAILABLE = False\n", "    print(\"注意: 为避免依赖冲突，暂时禁用FinBERT功能，使用多种传统方法进行情感分析\")\n", "except ImportError:\n", "    TRANSFORMERS_AVAILABLE = False\n", "    print(\"警告: transformers不可用，FinBERT功能将不可用\")\n", "\n", "# 可视化\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "import seaborn as sns\n", "from wordcloud import WordCloud\n", "\n", "# 设置matplotlib和WordCloud字体支持\n", "import platform\n", "system = platform.system()\n", "\n", "# 获取系统默认字体路径\n", "def get_system_font_path():\n", "    \"\"\"获取系统默认中文字体路径\"\"\"\n", "    import matplotlib.font_manager as fm\n", "    \n", "    # 优先使用系统默认字体\n", "    system_fonts = []\n", "    \n", "    if system == 'Windows':\n", "        system_fonts = [\n", "            'C:/Windows/Fonts/simhei.ttf',  # 黑体\n", "            'C:/Windows/Fonts/msyh.ttc',    # 微软雅黑\n", "            'C:/Windows/Fonts/simsun.ttc',  # 宋体\n", "            'C:/Windows/Fonts/arial.ttf'    # Arial\n", "        ]\n", "    elif system == 'Darwin':  # macOS\n", "        system_fonts = [\n", "            '/System/Library/Fonts/PingFang.ttc',\n", "            '/System/Library/Fonts/Arial.ttf',\n", "            '/Library/Fonts/Arial.ttf'\n", "        ]\n", "    else:  # Linux\n", "        system_fonts = [\n", "            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',\n", "            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',\n", "            '/usr/share/fonts/TTF/arial.ttf'\n", "        ]\n", "    \n", "    # 检查字体文件是否存在\n", "    for font_path in system_fonts:\n", "        if os.path.exists(font_path):\n", "            return font_path\n", "    \n", "    # 如果没有找到，使用matplotlib的默认字体\n", "    try:\n", "        # 获取系统中可用的字体\n", "        font_list = fm.findSystemFonts()\n", "        if font_list:\n", "            return font_list[0]  # 返回第一个可用字体\n", "    except:\n", "        pass\n", "    \n", "    return None\n", "\n", "# 获取系统字体路径\n", "SYSTEM_FONT_PATH = get_system_font_path()\n", "print(f\"检测到系统字体: {SYSTEM_FONT_PATH}\")\n", "\n", "# 设置matplotlib字体（使用系统默认字体）\n", "plt.rcParams['font.family'] = ['sans-serif']\n", "plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'sans-serif']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['font.size'] = 12\n", "\n", "# 测试字体\n", "try:\n", "    fig, ax = plt.subplots(figsize=(1, 1))\n", "    ax.text(0.5, 0.5, 'Test Font', ha='center', va='center')\n", "    plt.close(fig)\n", "    print(\"字体设置成功\")\n", "    FONT_AVAILABLE = True\n", "except Exception as e:\n", "    print(f\"字体设置可能有问题: {e}\")\n", "    FONT_AVAILABLE = False\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"所有必要的库已导入完成\")\n", "print(f\"PyMuPDF可用: {PYMUPDF_AVAILABLE}\")\n", "print(f\"Camelot可用: {CAMELOT_AVAILABLE}\")\n", "print(f\"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}\")\n", "print(f\"Transformers可用: {TRANSFORMERS_AVAILABLE}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. PDF文本提取模块"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PDF文本提取模块已定义\n"]}], "source": ["def extract_text_with_pymupdf(pdf_path: str) -> Tuple[str, List[str]]:\n", "    \"\"\"\n", "    使用PyMuPDF提取PDF文本\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 按页分割的文本列表)\n", "    \"\"\"\n", "    if not PYMUPDF_AVAILABLE:\n", "        return None, []\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        all_text = \"\"\n", "        page_texts = []\n", "        \n", "        for page_num in range(len(doc)):\n", "            page = doc[page_num]\n", "            text = page.get_text()\n", "            \n", "            if text.strip():\n", "                all_text += text + \"\\n\"\n", "                page_texts.append(text)\n", "        \n", "        doc.close()\n", "        return all_text, page_texts\n", "        \n", "    except Exception as e:\n", "        print(f\"PyMuPDF提取失败: {e}\")\n", "        return None, []\n", "\n", "def extract_text_with_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    使用pdfplumber提取PDF文本和表格\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 表格列表)\n", "    \"\"\"\n", "    try:\n", "        all_text = \"\"\n", "        all_tables = []\n", "        \n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            for page_num, page in enumerate(pdf.pages):\n", "                # 提取文本\n", "                text = page.extract_text()\n", "                if text:\n", "                    all_text += text + \"\\n\"\n", "                \n", "                # 提取表格\n", "                tables = page.extract_tables()\n", "                for table in tables:\n", "                    if table and len(table) > 1:\n", "                        try:\n", "                            df = pd.DataFrame(table[1:], columns=table[0])\n", "                            all_tables.append(df)\n", "                        except Exception as e:\n", "                            print(f\"表格处理失败: {e}\")\n", "        \n", "        return all_text, all_tables\n", "        \n", "    except Exception as e:\n", "        print(f\"pdfplumber提取失败: {e}\")\n", "        return \"\", []\n", "\n", "def extract_text_and_tables_from_pdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    从PDF文件中提取文本和表格，使用多种方法确保提取完整性\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (提取的文本, 表格列表)\n", "    \"\"\"\n", "    print(f\"开始提取PDF文件: {os.path.basename(pdf_path)}\")\n", "    \n", "    # 方法1: PyMuPDF提取文本\n", "    pymupdf_text, _ = extract_text_with_pymupdf(pdf_path)\n", "    \n", "    # 方法2: pdfplumber提取文本和表格\n", "    pdfplumber_text, tables = extract_text_with_pdfplumber(pdf_path)\n", "    \n", "    # 选择最佳文本提取结果\n", "    if pymupdf_text and len(pymupdf_text.strip()) > len(pdfplumber_text.strip()):\n", "        best_text = pymupdf_text\n", "        print(f\"使用PyMuPDF提取的文本 (长度: {len(best_text)} 字符)\")\n", "    else:\n", "        best_text = pdfplumber_text\n", "        print(f\"使用pdfplumber提取的文本 (长度: {len(best_text)} 字符)\")\n", "    \n", "    print(f\"📊 提取到 {len(tables)} 个表格\")\n", "    \n", "    return best_text, tables\n", "\n", "print(\"PDF文本提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 文本预处理模块"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["文本预处理模块已定义\n"]}], "source": ["def load_stopwords(stopwords_path: str) -> set:\n", "    \"\"\"\n", "    加载停用词\n", "    \n", "    参数:\n", "        stopwords_path: 停用词文件路径\n", "    \n", "    返回:\n", "        停用词集合\n", "    \"\"\"\n", "    stopwords = set()\n", "    \n", "    # 默认停用词\n", "    default_stopwords = {\n", "        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',\n", "        '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'\n", "    }\n", "    stopwords.update(default_stopwords)\n", "    \n", "    # 从文件加载停用词\n", "    if os.path.exists(stopwords_path):\n", "        try:\n", "            with open(stopwords_path, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    word = line.strip()\n", "                    if word:\n", "                        stopwords.add(word)\n", "            print(f\"从文件加载了 {len(stopwords)} 个停用词\")\n", "        except Exception as e:\n", "            print(f\"加载停用词文件失败: {e}，使用默认停用词\")\n", "    else:\n", "        print(f\"停用词文件不存在: {stopwords_path}，使用默认停用词\")\n", "    \n", "    return stopwords\n", "\n", "def clean_text(text: str) -> str:\n", "    \"\"\"\n", "    清洗文本，去除特殊字符等\n", "    \n", "    参数:\n", "        text: 待清洗的文本\n", "    \n", "    返回:\n", "        清洗后的文本\n", "    \"\"\"\n", "    # 去除URL\n", "    text = re.sub(r'https?://\\S+|www\\.\\S+', '', text)\n", "    \n", "    # 去除HTML标签\n", "    text = re.sub(r'<.*?>', '', text)\n", "    \n", "    # 去除邮箱\n", "    text = re.sub(r'\\S*@\\S*\\s?', '', text)\n", "    \n", "    # 保留中文、英文、数字和基本标点\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9.,，。、；：''\"\"（）()？?!！\\s]+', ' ', text)\n", "    \n", "    # 去除多余的空白字符\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    return text\n", "\n", "def preprocess_text(text: str, stopwords_path: str, min_word_len: int = 2) -> Tuple[List[str], str]:\n", "    \"\"\"\n", "    文本预处理：分词、去停用词、过滤\n", "    \n", "    参数:\n", "        text: 待处理的文本\n", "        stopwords_path: 停用词文件路径\n", "        min_word_len: 最小词长度\n", "    \n", "    返回:\n", "        (过滤后的词列表, 过滤后的文本)\n", "    \"\"\"\n", "    print(\"开始文本预处理...\")\n", "    \n", "    if not text or len(text.strip()) == 0:\n", "        print(\"输入文本为空\")\n", "        return [], \"\"\n", "    \n", "    # 清洗文本\n", "    cleaned_text = clean_text(text)\n", "    print(f\"文本清洗完成，长度: {len(cleaned_text)} 字符\")\n", "    \n", "    # 加载停用词\n", "    stopwords = load_stopwords(stopwords_path)\n", "    \n", "    # 分词\n", "    print(\"开始分词...\")\n", "    words = jieba.lcut(cleaned_text)\n", "    print(f\"分词完成，共 {len(words)} 个词\")\n", "    \n", "    # 过滤词语\n", "    filtered_words = []\n", "    for word in words:\n", "        word = word.strip()\n", "        if (len(word) >= min_word_len and \n", "            word not in stopwords and \n", "            not word.isdigit() and \n", "            not re.match(r'^[\\W_]+$', word)):\n", "            filtered_words.append(word)\n", "    \n", "    # 重新组合文本\n", "    filtered_text = ' '.join(filtered_words)\n", "    \n", "    print(f\"文本预处理完成，过滤后共 {len(filtered_words)} 个有效词\")\n", "    \n", "    return filtered_words, filtered_text\n", "\n", "print(\"文本预处理模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 关键词提取模块"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["关键词提取模块已定义\n"]}], "source": ["def extract_keywords_textrank4zh(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TextRank4zh提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    if not TEXTRANK4ZH_AVAILABLE:\n", "        return []\n", "    \n", "    try:\n", "        tr4w = TextRank4Keyword()\n", "        tr4w.analyze(text=text, lower=True, window=2)\n", "        keywords = tr4w.get_keywords(num=num_keywords, word_min_len=2)\n", "        return [(item.word, item.weight) for item in keywords]\n", "    except Exception as e:\n", "        print(f\"TextRank4zh提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_jieba(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用jieba的TextRank提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)\n", "        return list(keywords)\n", "    except Exception as e:\n", "        print(f\"jieba TextRank提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_tfidf(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TF-IDF提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        # 分词\n", "        words = jieba.lcut(text)\n", "        text_processed = ' '.join(words)\n", "        \n", "        # TF-IDF\n", "        vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))\n", "        tfidf_matrix = vectorizer.fit_transform([text_processed])\n", "        \n", "        # 获取特征名和权重\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        tfidf_scores = tfidf_matrix.toarray()[0]\n", "        \n", "        # 排序并返回前num_keywords个\n", "        word_scores = list(zip(feature_names, tfidf_scores))\n", "        word_scores.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        return word_scores[:num_keywords]\n", "    except Exception as e:\n", "        print(f\"TF-IDF提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    综合多种方法提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    print(\"开始提取关键词...\")\n", "    \n", "    all_keywords = defaultdict(float)\n", "    \n", "    # 方法1: TextRank4zh\n", "    if TEXTRANK4ZH_AVAILABLE:\n", "        keywords_tr4zh = extract_keywords_textrank4zh(text, num_keywords)\n", "        if keywords_tr4zh:\n", "            print(f\"TextRank4zh提取到 {len(keywords_tr4zh)} 个关键词\")\n", "            for word, weight in keywords_tr4zh:\n", "                all_keywords[word] += weight * 0.4\n", "    \n", "    # 方法2: <PERSON><PERSON><PERSON> TextRank\n", "    keywords_jieba = extract_keywords_jieba(text, num_keywords)\n", "    if keywords_jieba:\n", "        print(f\"jieba TextRank提取到 {len(keywords_jieba)} 个关键词\")\n", "        for word, weight in keywords_jieba:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 方法3: TF-IDF\n", "    keywords_tfidf = extract_keywords_tfidf(text, num_keywords)\n", "    if keywords_tfidf:\n", "        print(f\"TF-IDF提取到 {len(keywords_tfidf)} 个关键词\")\n", "        for word, weight in keywords_tfidf:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 合并并排序\n", "    if not all_keywords:\n", "        print(\"所有方法都未能提取到关键词\")\n", "        return []\n", "    \n", "    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)\n", "    result = sorted_keywords[:num_keywords]\n", "    \n", "    print(f\"关键词提取完成，共 {len(result)} 个关键词\")\n", "    \n", "    return result\n", "\n", "def extract_keywords_with_sentences(text: str, keywords: List[Tuple[str, float]], num_sentences: int = 2) -> Dict[str, List[str]]:\n", "    \"\"\"\n", "    提取包含关键词的代表性句子\n", "    \n", "    参数:\n", "        text: 原始文本\n", "        keywords: 关键词列表\n", "        num_sentences: 每个关键词返回的句子数量\n", "    \n", "    返回:\n", "        关键词到句子列表的映射\n", "    \"\"\"\n", "    # 分割句子\n", "    sentences = re.split(r'[。！？!?；;]+', text)\n", "    sentences = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]\n", "    \n", "    keyword_sentences = {}\n", "    \n", "    for keyword, weight in keywords:\n", "        matching_sentences = []\n", "        \n", "        for sentence in sentences:\n", "            if keyword in sentence:\n", "                matching_sentences.append(sentence)\n", "        \n", "        # 按句子长度排序，选择较长的句子作为代表性句子\n", "        matching_sentences.sort(key=len, reverse=True)\n", "        keyword_sentences[keyword] = matching_sentences[:num_sentences]\n", "    \n", "    return keyword_sentences\n", "\n", "def extract_sentiment_keywords(text: str, sentiment_dict: Dict[str, float], num_keywords: int = 20) -> Tuple[List[Tuple[str, float, float]], List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    专门提取有情感意义的关键词\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        sentiment_dict: 情感词典\n", "        num_keywords: 每种情感类型提取的关键词数量\n", "    \n", "    返回:\n", "        (正面情感关键词列表, 负面情感关键词列表)\n", "        每个元素为(词, 情感得分, 词频权重)元组\n", "    \"\"\"\n", "    print(\"开始提取有情感意义的关键词...\")\n", "    \n", "    # 分词并统计词频\n", "    words = jieba.lcut(text)\n", "    word_freq = Counter(words)\n", "    \n", "    # 计算总词数用于归一化\n", "    total_words = len(words)\n", "    \n", "    positive_keywords = []\n", "    negative_keywords = []\n", "    \n", "    # 遍历所有词，找出有情感意义的词\n", "    for word, freq in word_freq.items():\n", "        if len(word) >= 2 and word in sentiment_dict:\n", "            sentiment_score = sentiment_dict[word]\n", "            # 计算词频权重（归一化）\n", "            freq_weight = freq / total_words\n", "            \n", "            if sentiment_score > 0:\n", "                positive_keywords.append((word, sentiment_score, freq_weight))\n", "            elif sentiment_score < 0:\n", "                negative_keywords.append((word, abs(sentiment_score), freq_weight))\n", "    \n", "    # 按情感强度和词频的综合得分排序\n", "    positive_keywords.sort(key=lambda x: x[1] * x[2], reverse=True)\n", "    negative_keywords.sort(key=lambda x: x[1] * x[2], reverse=True)\n", "    \n", "    # 取前num_keywords个\n", "    positive_keywords = positive_keywords[:num_keywords]\n", "    negative_keywords = negative_keywords[:num_keywords]\n", "    \n", "    print(f\"情感关键词提取完成\")\n", "    print(f\"  • 正面情感关键词: {len(positive_keywords)} 个\")\n", "    print(f\"  • 负面情感关键词: {len(negative_keywords)} 个\")\n", "    \n", "    return positive_keywords, negative_keywords\n", "\n", "def extract_contextual_sentiment_keywords(text: str, keywords: List[Tuple[str, float]], window_size: int = 10) -> List[Tuple[str, float, List[str]]]:\n", "    \"\"\"\n", "    基于上下文提取情感关键词\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 基础关键词列表\n", "        window_size: 上下文窗口大小\n", "    \n", "    返回:\n", "        关键词及其情感上下文列表\n", "        每个元素为(关键词, 上下文情感得分, 情感词列表)元组\n", "    \"\"\"\n", "    print(\" 开始基于上下文提取情感关键词...\")\n", "    \n", "    # 预定义情感词\n", "    positive_words = {\n", "        '好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', \n", "        '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新',\n", "        '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣'\n", "    }\n", "    \n", "    negative_words = {\n", "        '差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少',\n", "        '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退',\n", "        '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境'\n", "    }\n", "    \n", "    # 分词\n", "    words = jieba.lcut(text)\n", "    \n", "    contextual_sentiment_keywords = []\n", "    \n", "    for keyword, weight in keywords:\n", "        # 找到关键词在文本中的所有位置\n", "        keyword_positions = []\n", "        for i, word in enumerate(words):\n", "            if keyword in word or word in keyword:\n", "                keyword_positions.append(i)\n", "        \n", "        if not keyword_positions:\n", "            continue\n", "        \n", "        # 分析每个关键词位置的上下文\n", "        context_sentiment_scores = []\n", "        context_sentiment_words = []\n", "        \n", "        for pos in keyword_positions:\n", "            # 定义上下文窗口\n", "            start = max(0, pos - window_size)\n", "            end = min(len(words), pos + window_size + 1)\n", "            context_words = words[start:end]\n", "            \n", "            # 计算上下文情感得分\n", "            pos_count = sum(1 for w in context_words if w in positive_words)\n", "            neg_count = sum(1 for w in context_words if w in negative_words)\n", "            \n", "            if len(context_words) > 0:\n", "                context_score = (pos_count - neg_count) / len(context_words)\n", "                context_sentiment_scores.append(context_score)\n", "                \n", "                # 收集上下文中的情感词\n", "                sentiment_words_in_context = [w for w in context_words if w in positive_words or w in negative_words]\n", "                context_sentiment_words.extend(sentiment_words_in_context)\n", "        \n", "        # 计算平均上下文情感得分\n", "        if context_sentiment_scores:\n", "            avg_context_sentiment = np.mean(context_sentiment_scores)\n", "            unique_sentiment_words = list(set(context_sentiment_words))\n", "            \n", "            contextual_sentiment_keywords.append((keyword, avg_context_sentiment, unique_sentiment_words))\n", "    \n", "    # 按上下文情感得分的绝对值排序（情感强度）\n", "    contextual_sentiment_keywords.sort(key=lambda x: abs(x[1]), reverse=True)\n", "    \n", "    print(f\"上下文情感关键词提取完成，共 {len(contextual_sentiment_keywords)} 个\")\n", "    \n", "    return contextual_sentiment_keywords\n", "\n", "print(\"关键词提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 情感分析模块"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["情感分析模块已定义\n"]}], "source": ["def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:\n", "    \"\"\"\n", "    加载情感词典\n", "    \n", "    参数:\n", "        positive_path: 正面词典文件路径\n", "        negative_path: 负面词典文件路径\n", "    \n", "    返回:\n", "        情感词典，正面词为正值，负面词为负值\n", "    \"\"\"\n", "    sentiment_dict = {}\n", "    \n", "    # 默认情感词\n", "    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定']\n", "    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定']\n", "    \n", "    # 添加默认词\n", "    for word in default_positive:\n", "        sentiment_dict[word] = 1.0\n", "    for word in default_negative:\n", "        sentiment_dict[word] = -1.0\n", "    \n", "    # 加载正面词典\n", "    if os.path.exists(positive_path):\n", "        try:\n", "            if positive_path.endswith('.csv'):\n", "                # 尝试多种编码\n", "                loaded = False\n", "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "                    try:\n", "                        df = pd.read_csv(positive_path, encoding=encoding)\n", "                        if not df.empty:\n", "                            words = df.iloc[:, 0].tolist()\n", "                            for word in words:\n", "                                if isinstance(word, str) and word.strip():\n", "                                    sentiment_dict[word.strip()] = 1.0\n", "                        print(f\"使用 {encoding} 编码加载正面词典: {positive_path}\")\n", "                        loaded = True\n", "                        break\n", "                    except (UnicodeDecodeError, pd.errors.EmptyDataError):\n", "                        continue\n", "                if not loaded:\n", "                    print(f\"无法加载正面词典: {positive_path}\")\n", "            else:\n", "                # 尝试多种编码\n", "                loaded = False\n", "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "                    try:\n", "                        with open(positive_path, 'r', encoding=encoding) as f:\n", "                            for line in f:\n", "                                word = line.strip()\n", "                                if word:\n", "                                    sentiment_dict[word] = 1.0\n", "                        print(f\"使用 {encoding} 编码加载正面词典: {positive_path}\")\n", "                        loaded = True\n", "                        break\n", "                    except UnicodeDecodeError:\n", "                        continue\n", "                if not loaded:\n", "                    print(f\"无法加载正面词典: {positive_path}\")\n", "        except Exception as e:\n", "            print(f\"加载正面词典失败: {e}\")\n", "    \n", "    # 加载负面词典\n", "    if os.path.exists(negative_path):\n", "        try:\n", "            if negative_path.endswith('.csv'):\n", "                # 尝试多种编码\n", "                loaded = False\n", "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "                    try:\n", "                        df = pd.read_csv(negative_path, encoding=encoding)\n", "                        if not df.empty:\n", "                            words = df.iloc[:, 0].tolist()\n", "                            for word in words:\n", "                                if isinstance(word, str) and word.strip():\n", "                                    sentiment_dict[word.strip()] = -1.0\n", "                        print(f\"使用 {encoding} 编码加载负面词典: {negative_path}\")\n", "                        loaded = True\n", "                        break\n", "                    except (UnicodeDecodeError, pd.errors.EmptyDataError):\n", "                        continue\n", "                if not loaded:\n", "                    print(f\"无法加载负面词典: {negative_path}\")\n", "            else:\n", "                # 尝试多种编码\n", "                loaded = False\n", "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "                    try:\n", "                        with open(negative_path, 'r', encoding=encoding) as f:\n", "                            for line in f:\n", "                                word = line.strip()\n", "                                if word:\n", "                                    sentiment_dict[word] = -1.0\n", "                        print(f\"使用 {encoding} 编码加载负面词典: {negative_path}\")\n", "                        loaded = True\n", "                        break\n", "                    except UnicodeDecodeError:\n", "                        continue\n", "                if not loaded:\n", "                    print(f\"无法加载负面词典: {negative_path}\")\n", "        except Exception as e:\n", "            print(f\"加载负面词典失败: {e}\")\n", "    \n", "    print(f\"情感词典加载完成，共 {len(sentiment_dict)} 个词\")\n", "    return sentiment_dict\n", "\n", "def sentiment_analysis_by_dict(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]], List[Tuple[str, float]]]:\n", "    \"\"\"\n", "    基于情感词典的情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "        sentiment_dict: 情感词典\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表, 匹配的情感词列表)\n", "    \"\"\"\n", "    print(\"开始基于词典的情感分析...\")\n", "    \n", "    # 分词\n", "    words = jieba.lcut(text)\n", "    \n", "    # 计算整体情感得分\n", "    total_score = 0\n", "    matched_words = []\n", "    \n", "    for word in words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            total_score += score\n", "            matched_words.append((word, score))\n", "    \n", "    # 归一化整体得分\n", "    if len(words) > 0:\n", "        overall_score = total_score / len(words)\n", "    else:\n", "        overall_score = 0\n", "    \n", "    # 计算关键词情感得分\n", "    keyword_scores = []\n", "    for keyword, weight in keywords:\n", "        if keyword in sentiment_dict:\n", "            score = sentiment_dict[keyword]\n", "        else:\n", "            # 如果关键词不在词典中，检查是否包含情感词\n", "            score = 0\n", "            for word in sentiment_dict:\n", "                if word in keyword:\n", "                    score += sentiment_dict[word] * 0.5\n", "        \n", "        keyword_scores.append((keyword, score, weight))\n", "    \n", "    print(f\"词典情感分析完成，整体得分: {overall_score:.4f}，匹配 {len(matched_words)} 个情感词\")\n", "    \n", "    return overall_score, keyword_scores, matched_words\n", "\n", "print(\"情感分析模块已定义\")"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["FinBERT和比较分析模块已定义\n"]}], "source": ["def sentiment_analysis_by_fin<PERSON>(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    基于FinBERT的情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表)\n", "    \"\"\"\n", "    if not TRANSFORMERS_AVAILABLE:\n", "        print(\"transformers不可用，使用简化的情感分析\")\n", "        return sentiment_analysis_by_snownlp(text, keywords)\n", "    \n", "    print(\"开始基于FinBERT的情感分析...\")\n", "    \n", "    try:\n", "        # 检查本地FinBERT模型\n", "        finbert_dir = os.path.join('src', 'finbert')\n", "        \n", "        if os.path.exists(finbert_dir) and os.path.exists(os.path.join(finbert_dir, 'pytorch_model.bin')):\n", "            print(\"使用本地FinBERT模型\")\n", "            model_path = finbert_dir\n", "        else:\n", "            print(\"使用在线FinBERT模型\")\n", "            model_path = 'ProsusAI/finbert'\n", "        \n", "        # 加载模型和分词器\n", "        tokenizer = AutoTokenizer.from_pretrained(model_path)\n", "        model = AutoModelForSequenceClassification.from_pretrained(model_path)\n", "        \n", "        # 分析整体文本\n", "        # 截断文本以适应模型输入限制\n", "        max_length = 512\n", "        if len(text) > max_length * 2:\n", "            # 取开头和结尾部分\n", "            text_for_analysis = text[:max_length] + text[-max_length:]\n", "        else:\n", "            text_for_analysis = text\n", "        \n", "        inputs = tokenizer(text_for_analysis, return_tensors='pt', truncation=True, max_length=max_length, padding=True)\n", "        \n", "        with torch.no_grad():\n", "            outputs = model(**inputs)\n", "            logits = outputs.logits\n", "            probabilities = torch.softmax(logits, dim=1)\n", "            \n", "            # FinBERT输出: [negative, neutral, positive]\n", "            # 计算情感得分: positive - negative\n", "            overall_score = probabilities[0][2].item() - probabilities[0][0].item()\n", "        \n", "        # 分析关键词情感\n", "        keyword_scores = []\n", "        \n", "        for keyword, weight in keywords:\n", "            # 为关键词创建上下文\n", "            keyword_context = f\"这个研报提到了{keyword}。{keyword}在金融分析中很重要。\"\n", "            \n", "            inputs = tokenizer(keyword_context, return_tensors='pt', truncation=True, max_length=128, padding=True)\n", "            \n", "            with torch.no_grad():\n", "                outputs = model(**inputs)\n", "                logits = outputs.logits\n", "                probabilities = torch.softmax(logits, dim=1)\n", "                \n", "                # 计算关键词情感得分\n", "                score = probabilities[0][2].item() - probabilities[0][0].item()\n", "                # 降低单独关键词的得分权重\n", "                score = score * 0.7\n", "            \n", "            keyword_scores.append((keyword, score, weight))\n", "        \n", "        print(f\"FinBERT情感分析完成，整体得分: {overall_score:.4f}\")\n", "        \n", "        return overall_score, keyword_scores\n", "        \n", "    except Exception as e:\n", "        print(f\" FinBERT分析失败: {e}，使用备选方法\")\n", "        return sentiment_analysis_by_snownlp(text, keywords)\n", "\n", "def sentiment_analysis_by_snownlp(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    使用SnowNLP进行情感分析（备选方案）\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表)\n", "    \"\"\"\n", "    try:\n", "        from snownlp import SnowNLP\n", "        \n", "        # 分析整体文本\n", "        s = SnowNLP(text)\n", "        # SnowNLP返回[0,1]，转换为[-1,1]\n", "        overall_score = 2 * s.sentiments - 1\n", "        \n", "        # 分析关键词\n", "        keyword_scores = []\n", "        for keyword, weight in keywords:\n", "            s = SnowNLP(keyword)\n", "            score = 2 * s.sentiments - 1\n", "            keyword_scores.append((keyword, score, weight))\n", "        \n", "        print(f\"SnowNLP情感分析完成，整体得分: {overall_score:.4f}\")\n", "        return overall_score, keyword_scores\n", "        \n", "    except ImportError:\n", "        print(\"SnowNLP不可用，使用简单规则\")\n", "        # 简单的规则基础情感分析\n", "        positive_words = ['好', '优秀', '增长', '上涨', '盈利', '成功', '发展', '提升']\n", "        negative_words = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '下降']\n", "        \n", "        words = text.split()\n", "        pos_count = sum(1 for word in words if any(pw in word for pw in positive_words))\n", "        neg_count = sum(1 for word in words if any(nw in word for nw in negative_words))\n", "        \n", "        if len(words) > 0:\n", "            overall_score = (pos_count - neg_count) / len(words)\n", "        else:\n", "            overall_score = 0\n", "        \n", "        keyword_scores = [(keyword, 0, weight) for keyword, weight in keywords]\n", "        \n", "        return overall_score, keyword_scores\n", "\n", "def sentiment_analysis_by_tfidf_sentiment(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    基于TF-IDF和情感词典结合的情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "        sentiment_dict: 情感词典\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表)\n", "    \"\"\"\n", "    print(\"开始基于TF-IDF和情感词典的情感分析...\")\n", "    \n", "    try:\n", "        # 分词\n", "        words = jieba.lcut(text)\n", "        \n", "        # 计算TF-IDF权重\n", "        from sklearn.feature_extraction.text import TfidfVectorizer\n", "        \n", "        # 创建文档（将分词结果重新组合）\n", "        document = ' '.join(words)\n", "        vectorizer = TfidfVectorizer(max_features=1000)\n", "        tfidf_matrix = vectorizer.fit_transform([document])\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        tfidf_scores = tfidf_matrix.toarray()[0]\n", "        \n", "        # 创建词到TF-IDF得分的映射\n", "        word_tfidf = dict(zip(feature_names, tfidf_scores))\n", "        \n", "        # 计算加权情感得分\n", "        total_weighted_score = 0\n", "        total_weight = 0\n", "        \n", "        for word in words:\n", "            if word in sentiment_dict:\n", "                sentiment_score = sentiment_dict[word]\n", "                tfidf_weight = word_tfidf.get(word, 0.001)  # 默认小权重\n", "                \n", "                weighted_score = sentiment_score * tfidf_weight\n", "                total_weighted_score += weighted_score\n", "                total_weight += tfidf_weight\n", "        \n", "        # 计算整体得分\n", "        if total_weight > 0:\n", "            overall_score = total_weighted_score / total_weight\n", "        else:\n", "            overall_score = 0\n", "        \n", "        # 计算关键词情感得分\n", "        keyword_scores = []\n", "        for keyword, weight in keywords:\n", "            # 检查关键词是否在情感词典中\n", "            if keyword in sentiment_dict:\n", "                sentiment_score = sentiment_dict[keyword]\n", "            else:\n", "                # 检查关键词的组成部分\n", "                sentiment_score = 0\n", "                word_count = 0\n", "                for word in jieba.lcut(keyword):\n", "                    if word in sentiment_dict:\n", "                        sentiment_score += sentiment_dict[word]\n", "                        word_count += 1\n", "                \n", "                if word_count > 0:\n", "                    sentiment_score = sentiment_score / word_count\n", "            \n", "            # 获取TF-IDF权重\n", "            tfidf_weight = word_tfidf.get(keyword, weight * 0.1)  # 使用关键词权重作为备选\n", "            \n", "            # 结合TF-IDF权重调整情感得分\n", "            adjusted_score = sentiment_score * (1 + tfidf_weight)\n", "            \n", "            keyword_scores.append((keyword, adjusted_score, weight))\n", "        \n", "        print(f\"TF-IDF情感分析完成，整体得分: {overall_score:.4f}\")\n", "        return overall_score, keyword_scores\n", "        \n", "    except Exception as e:\n", "        print(f\"TF-IDF情感分析失败: {e}，使用简化方法\")\n", "        # 简化的备选方法\n", "        words = jieba.lcut(text)\n", "        sentiment_words = [w for w in words if w in sentiment_dict]\n", "        \n", "        if sentiment_words:\n", "            overall_score = sum(sentiment_dict[w] for w in sentiment_words) / len(sentiment_words)\n", "        else:\n", "            overall_score = 0\n", "        \n", "        keyword_scores = [(keyword, sentiment_dict.get(keyword, 0), weight) for keyword, weight in keywords]\n", "        \n", "        return overall_score, keyword_scores\n", "\n", "def compare_sentiment_results(dict_score: float, finbert_score: float, \n", "                            dict_keywords: List[Tuple[str, float, float]], \n", "                            finbert_keywords: List[Tuple[str, float, float]]) -> Tuple[pd.DataFrame, float]:\n", "    \"\"\"\n", "    比较两种情感分析方法的结果\n", "    \n", "    参数:\n", "        dict_score: 词典方法整体得分\n", "        finbert_score: FinBERT方法整体得分\n", "        dict_keywords: 词典方法关键词得分\n", "        finbert_keywords: FinBERT方法关键词得分\n", "    \n", "    返回:\n", "        (比较结果DataF<PERSON><PERSON>, 一致率)\n", "    \"\"\"\n", "    print(\"开始比较两种情感分析方法的结果...\")\n", "    \n", "    # 创建关键词映射\n", "    dict_map = {kw: (score, weight) for kw, score, weight in dict_keywords}\n", "    finbert_map = {kw: (score, weight) for kw, score, weight in finbert_keywords}\n", "    \n", "    # 获取所有关键词\n", "    all_keywords = set(dict_map.keys()) | set(finbert_map.keys())\n", "    \n", "    # 创建比较数据\n", "    comparison_data = []\n", "    \n", "    for keyword in all_keywords:\n", "        dict_score_kw, dict_weight = dict_map.get(keyword, (0, 0))\n", "        finbert_score_kw, finbert_weight = finbert_map.get(keyword, (0, 0))\n", "        \n", "        # 确定情感倾向\n", "        def get_sentiment(score):\n", "            if score > 0.1:\n", "                return '正面'\n", "            elif score < -0.1:\n", "                return '负面'\n", "            else:\n", "                return '中性'\n", "        \n", "        dict_sentiment = get_sentiment(dict_score_kw)\n", "        finbert_sentiment = get_sentiment(finbert_score_kw)\n", "        \n", "        comparison_data.append({\n", "            '关键词': keyword,\n", "            '词典情感得分': dict_score_kw,\n", "            '词典情感倾向': dict_sentiment,\n", "            'FinBERT情感得分': finbert_score_kw,\n", "            'FinBERT情感倾向': finbert_sentiment,\n", "            '权重': max(dict_weight, finbert_weight),\n", "            '得分差异': abs(dict_score_kw - finbert_score_kw),\n", "            '倾向一致': dict_sentiment == finbert_sentiment\n", "        })\n", "    \n", "    # 创建DataFrame\n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    \n", "    # 计算一致率\n", "    if len(comparison_df) > 0:\n", "        agreement_rate = comparison_df['倾向一致'].mean()\n", "    else:\n", "        agreement_rate = 0\n", "    \n", "    print(f\"情感分析比较完成，一致率: {agreement_rate:.2%}\")\n", "    \n", "    return comparison_df, agreement_rate\n", "\n", "print(\"FinBERT和比较分析模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 可视化模块"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["可视化模块已定义\n"]}], "source": ["# 定义颜色方案\n", "COLORS = {\n", "    'positive':'#2E8B57',  # 海绿色\n", "    'negative': '#DC143C',  # 深红色\n", "    'neutral': '#708090',   # 石板灰\n", "    'background': '#F8F9FA',\n", "    'highlight': '#4169E1',  # 皇家蓝\n", "    'secondary': '#FFD700'   # 金色\n", "}\n", "\n", "def create_sentiment_comparison_chart(dict_score: float, finbert_score: float) -> None:\n", "    \"\"\"\n", "    创建情感分析方法对比图表\n", "    \n", "    参数:\n", "        dict_score: 词典方法得分\n", "        finbert_score: FinBERT方法得分\n", "    \"\"\"\n", "    fig, ax = plt.subplots(figsize=(10, 6))\n", "    \n", "    methods = ['Dictionary Analysis', 'FinBERT Analysis']\n", "    scores = [dict_score, finbert_score]\n", "    \n", "    # 确定颜色\n", "    colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral'] for s in scores]\n", "    \n", "    # 创建条形图\n", "    bars = ax.bar(methods, scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1)\n", "    \n", "    # 添加数值标签\n", "    for bar, score in zip(bars, scores):\n", "        height = bar.get_height()\n", "        ax.text(bar.get_x() + bar.get_width()/2., height + (0.02 if height >= 0 else -0.05),\n", "                f'{score:.3f}', ha='center', va='bottom' if height >= 0 else 'top',\n", "                fontweight='bold', fontsize=12)\n", "    \n", "    # 添加零线\n", "    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title('Sentiment Analysis Methods Comparison', fontsize=16, fontweight='bold', pad=20)\n", "    ax.set_ylabel('Sentiment Score\\n(Negative < 0 < Positive)', fontsize=12, fontweight='bold')\n", "    \n", "    # 设置y轴范围\n", "    y_max = max(abs(min(scores)), abs(max(scores)), 0.5)\n", "    ax.set_ylim(-y_max*1.2, y_max*1.2)\n", "    \n", "    # 添加网格\n", "    ax.grid(axis='y', alpha=0.3, linestyle='--')\n", "    \n", "    # 添加解释文本\n", "    explanation = f\"Dictionary Score: {dict_score:.3f} ({'Positive' if dict_score > 0.1 else 'Negative' if dict_score < -0.1 else 'Neutral'})\\n\"\n", "    explanation += f\"FinBERT Score: {finbert_score:.3f} ({'Positive' if finbert_score > 0.1 else 'Negative' if finbert_score < -0.1 else 'Neutral'})\"\n", "    \n", "    plt.figtext(0.5, 0.02, explanation, ha='center', fontsize=11,\n", "                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_keyword_sentiment_chart(keyword_scores: List[Tuple[str, float, float]], method_name: str, top_n: int = 15) -> None:\n", "    \"\"\"\n", "    创建关键词情感分布图表\n", "    \n", "    参数:\n", "        keyword_scores: 关键词情感得分列表\n", "        method_name: 方法名称\n", "        top_n: 显示的关键词数量\n", "    \"\"\"\n", "    if not keyword_scores:\n", "        print(f\"No keyword sentiment data available for {method_name}\")\n", "        return\n", "    \n", "    # 按权重排序并选择前top_n个\n", "    sorted_keywords = sorted(keyword_scores, key=lambda x: x[2], reverse=True)[:top_n]\n", "    \n", "    keywords = [item[0] for item in sorted_keywords]\n", "    scores = [item[1] for item in sorted_keywords]\n", "    weights = [item[2] for item in sorted_keywords]\n", "    \n", "    # 按情感得分排序\n", "    sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i])\n", "    sorted_keywords = [keywords[i] for i in sorted_indices]\n", "    sorted_scores = [scores[i] for i in sorted_indices]\n", "    sorted_weights = [weights[i] for i in sorted_indices]\n", "    \n", "    # 创建图表\n", "    fig, ax = plt.subplots(figsize=(12, 8))\n", "    \n", "    # 确定颜色\n", "    colors = [COLORS['positive'] if s > 0.05 else COLORS['negative'] if s < -0.05 else COLORS['neutral'] for s in sorted_scores]\n", "    \n", "    # 创建水平条形图\n", "    bars = ax.barh(sorted_keywords, sorted_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "    \n", "    # 添加数值标签\n", "    for i, (bar, weight) in enumerate(zip(bars, sorted_weights)):\n", "        width = bar.get_width()\n", "        ax.text(width + (0.02 if width >= 0 else -0.02),\n", "                bar.get_y() + bar.get_height()/2,\n", "                f'{width:.3f}',\n", "                ha='left' if width >= 0 else 'right',\n", "                va='center', fontweight='bold', fontsize=9)\n", "    \n", "    # 添加零线\n", "    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(f'Keyword Sentiment Distribution ({method_name})', fontsize=14, fontweight='bold', pad=20)\n", "    ax.set_xlabel('Sentiment Score', fontsize=12, fontweight='bold')\n", "    \n", "    # 设置x轴范围\n", "    if sorted_scores:\n", "        x_max = max(abs(min(sorted_scores)), abs(max(sorted_scores)), 0.3)\n", "        ax.set_xlim(-x_max*1.2, x_max*1.2)\n", "    \n", "    # 添加网格\n", "    ax.grid(axis='x', alpha=0.3, linestyle='--')\n", "    \n", "    # 统计信息\n", "    pos_count = sum(1 for s in sorted_scores if s > 0.05)\n", "    neg_count = sum(1 for s in sorted_scores if s < -0.05)\n", "    neu_count = len(sorted_scores) - pos_count - neg_count\n", "    \n", "    stats_text = f\"Positive: {pos_count}, Negative: {neg_count}, Neutral: {neu_count}\"\n", "    plt.figtext(0.02, 0.02, stats_text, fontsize=10,\n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightblue', alpha=0.7))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_agreement_chart(comparison_df: pd.DataFrame) -> None:\n", "    \"\"\"\n", "    创建两种方法一致性分析图表\n", "    \n", "    参数:\n", "        comparison_df: 比较结果DataFrame\n", "    \"\"\"\n", "    if comparison_df.empty:\n", "        print(\"No comparison data available\")\n", "        return\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 1. 一致性饼图\n", "    agreement_counts = comparison_df['倾向一致'].value_counts()\n", "    labels = ['Consistent', 'Inconsistent']\n", "    sizes = [agreement_counts.get(True, 0), agreement_counts.get(False, 0)]\n", "    colors = [COLORS['highlight'], COLORS['neutral']]\n", "    \n", "    wedges, texts, autotexts = ax1.pie(sizes, labels=labels, autopct='%1.1f%%',\n", "                                      colors=colors, startangle=90, explode=(0.05, 0))\n", "    \n", "    for autotext in autotexts:\n", "        autotext.set_fontweight('bold')\n", "    \n", "    ax1.set_title('Sentiment Analysis Agreement', fontsize=14, fontweight='bold')\n", "    \n", "    # 2. 情感分布散点图\n", "    dict_scores = comparison_df['词典情感得分'].values\n", "    finbert_scores = comparison_df['FinBERT情感得分'].values\n", "    \n", "    # 根据一致性着色\n", "    colors_scatter = [COLORS['highlight'] if agree else COLORS['secondary'] \n", "                     for agree in comparison_df['倾向一致']]\n", "    \n", "    ax2.scatter(dict_scores, finbert_scores, c=colors_scatter, alpha=0.7, s=50)\n", "    \n", "    # 添加对角线\n", "    lims = [min(ax2.get_xlim()[0], ax2.get_ylim()[0]),\n", "            max(ax2.get_xlim()[1], ax2.get_ylim()[1])]\n", "    ax2.plot(lims, lims, 'k--', alpha=0.5, zorder=0)\n", "    \n", "    # 添加象限线\n", "    ax2.axhline(y=0, color='gray', linestyle='-', alpha=0.3)\n", "    ax2.axvline(x=0, color='gray', linestyle='-', alpha=0.3)\n", "    \n", "    ax2.set_xlabel('Dictionary Sentiment Score', fontweight='bold')\n", "    ax2.set_ylabel('FinBERT Sentiment Score', fontweight='bold')\n", "    ax2.set_title('Sentiment Score Correlation', fontsize=14, fontweight='bold')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 计算相关系数\n", "    if len(dict_scores) > 1:\n", "        correlation = np.corrcoef(dict_scores, finbert_scores)[0, 1]\n", "        ax2.text(0.05, 0.95, f'Correlation: {correlation:.3f}', \n", "                transform=ax2.transAxes, fontsize=11,\n", "                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_wordcloud(keywords: List[Tuple[str, float]]) -> None:\n", "    \"\"\"\n", "    创建关键词云图，使用系统默认字体\n", "    \n", "    参数:\n", "        keywords: 关键词列表\n", "    \"\"\"\n", "    if not keywords:\n", "        print(\"没有关键词可用于生成词云\")\n", "        return\n", "    \n", "    try:\n", "        # 准备词频字典\n", "        word_freq = {word: weight for word, weight in keywords}\n", "        \n", "        # 创建词云（使用系统默认字体）\n", "        wordcloud = WordCloud(\n", "            width=800, height=400,\n", "            background_color='white',\n", "            max_words=50,\n", "            colormap='viridis',\n", "            font_path=SYSTEM_FONT_PATH,  # 使用系统字体\n", "            prefer_horizontal=0.9,\n", "            min_font_size=10,\n", "            max_font_size=100,\n", "            relative_scaling=0.5,\n", "            random_state=42\n", "        ).generate_from_frequencies(word_freq)\n", "        \n", "        # 显示词云\n", "        plt.figure(figsize=(12, 6))\n", "        plt.imshow(wordcloud, interpolation='bilinear')\n", "        plt.axis('off')\n", "        plt.title('Keywords Word Cloud', fontsize=16, fontweight='bold', pad=20)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "    except Exception as e:\n", "        print(f\"词云生成失败: {e}\")\n", "        # 创建简单的条形图作为替代\n", "        try:\n", "            top_words = keywords[:20]\n", "            words = [item[0] for item in top_words]\n", "            weights = [item[1] for item in top_words]\n", "            \n", "            plt.figure(figsize=(12, 8))\n", "            bars = plt.barh(words, weights, color='steelblue', alpha=0.7)\n", "            plt.xlabel('Weight')\n", "            plt.title('Top Keywords (Alternative to Word Cloud)')\n", "            plt.gca().invert_yaxis()  # 最高权重在顶部\n", "            \n", "            # 添加数值标签\n", "            for bar in bars:\n", "                width = bar.get_width()\n", "                plt.text(width + 0.001, bar.get_y() + bar.get_height()/2,\n", "                        f'{width:.3f}', ha='left', va='center')\n", "            \n", "            plt.tight_layout()\n", "            plt.show()\n", "        except Exception as backup_error:\n", "            print(f\"备用图表也失败: {backup_error}\")\n", "\n", "def create_sentiment_keywords_chart(positive_keywords: List[Tuple[str, float, float]], \n", "                                   negative_keywords: List[Tuple[str, float, float]], \n", "                                   top_n: int = 10) -> None:\n", "    \"\"\"\n", "    创建情感关键词分布图表\n", "    \n", "    参数:\n", "        positive_keywords: 正面情感关键词列表\n", "        negative_keywords: 负面情感关键词列表\n", "        top_n: 每种情感显示的关键词数量\n", "    \"\"\"\n", "    if not positive_keywords and not negative_keywords:\n", "        print(\"No sentiment keywords available for visualization\")\n", "        return\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))\n", "    \n", "    # 正面情感关键词\n", "    if positive_keywords:\n", "        top_positive = positive_keywords[:top_n]\n", "        pos_words = [item[0] for item in top_positive]\n", "        pos_scores = [item[1] * item[2] for item in top_positive]  # 情感强度 × 词频权重\n", "        \n", "        bars1 = ax1.barh(pos_words, pos_scores, color=COLORS['positive'], alpha=0.8)\n", "        ax1.set_title('Top Positive Sentiment Keywords', fontsize=14, fontweight='bold')\n", "        ax1.set_xlabel('Sentiment Strength × Frequency Weight', fontweight='bold')\n", "        \n", "        # 添加数值标签\n", "        for bar, score in zip(bars1, pos_scores):\n", "            width = bar.get_width()\n", "            ax1.text(width + width*0.01, bar.get_y() + bar.get_height()/2,\n", "                    f'{score:.4f}', ha='left', va='center', fontweight='bold', fontsize=9)\n", "        \n", "        ax1.grid(axis='x', alpha=0.3)\n", "    else:\n", "        ax1.text(0.5, 0.5, 'No Positive Keywords Found', ha='center', va='center', \n", "                transform=ax1.transAxes, fontsize=12, style='italic')\n", "        ax1.set_title('Top Positive Sentiment Keywords', fontsize=14, fontweight='bold')\n", "    \n", "    # 负面情感关键词\n", "    if negative_keywords:\n", "        top_negative = negative_keywords[:top_n]\n", "        neg_words = [item[0] for item in top_negative]\n", "        neg_scores = [item[1] * item[2] for item in top_negative]  # 情感强度 × 词频权重\n", "        \n", "        bars2 = ax2.barh(neg_words, neg_scores, color=COLORS['negative'], alpha=0.8)\n", "        ax2.set_title('Top Negative Sentiment Keywords', fontsize=14, fontweight='bold')\n", "        ax2.set_xlabel('Sentiment Strength × Frequency Weight', fontweight='bold')\n", "        \n", "        # 添加数值标签\n", "        for bar, score in zip(bars2, neg_scores):\n", "            width = bar.get_width()\n", "            ax2.text(width + width*0.01, bar.get_y() + bar.get_height()/2,\n", "                    f'{score:.4f}', ha='left', va='center', fontweight='bold', fontsize=9)\n", "        \n", "        ax2.grid(axis='x', alpha=0.3)\n", "    else:\n", "        ax2.text(0.5, 0.5, 'No Negative Keywords Found', ha='center', va='center', \n", "                transform=ax2.transAxes, fontsize=12, style='italic')\n", "        ax2.set_title('Top Negative Sentiment Keywords', fontsize=14, fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_contextual_sentiment_chart(contextual_keywords: List[Tuple[str, float, List[str]]], \n", "                                     top_n: int = 15) -> None:\n", "    \"\"\"\n", "    创建上下文情感关键词图表\n", "    \n", "    参数:\n", "        contextual_keywords: 上下文情感关键词列表\n", "        top_n: 显示的关键词数量\n", "    \"\"\"\n", "    if not contextual_keywords:\n", "        print(\"No contextual sentiment keywords available for visualization\")\n", "        return\n", "    \n", "    # 取前top_n个关键词\n", "    top_keywords = contextual_keywords[:top_n]\n", "    \n", "    keywords = [item[0] for item in top_keywords]\n", "    scores = [item[1] for item in top_keywords]\n", "    sentiment_words_lists = [item[2] for item in top_keywords]\n", "    \n", "    # 按情感得分排序\n", "    sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i])\n", "    sorted_keywords = [keywords[i] for i in sorted_indices]\n", "    sorted_scores = [scores[i] for i in sorted_indices]\n", "    sorted_sentiment_words = [sentiment_words_lists[i] for i in sorted_indices]\n", "    \n", "    # 创建图表\n", "    fig, ax = plt.subplots(figsize=(14, 10))\n", "    \n", "    # 确定颜色\n", "    colors = [COLORS['positive'] if s > 0.02 else COLORS['negative'] if s < -0.02 else COLORS['neutral'] for s in sorted_scores]\n", "    \n", "    # 创建水平条形图\n", "    bars = ax.barh(sorted_keywords, sorted_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "    \n", "    # 添加数值标签和情感词信息\n", "    for i, (bar, sentiment_words) in enumerate(zip(bars, sorted_sentiment_words)):\n", "        width = bar.get_width()\n", "        \n", "        # 添加得分标签\n", "        ax.text(width + (0.005 if width >= 0 else -0.005),\n", "                bar.get_y() + bar.get_height()/2,\n", "                f'{width:.3f}',\n", "                ha='left' if width >= 0 else 'right',\n", "                va='center', fontweight='bold', fontsize=9)\n", "        \n", "        # 添加情感词信息（显示前3个）\n", "        if sentiment_words:\n", "            sentiment_text = ', '.join(sentiment_words[:3])\n", "            if len(sentiment_words) > 3:\n", "                sentiment_text += '...'\n", "            \n", "            ax.text(-0.01 if width >= 0 else 0.01,\n", "                    bar.get_y() + bar.get_height()/2,\n", "                    f'({sentiment_text})',\n", "                    ha='right' if width >= 0 else 'left',\n", "                    va='center', fontsize=8, style='italic', alpha=0.7)\n", "    \n", "    # 添加零线\n", "    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title('Keywords with Contextual Sentiment Analysis', fontsize=16, fontweight='bold', pad=20)\n", "    ax.set_xlabel('Contextual Sentiment Score\\n(Based on surrounding sentiment words)', fontsize=12, fontweight='bold')\n", "    \n", "    # 添加网格\n", "    ax.grid(axis='x', alpha=0.3, linestyle='--')\n", "    \n", "    # 添加说明\n", "    explanation = \"Note: Scores based on sentiment words in ±10 word context window.\\nParentheses show example sentiment words found near each keyword.\"\n", "    plt.figtext(0.02, 0.02, explanation, fontsize=10, style='italic',\n", "                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.3))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_sentiment_summary_chart(positive_keywords: List[Tuple[str, float, float]], \n", "                                  negative_keywords: List[Tuple[str, float, float]]) -> None:\n", "    \"\"\"\n", "    创建情感关键词总结图表\n", "    \n", "    参数:\n", "        positive_keywords: 正面情感关键词列表\n", "        negative_keywords: 负面情感关键词列表\n", "    \"\"\"\n", "    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))\n", "    \n", "    # 1. 情感关键词数量对比\n", "    counts = [len(positive_keywords), len(negative_keywords)]\n", "    labels = ['Positive Keywords', 'Negative Keywords']\n", "    colors = [COLORS['positive'], COLORS['negative']]\n", "    \n", "    bars1 = ax1.bar(labels, counts, color=colors, alpha=0.8)\n", "    ax1.set_title('Sentiment Keywords Count', fontweight='bold')\n", "    ax1.set_ylabel('Number of Keywords')\n", "    \n", "    for bar, count in zip(bars1, counts):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "                f'{count}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 2. 情感强度分布\n", "    if positive_keywords:\n", "        pos_strengths = [item[1] for item in positive_keywords]\n", "        ax2.hist(pos_strengths, bins=10, color=COLORS['positive'], alpha=0.7, label='Positive')\n", "    \n", "    if negative_keywords:\n", "        neg_strengths = [item[1] for item in negative_keywords]\n", "        ax2.hist(neg_strengths, bins=10, color=COLORS['negative'], alpha=0.7, label='Negative')\n", "    \n", "    ax2.set_title('Sentiment Strength Distribution', fontweight='bold')\n", "    ax2.set_xlabel('Sentiment Strength')\n", "    ax2.set_ylabel('Frequency')\n", "    ax2.legend()\n", "    ax2.grid(alpha=0.3)\n", "    \n", "    # 3. 词频权重分布\n", "    if positive_keywords:\n", "        pos_freqs = [item[2] for item in positive_keywords]\n", "        ax3.scatter(range(len(pos_freqs)), pos_freqs, color=COLORS['positive'], alpha=0.7, label='Positive')\n", "    \n", "    if negative_keywords:\n", "        neg_freqs = [item[2] for item in negative_keywords]\n", "        ax3.scatter(range(len(neg_freqs)), neg_freqs, color=COLORS['negative'], alpha=0.7, label='Negative')\n", "    \n", "    ax3.set_title('Frequency Weight Distribution', fontweight='bold')\n", "    ax3.set_xlabel('Keyword Index')\n", "    ax3.set_ylabel('Frequency Weight')\n", "    ax3.legend()\n", "    ax3.grid(alpha=0.3)\n", "    \n", "    # 4. 综合得分（强度×频率）分布\n", "    if positive_keywords:\n", "        pos_combined = [item[1] * item[2] for item in positive_keywords]\n", "        ax4.bar(range(len(pos_combined)), pos_combined, color=COLORS['positive'], alpha=0.7, label='Positive')\n", "    \n", "    if negative_keywords:\n", "        neg_combined = [item[1] * item[2] for item in negative_keywords]\n", "        neg_start = len(positive_keywords) if positive_keywords else 0\n", "        ax4.bar(range(neg_start, neg_start + len(neg_combined)), neg_combined, \n", "                color=COLORS['negative'], alpha=0.7, label='Negative')\n", "    \n", "    ax4.set_title('Combined Score (Strength × Frequency)', fontweight='bold')\n", "    ax4.set_xlabel('Keyword Index')\n", "    ax4.set_ylabel('Combined Score')\n", "    ax4.legend()\n", "    ax4.grid(alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(\"可视化模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 主流程整合"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 主流程模块已定义\n"]}], "source": ["def analyze_financial_report(pdf_path: str, \n", "                           positive_dict_path: str = None, \n", "                           negative_dict_path: str = None,\n", "                           stopwords_path: str = None,\n", "                           num_keywords: int = 20) -> Dict:\n", "    \"\"\"\n", "    完整的研报情感分析流程\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "        positive_dict_path: 正面词典路径\n", "        negative_dict_path: 负面词典路径\n", "        stopwords_path: 停用词文件路径\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        分析结果字典\n", "    \"\"\"\n", "    print(\"开始研报情感分析流程\")\n", "    print(\"=\" * 60)\n", "    \n", "    results = {\n", "        'pdf_path': pdf_path,\n", "        'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),\n", "        'success': <PERSON><PERSON><PERSON>,\n", "        'error': None\n", "    }\n", "    \n", "    try:\n", "        # 步骤1: PDF文本提取\n", "        print(\"\\n 步骤1: PDF文本和表格提取\")\n", "        text, tables = extract_text_and_tables_from_pdf(pdf_path)\n", "        \n", "        if not text or len(text.strip()) < 100:\n", "            raise ValueError(\"PDF文本提取失败或内容过少\")\n", "        \n", "        results['text_length'] = len(text)\n", "        results['tables_count'] = len(tables)\n", "        results['text_sample'] = text[:500] + \"...\" if len(text) > 500 else text\n", "        \n", "        print(f\"文本提取完成: {len(text)} 字符, {len(tables)} 个表格\")\n", "        \n", "        # 步骤2: 文本预处理\n", "        print(\"\\n 步骤2: 文本预处理\")\n", "        if not stopwords_path:\n", "            stopwords_path = os.path.join('data', 'stopwords.txt')\n", "        \n", "        filtered_words, filtered_text = preprocess_text(text, stopwords_path)\n", "        \n", "        if not filtered_words:\n", "            raise ValueError(\"文本预处理失败\")\n", "        \n", "        results['filtered_words_count'] = len(filtered_words)\n", "        results['filtered_text_length'] = len(filtered_text)\n", "        \n", "        # 步骤3: 关键词提取\n", "        print(\"\\n  步骤3: 关键词提取\")\n", "        keywords = extract_keywords(filtered_text, num_keywords)\n", "        \n", "        if not keywords:\n", "            raise ValueError(\"关键词提取失败\")\n", "        \n", "        results['keywords'] = keywords\n", "        results['keywords_count'] = len(keywords)\n", "        \n", "        # 提取关键词代表性句子\n", "        keyword_sentences = extract_keywords_with_sentences(text, keywords)\n", "        results['keyword_sentences'] = keyword_sentences\n", "        \n", "        # 步骤4: 情感分析\n", "        print(\"\\n 步骤4: 情感分析\")\n", "        \n", "        # 4.1 词典方法\n", "        print(\"\\n 4.1 基于词典的情感分析\")\n", "        if not positive_dict_path:\n", "            positive_dict_path = os.path.join('data', 'CFSD中文金融情感词典', '正面词典.csv')\n", "        if not negative_dict_path:\n", "            negative_dict_path = os.path.join('data', 'CFSD中文金融情感词典', '负面词典.csv')\n", "        \n", "        sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)\n", "        dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(\n", "            filtered_text, keywords, sentiment_dict\n", "        )\n", "        \n", "        results['dict_analysis'] = {\n", "            'overall_score': dict_score,\n", "            'sentiment': '正面' if dict_score > 0.1 else '负面' if dict_score < -0.1 else '中性',\n", "            'keyword_scores': dict_keywords,\n", "            'matched_words': matched_words,\n", "            'matched_words_count': len(matched_words)\n", "        }\n", "        \n", "        # 4.2 FinBERT方法\n", "        print(\"\\n 4.2 基于FinBERT的情感分析\")\n", "        finbert_score, finbert_keywords = sentiment_analysis_by_finbert(\n", "            filtered_text, keywords\n", "        )\n", "        \n", "        results['finbert_analysis'] = {\n", "            'overall_score': finbert_score,\n", "            'sentiment': '正面' if finbert_score > 0.1 else '负面' if finbert_score < -0.1 else '中性',\n", "            'keyword_scores': finbert_keywords\n", "        }\n", "        \n", "        # 4.3 结果比较\n", "        print(\"\\n 4.3 两种方法结果比较\")\n", "        comparison_df, agreement_rate = compare_sentiment_results(\n", "            dict_score, finbert_score, dict_keywords, finbert_keywords\n", "        )\n", "        \n", "        results['comparison'] = {\n", "            'agreement_rate': agreement_rate,\n", "            'comparison_df': comparison_df,\n", "            'combined_score': (dict_score + finbert_score) / 2,\n", "            'score_difference': abs(dict_score - finbert_score)\n", "        }\n", "        \n", "        # 步骤5: 情感关键词提取\n", "        print(\"\\n 步骤5: 情感关键词提取\")\n", "        \n", "        # 5.1 提取有情感意义的关键词\n", "        print(\"\\n 5.1 提取有情感意义的关键词\")\n", "        positive_keywords, negative_keywords = extract_sentiment_keywords(\n", "            filtered_text, sentiment_dict, num_keywords=15\n", "        )\n", "        \n", "        results['sentiment_keywords'] = {\n", "            'positive_keywords': positive_keywords,\n", "            'negative_keywords': negative_keywords,\n", "            'positive_count': len(positive_keywords),\n", "            'negative_count': len(negative_keywords)\n", "        }\n", "        \n", "        # 5.2 基于上下文的情感关键词分析\n", "        print(\"\\n 5.2 基于上下文的情感关键词分析\")\n", "        contextual_keywords = extract_contextual_sentiment_keywords(\n", "            filtered_text, keywords, window_size=10\n", "        )\n", "        \n", "        results['contextual_sentiment_keywords'] = contextual_keywords\n", "        \n", "        # 综合评估\n", "        combined_score = results['comparison']['combined_score']\n", "        results['final_assessment'] = {\n", "            'combined_score': combined_score,\n", "            'combined_sentiment': '正面' if combined_score > 0.1 else '负面' if combined_score < -0.1 else '中性',\n", "            'confidence': 'high' if agreement_rate > 0.7 else 'medium' if agreement_rate > 0.4 else 'low',\n", "            'agreement_rate': agreement_rate,\n", "            'sentiment_keyword_balance': len(positive_keywords) / (len(positive_keywords) + len(negative_keywords)) if (positive_keywords or negative_keywords) else 0.5\n", "        }\n", "        \n", "        results['success'] = True\n", "        print(\"\\n 研报情感分析完成!\")\n", "        \n", "        return results\n", "        \n", "    except Exception as e:\n", "        error_msg = f\"分析过程中出错: {str(e)}\"\n", "        print(f\"\\n {error_msg}\")\n", "        results['error'] = error_msg\n", "        return results\n", "\n", "def display_analysis_results(results: Dict) -> None:\n", "    \"\"\"\n", "    显示分析结果摘要\n", "    \n", "    参数:\n", "        results: 分析结果字典\n", "    \"\"\"\n", "    if not results['success']:\n", "        print(f\" 分析失败: {results.get('error', '未知错误')}\")\n", "        return\n", "    \n", "    print(\"\\n\" + \"=\" * 60)\n", "    print(\" 研报情感分析结果摘要\")\n", "    print(\"=\" * 60)\n", "    \n", "    # 基本信息\n", "    print(f\"\\n 文档信息:\")\n", "    print(f\"  • 文件: {os.path.basename(results['pdf_path'])}\")\n", "    print(f\"  • 分析时间: {results['timestamp']}\")\n", "    print(f\"  • 原始文本长度: {results['text_length']:,} 字符\")\n", "    print(f\"  • 提取表格数量: {results['tables_count']} 个\")\n", "    print(f\"  • 有效词汇数量: {results['filtered_words_count']:,} 个\")\n", "    print(f\"  • 关键词数量: {results['keywords_count']} 个\")\n", "    \n", "    # 关键词展示\n", "    print(f\"\\n 前10个关键词:\")\n", "    for i, (word, weight) in enumerate(results['keywords'][:10], 1):\n", "        print(f\"  {i:2d}. {word:<12} (权重: {weight:.4f})\")\n", "    \n", "    # 情感分析结果\n", "    dict_analysis = results['dict_analysis']\n", "    finbert_analysis = results['finbert_analysis']\n", "    final_assessment = results['final_assessment']\n", "    \n", "    print(f\"\\n 情感分析结果:\")\n", "    print(f\"  • 词典方法:\")\n", "    print(f\"    - 情感得分: {dict_analysis['overall_score']:+.4f}\")\n", "    print(f\"    - 情感倾向: {dict_analysis['sentiment']}\")\n", "    print(f\"    - 匹配情感词: {dict_analysis['matched_words_count']} 个\")\n", "    \n", "    print(f\"  • FinBERT方法:\")\n", "    print(f\"    - 情感得分: {finbert_analysis['overall_score']:+.4f}\")\n", "    print(f\"    - 情感倾向: {finbert_analysis['sentiment']}\")\n", "    \n", "    print(f\"  • 综合评估:\")\n", "    print(f\"    - 综合得分: {final_assessment['combined_score']:+.4f}\")\n", "    print(f\"    - 综合倾向: {final_assessment['combined_sentiment']}\")\n", "    print(f\"    - 方法一致率: {final_assessment['agreement_rate']:.1%}\")\n", "    print(f\"    - 结果可信度: {final_assessment['confidence']}\")\n", "    \n", "    # 情感解读\n", "    print(f\"\\n 结果解读:\")\n", "    combined_score = final_assessment['combined_score']\n", "    agreement_rate = final_assessment['agreement_rate']\n", "    \n", "    if combined_score > 0.2:\n", "        sentiment_desc = \"研报整体呈现积极正面的情感倾向\"\n", "    elif combined_score < -0.2:\n", "        sentiment_desc = \"研报整体呈现消极负面的情感倾向\"\n", "    else:\n", "        sentiment_desc = \"研报整体情感倾向相对中性\"\n", "    \n", "    if agreement_rate > 0.7:\n", "        confidence_desc = \"两种分析方法高度一致，结果可信度很高\"\n", "    elif agreement_rate > 0.4:\n", "        confidence_desc = \"两种分析方法基本一致，结果具有一定可信度\"\n", "    else:\n", "        confidence_desc = \"两种分析方法存在较大分歧，建议进一步人工审核\"\n", "    \n", "    print(f\"  • {sentiment_desc}\")\n", "    print(f\"  • {confidence_desc}\")\n", "    \n", "    print(\"\\n\" + \"=\" * 60)\n", "\n", "print(\" 主流程模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 示例运行和结果展示"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.1 设置文件路径\n", "\n", "请根据您的实际文件路径修改以下设置："]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 检查文件存在性:\n", "  if exists else PDF文件: data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n", "  if exists else 正面词典: data/CFSD中文金融情感词典/正面词典.csv\n", "  if exists else 负面词典: data/CFSD中文金融情感词典/负面词典.csv\n", "  if exists else 停用词文件: data/stopwords.txt\n", "\n", " 将使用以下文件进行分析:\n", "   PDF文件: data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n", "   正面词典: data/CFSD中文金融情感词典/正面词典.csv\n", "   负面词典: data/CFSD中文金融情感词典/负面词典.csv\n", "   停用词: data/stopwords.txt\n"]}], "source": ["# 设置文件路径 - 请根据实际情况修改\n", "PDF_PATH = \"data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\"\n", "POSITIVE_DICT_PATH = \"data/CFSD中文金融情感词典/正面词典.csv\"\n", "NEGATIVE_DICT_PATH = \"data/CFSD中文金融情感词典/负面词典.csv\"\n", "STOPWORDS_PATH = \"data/stopwords.txt\"\n", "\n", "# 检查文件是否存在\n", "files_to_check = {\n", "    \"PDF文件\": PDF_PATH,\n", "    \"正面词典\": POSITIVE_DICT_PATH,\n", "    \"负面词典\": NEGATIVE_DICT_PATH,\n", "    \"停用词文件\": STOPWORDS_PATH\n", "}\n", "\n", "print(\" 检查文件存在性:\")\n", "for name, path in files_to_check.items():\n", "    exists = os.path.exists(path)\n", "    status = \"if exists else\"\n", "    print(f\"  {status} {name}: {path}\")\n", "    if not exists:\n", "        print(f\"      请确保文件存在或修改路径\")\n", "\n", "# 如果PDF文件不存在，尝试查找其他PDF文件\n", "if not os.path.exists(PDF_PATH):\n", "    print(\"\\n🔍 尝试查找其他PDF文件...\")\n", "    data_dir = \"data\"\n", "    if os.path.exists(data_dir):\n", "        pdf_files = [f for f in os.listdir(data_dir) if f.endswith('.pdf')]\n", "        if pdf_files:\n", "            PDF_PATH = os.path.join(data_dir, pdf_files[0])\n", "            print(f\"找到PDF文件: {PDF_PATH}\")\n", "        else:\n", "            print(\" 未找到任何PDF文件\")\n", "    else:\n", "        print(\" data目录不存在\")\n", "\n", "print(f\"\\n 将使用以下文件进行分析:\")\n", "print(f\"   PDF文件: {PDF_PATH}\")\n", "print(f\"   正面词典: {POSITIVE_DICT_PATH}\")\n", "print(f\"   负面词典: {NEGATIVE_DICT_PATH}\")\n", "print(f\"   停用词: {STOPWORDS_PATH}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.2 执行完整分析流程"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 开始执行研报情感分析...\n", "开始研报情感分析流程\n", "============================================================\n", "\n", " 步骤1: PDF文本和表格提取\n", "开始提取PDF文件: 2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n"]}, {"name": "stdout", "output_type": "stream", "text": ["使用PyMuPDF提取的文本 (长度: 39084 字符)\n", "📊 提取到 101 个表格\n", "文本提取完成: 39084 字符, 101 个表格\n", "\n", " 步骤2: 文本预处理\n", "开始文本预处理...\n", "文本清洗完成，长度: 37024 字符\n", "从文件加载了 1317 个停用词\n", "开始分词...\n", "分词完成，共 17998 个词\n", "文本预处理完成，过滤后共 8715 个有效词\n", "\n", "  步骤3: 关键词提取\n", "开始提取关键词...\n", "TextRank4zh提取失败: module 'networkx' has no attribute 'from_numpy_matrix'\n", "jieba TextRank提取到 20 个关键词\n", "TF-IDF提取到 20 个关键词\n", "关键词提取完成，共 20 个关键词\n", "\n", " 步骤4: 情感分析\n", "\n", " 4.1 基于词典的情感分析\n", "使用 gbk 编码加载正面词典: data/CFSD中文金融情感词典/正面词典.csv\n", "使用 gbk 编码加载负面词典: data/CFSD中文金融情感词典/负面词典.csv\n", "情感词典加载完成，共 2609 个词\n", "开始基于词典的情感分析...\n", "词典情感分析完成，整体得分: 0.0274，匹配 648 个情感词\n", "\n", " 4.2 基于FinBERT的情感分析\n", "transformers不可用，使用简化的情感分析\n", "SnowNLP情感分析完成，整体得分: 1.0000\n", "\n", " 4.3 两种方法结果比较\n", "开始比较两种情感分析方法的结果...\n", "情感分析比较完成，一致率: 30.00%\n", "\n", " 步骤5: 情感关键词提取\n", "\n", " 5.1 提取有情感意义的关键词\n", "开始提取有情感意义的关键词...\n", "情感关键词提取完成\n", "  • 正面情感关键词: 15 个\n", "  • 负面情感关键词: 15 个\n", "\n", " 5.2 基于上下文的情感关键词分析\n", " 开始基于上下文提取情感关键词...\n", "上下文情感关键词提取完成，共 20 个\n", "\n", " 研报情感分析完成!\n", "\n", "============================================================\n", " 研报情感分析结果摘要\n", "============================================================\n", "\n", " 文档信息:\n", "  • 文件: 2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n", "  • 分析时间: 2025-05-30 12:11:43\n", "  • 原始文本长度: 39,084 字符\n", "  • 提取表格数量: 101 个\n", "  • 有效词汇数量: 8,715 个\n", "  • 关键词数量: 20 个\n", "\n", " 前10个关键词:\n", "   1. 证券           (权重: 0.3959)\n", "   2. 服务           (权重: 0.3534)\n", "   3. 财富           (权重: 0.2977)\n", "   4. 金融           (权重: 0.2840)\n", "   5. 图表           (权重: 0.2409)\n", "   6. 业务           (权重: 0.2405)\n", "   7. 用户           (权重: 0.2218)\n", "   8. 产品           (权重: 0.2052)\n", "   9. 国金           (权重: 0.1938)\n", "  10. 指南针          (权重: 0.1440)\n", "\n", " 情感分析结果:\n", "  • 词典方法:\n", "    - 情感得分: +0.0274\n", "    - 情感倾向: 中性\n", "    - 匹配情感词: 648 个\n", "  • FinBERT方法:\n", "    - 情感得分: +1.0000\n", "    - 情感倾向: 正面\n", "  • 综合评估:\n", "    - 综合得分: +0.5137\n", "    - 综合倾向: 正面\n", "    - 方法一致率: 30.0%\n", "    - 结果可信度: low\n", "\n", " 结果解读:\n", "  • 研报整体呈现积极正面的情感倾向\n", "  • 两种分析方法存在较大分歧，建议进一步人工审核\n", "\n", "============================================================\n"]}], "source": ["# 执行完整的研报情感分析\n", "if os.path.exists(PDF_PATH):\n", "    print(\" 开始执行研报情感分析...\")\n", "    \n", "    # 执行分析\n", "    analysis_results = analyze_financial_report(\n", "        pdf_path=PDF_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        num_keywords=20\n", "    )\n", "    \n", "    # 显示结果摘要\n", "    display_analysis_results(analysis_results)\n", "    \n", "else:\n", "    print(\" PDF文件不存在，无法执行分析\")\n", "    print(\"请确保PDF文件路径正确，或将PDF文件放在data目录下\")\n", "    analysis_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.3 详细结果展示"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" 详细分析结果:\n", "================================================================================\n", "\n", " 提取的文本样本 (前500字符):\n", "--------------------------------------------------\n", "  \n", " \n", "敬请参阅最后一页特别声明 \n", "1 \n", " \n", " \n", " \n", " \n", "  国金证券研究所分析师：夏昌盛（执业S1130524020003）xiachangsheng＠gjzq.com.cn分析师：舒思勤（执业S1130524040001）shusiqin＠gjzq.com.cn分析师：杨佳妮（执业S1130524040002）yangjiani＠gjzq.com.cn联系人：方丽fangli＠gjzq.com.cn\n", "  金融IT商业模式、产品和客户全方位对比\n", "核心内容 \n", "金融IT 公司依托核心资源禀赋探索差异化发展路径，构建起多维竞争优势。具体而言，东方财富凭借互联网流量入\n", "口优势与牌照布局，构筑综合金融服务平台生态；同花顺以人工智能技术研发为核心驱动力，并通过广告业务打开收\n", "入增量空间；财富趋势持续深耕机构端交易系统研发，在量化交易等领域形成技术护城河；九方智投专注C 端投顾业\n", "务精细化运营；指南针则通过战略收购麦高证券获取证券牌照，打造金融科技赋能的特色券商模式。整体来看，行业\n", "已形成技术研发能力、用户流量优势与金融牌照资质的竞争壁垒体系，头部机构通过差异化战略实现错位竞争。 \n", "金融IT 行...\n", "\n", " 关键词及其代表性句子:\n", "--------------------------------------------------\n", "\n", " 1. 关键词: 证券 (权重: 0.3959)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "\n", " 2. 关键词: 服务 (权重: 0.3534)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "\n", " 3. 关键词: 财富 (权重: 0.2977)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "\n", " 4. 关键词: 金融 (权重: 0.2840)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "\n", " 5. 关键词: 图表 (权重: 0.2409)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "\n", " 6. 关键词: 业务 (权重: 0.2405)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 0.820.85\n", "0.93 1.711.791.69\n", "1.95\n", "2.26\n", "2.73\n", "3.263.21\n", "4.35\n", "3.89\n", "-20.00%\n", "0.00%\n", "20.00%\n", "40.00%\n", "60.00%\n", "80.0...\n", "\n", " 7. 关键词: 用户 (权重: 0.2218)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "\n", " 8. 关键词: 产品 (权重: 0.2052)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 0.820.85\n", "0.93 1.711.791.69\n", "1.95\n", "2.26\n", "2.73\n", "3.263.21\n", "4.35\n", "3.89\n", "-20.00%\n", "0.00%\n", "20.00%\n", "40.00%\n", "60.00%\n", "80.0...\n", "\n", " 9. 关键词: 国金 (权重: 0.1938)\n", "    句子1: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "    句子2: 0.820.85\n", "0.93 1.711.791.69\n", "1.95\n", "2.26\n", "2.73\n", "3.263.21\n", "4.35\n", "3.89\n", "-20.00%\n", "0.00%\n", "20.00%\n", "40.00%\n", "60.00%\n", "80.0...\n", "\n", "10. 关键词: 指南针 (权重: 0.1440)\n", "    句子1: 非银行金融行业研究2025年03月31日增持（维持评级）行业深度研究证券研究报告\n", "用使江令陈PIV金国供仅告报此\n", "用户472398006于2025-04-25日下载，仅供本人内部使用，不可传播与转载\n", "...\n", "    句子2: 图表34：2013-2024 年东方财富营业收入及同比变化 \n", " \n", "图表35：2013-2024 年东方财富归母净利润及同比变化 \n", " \n", " \n", " \n", "来源：iFinD，国金证券研究所 \n", " \n", "来源：iFinD，...\n", "\n", " 匹配的情感词 (前20个):\n", "--------------------------------------------------\n", "正面情感词 (19 个):\n", "  • 昌盛 (+1.00)\n", "  • 核心 (+1.00)\n", "  • 发展 (+1.00)\n", "  • 优势 (+1.00)\n", "  • 财富 (+1.00)\n", "  • 优势 (+1.00)\n", "  • 核心 (+1.00)\n", "  • 财富 (+1.00)\n", "  • 专注 (+1.00)\n", "  • 特色 (+1.00)\n", "\n", "负面情感词 (1 个):\n", "  • 分化 (-1.00)\n", "\n", " 两种方法比较结果 (前15个关键词):\n", "--------------------------------------------------\n", "关键词          词典得分     词典倾向   FinBERT得分  FinBERT倾向 一致   权重      \n", "----------------------------------------------------------------------\n"]}, {"ename": "NameError", "evalue": "name 'consistent' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[44], line 60\u001b[0m\n\u001b[0;32m     57\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m-\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m*\u001b[39m \u001b[38;5;241m70\u001b[39m)\n\u001b[0;32m     59\u001b[0m     \u001b[38;5;28;01mfor\u001b[39;00m _, row \u001b[38;5;129;01min\u001b[39;00m top_comparison\u001b[38;5;241m.\u001b[39miterrows():\n\u001b[1;32m---> 60\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m关键词\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m<12\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m词典情感得分\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m>7.3f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m词典情感倾向\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m<6\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFinBERT情感得分\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m>9.3f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mFinBERT情感倾向\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m<8\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mconsistent\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m<4\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mrow[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m权重\u001b[39m\u001b[38;5;124m'\u001b[39m]\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m>7.4f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     62\u001b[0m \u001b[38;5;66;03m# 5. 情感关键词分析结果\u001b[39;00m\n\u001b[0;32m     63\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124msentiment_keywords\u001b[39m\u001b[38;5;124m'\u001b[39m \u001b[38;5;129;01min\u001b[39;00m analysis_results:\n", "\u001b[1;31mNameError\u001b[0m: name 'consistent' is not defined"]}], "source": ["# 显示详细的分析结果\n", "if analysis_results and analysis_results['success']:\n", "    print(\" 详细分析结果:\")\n", "    print(\"=\" * 80)\n", "    \n", "    # 1. 文本样本\n", "    print(\"\\n 提取的文本样本 (前500字符):\")\n", "    print(\"-\" * 50)\n", "    print(analysis_results['text_sample'])\n", "    \n", "    # 2. 关键词及其代表性句子\n", "    print(\"\\n 关键词及其代表性句子:\")\n", "    print(\"-\" * 50)\n", "    keyword_sentences = analysis_results['keyword_sentences']\n", "    \n", "    for i, (keyword, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "        print(f\"\\n{i:2d}. 关键词: {keyword} (权重: {weight:.4f})\")\n", "        sentences = keyword_sentences.get(keyword, [])\n", "        if sentences:\n", "            for j, sentence in enumerate(sentences[:2], 1):\n", "                print(f\"    句子{j}: {sentence[:100]}{'...' if len(sentence) > 100 else ''}\")\n", "        else:\n", "            print(\"    (未找到包含该关键词的句子)\")\n", "    \n", "    # 3. 匹配的情感词\n", "    print(\"\\n 匹配的情感词 (前20个):\")\n", "    print(\"-\" * 50)\n", "    matched_words = analysis_results['dict_analysis']['matched_words']\n", "    \n", "    if matched_words:\n", "        # 按情感得分排序\n", "        sorted_sentiment_words = sorted(matched_words, key=lambda x: abs(x[1]), reverse=True)[:20]\n", "        \n", "        positive_words = [(w, s) for w, s in sorted_sentiment_words if s > 0]\n", "        negative_words = [(w, s) for w, s in sorted_sentiment_words if s < 0]\n", "        \n", "        print(f\"正面情感词 ({len(positive_words)} 个):\")\n", "        for word, score in positive_words[:10]:\n", "            print(f\"  • {word} ({score:+.2f})\")\n", "        \n", "        print(f\"\\n负面情感词 ({len(negative_words)} 个):\")\n", "        for word, score in negative_words[:10]:\n", "            print(f\"  • {word} ({score:+.2f})\")\n", "    else:\n", "        print(\"未匹配到情感词\")\n", "    \n", "    # 4. 比较分析结果表格\n", "    print(\"\\n 两种方法比较结果 (前15个关键词):\")\n", "    print(\"-\" * 50)\n", "    comparison_df = analysis_results['comparison']['comparison_df']\n", "    \n", "    if not comparison_df.empty:\n", "        # 按权重排序显示前15个\n", "        top_comparison = comparison_df.nlargest(15, '权重')\n", "        \n", "        print(f\"{'关键词':<12} {'词典得分':<8} {'词典倾向':<6} {'FinBERT得分':<10} {'FinBERT倾向':<8} {'一致':<4} {'权重':<8}\")\n", "        print(\"-\" * 70)\n", "        \n", "        for _, row in top_comparison.iterrows():\n", "            consistent = '是' if row['倾向一致'] else '否'\n", "            print(f\"{row['关键词']:<12} {row['词典情感得分']:>7.3f} {row['词典情感倾向']:<6} {row['FinBERT情感得分']:>9.3f} {row['FinBERT情感倾向']:<8} {consistent:<4} {row['权重']:>7.4f}\")\n", "    \n", "    # 5. 情感关键词分析结果\n", "    if 'sentiment_keywords' in analysis_results:\n", "        print(\"\\n 情感关键词分析结果:\")\n", "        print(\"-\" * 50)\n", "        \n", "        sentiment_keywords = analysis_results['sentiment_keywords']\n", "        positive_keywords = sentiment_keywords['positive_keywords']\n", "        negative_keywords = sentiment_keywords['negative_keywords']\n", "        \n", "        print(f\"正面情感关键词 ({len(positive_keywords)} 个):\")\n", "        if positive_keywords:\n", "            for i, (word, sentiment_score, freq_weight) in enumerate(positive_keywords[:10], 1):\n", "                combined_score = sentiment_score * freq_weight\n", "                print(f\"  {i:2d}. {word:<12} 情感强度: {sentiment_score:.3f}, 词频权重: {freq_weight:.6f}, 综合得分: {combined_score:.6f}\")\n", "        else:\n", "            print(\"  (未找到正面情感关键词)\")\n", "        \n", "        print(f\"\\n负面情感关键词 ({len(negative_keywords)} 个):\")\n", "        if negative_keywords:\n", "            for i, (word, sentiment_score, freq_weight) in enumerate(negative_keywords[:10], 1):\n", "                combined_score = sentiment_score * freq_weight\n", "                print(f\"  {i:2d}. {word:<12} 情感强度: {sentiment_score:.3f}, 词频权重: {freq_weight:.6f}, 综合得分: {combined_score:.6f}\")\n", "        else:\n", "            print(\"  (未找到负面情感关键词)\")\n", "    \n", "    # 6. 上下文情感关键词分析结果\n", "    if 'contextual_sentiment_keywords' in analysis_results:\n", "        contextual_keywords = analysis_results['contextual_sentiment_keywords']\n", "        if contextual_keywords:\n", "            print(\"\\n 上下文情感关键词分析 (前10个):\")\n", "            print(\"-\" * 50)\n", "            \n", "            for i, (keyword, context_score, sentiment_words) in enumerate(contextual_keywords[:10], 1):\n", "                sentiment_tendency = '正面' if context_score > 0.02 else '负面' if context_score < -0.02 else '中性'\n", "                sentiment_words_str = ', '.join(sentiment_words[:5]) if sentiment_words else '无'\n", "                if len(sentiment_words) > 5:\n", "                    sentiment_words_str += '...'\n", "                \n", "                print(f\"  {i:2d}. {keyword:<12} 上下文得分: {context_score:+.4f} ({sentiment_tendency})\")\n", "                print(f\"      相关情感词: {sentiment_words_str}\")\n", "    \n", "    print(\"\\n\" + \"=\" * 80)\n", "else:\n", "    print(\" 无法显示详细结果，分析未成功完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.4 可视化结果展示"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成可视化图表\n", "if analysis_results and analysis_results['success']:\n", "    print(\" 生成可视化图表...\")\n", "    \n", "    # 获取分析结果\n", "    dict_score = analysis_results['dict_analysis']['overall_score']\n", "    finbert_score = analysis_results['finbert_analysis']['overall_score']\n", "    dict_keywords = analysis_results['dict_analysis']['keyword_scores']\n", "    finbert_keywords = analysis_results['finbert_analysis']['keyword_scores']\n", "    comparison_df = analysis_results['comparison']['comparison_df']\n", "    keywords = analysis_results['keywords']\n", "    \n", "    try:\n", "        # 1. 情感分析方法对比图\n", "        print(\"\\n 1. 情感分析方法对比\")\n", "        create_sentiment_comparison_chart(dict_score, finbert_score)\n", "        \n", "        # 2. 词典方法关键词情感分布\n", "        if dict_keywords:\n", "            print(\"\\n 2. 词典方法 - 关键词情感分布\")\n", "            create_keyword_sentiment_chart(dict_keywords, \"Dictionary Method\", top_n=15)\n", "        \n", "        # 3. FinBERT方法关键词情感分布\n", "        if finbert_keywords:\n", "            print(\"\\n 3. FinBERT方法 - 关键词情感分布\")\n", "            create_keyword_sentiment_chart(finbert_keywords, \"FinBERT Method\", top_n=15)\n", "        \n", "        # 4. 两种方法一致性分析\n", "        if not comparison_df.empty:\n", "            print(\"\\n 4. 两种方法一致性分析\")\n", "            create_agreement_chart(comparison_df)\n", "        \n", "        # 5. 关键词云图\n", "        if keywords:\n", "            print(\"\\n 5. 关键词云图\")\n", "            create_wordcloud(keywords)\n", "        \n", "        # 6. 情感关键词可视化\n", "        if 'sentiment_keywords' in analysis_results:\n", "            sentiment_keywords = analysis_results['sentiment_keywords']\n", "            positive_keywords = sentiment_keywords['positive_keywords']\n", "            negative_keywords = sentiment_keywords['negative_keywords']\n", "            \n", "            if positive_keywords or negative_keywords:\n", "                print(\"\\n 6. 情感关键词分布\")\n", "                create_sentiment_keywords_chart(positive_keywords, negative_keywords, top_n=10)\n", "                \n", "                print(\"\\n 7. 情感关键词统计总结\")\n", "                create_sentiment_summary_chart(positive_keywords, negative_keywords)\n", "        \n", "        # 7. 上下文情感关键词分析\n", "        if 'contextual_sentiment_keywords' in analysis_results:\n", "            contextual_keywords = analysis_results['contextual_sentiment_keywords']\n", "            if contextual_keywords:\n", "                print(\"\\n 8. 上下文情感关键词分析\")\n", "                create_contextual_sentiment_chart(contextual_keywords, top_n=15)\n", "        \n", "        print(\"\\n 所有可视化图表生成完成!\")\n", "        \n", "    except Exception as e:\n", "        print(f\"\\n 可视化过程中出现错误: {e}\")\n", "        print(\"这可能是由于缺少某些可视化库或字体问题导致的\")\n", "        \n", "else:\n", "    print(\" 无法生成可视化图表，分析未成功完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 8.5 结果数据导出"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将分析结果导出为DataFrame，便于进一步分析\n", "if analysis_results and analysis_results['success']:\n", "    print(\"💾 导出分析结果数据...\")\n", "    \n", "    # 1. 关键词分析结果\n", "    keywords_data = []\n", "    for keyword, weight in analysis_results['keywords']:\n", "        # 获取词典和FinBERT的情感得分\n", "        dict_score = 0\n", "        finbert_score = 0\n", "        \n", "        # 从词典分析结果中查找\n", "        for kw, score, w in analysis_results['dict_analysis']['keyword_scores']:\n", "            if kw == keyword:\n", "                dict_score = score\n", "                break\n", "        \n", "        # 从FinBERT分析结果中查找\n", "        for kw, score, w in analysis_results['finbert_analysis']['keyword_scores']:\n", "            if kw == keyword:\n", "                finbert_score = score\n", "                break\n", "        \n", "        keywords_data.append({\n", "            '关键词': keyword,\n", "            '权重': weight,\n", "            '词典情感得分': dict_score,\n", "            'FinBERT情感得分': finbert_score,\n", "            '平均情感得分': (dict_score + finbert_score) / 2,\n", "            '得分差异': abs(dict_score - finbert_score)\n", "        })\n", "    \n", "    keywords_df = pd.DataFrame(keywords_data)\n", "    \n", "    print(\"\\n 关键词分析结果表:\")\n", "    print(keywords_df.head(10).to_string(index=False, float_format='%.4f'))\n", "    \n", "    # 2. 情感词统计\n", "    matched_words = analysis_results['dict_analysis']['matched_words']\n", "    if matched_words:\n", "        sentiment_stats = {\n", "            '正面情感词数量': len([w for w, s in matched_words if s > 0]),\n", "            '负面情感词数量': len([w for w, s in matched_words if s < 0]),\n", "            '总情感词数量': len(matched_words),\n", "            '正面情感词平均得分': np.mean([s for w, s in matched_words if s > 0]) if any(s > 0 for w, s in matched_words) else 0,\n", "            '负面情感词平均得分': np.mean([s for w, s in matched_words if s < 0]) if any(s < 0 for w, s in matched_words) else 0\n", "        }\n", "        \n", "        print(\"\\n 情感词统计:\")\n", "        for key, value in sentiment_stats.items():\n", "            if '得分' in key:\n", "                print(f\"  • {key}: {value:+.4f}\")\n", "            else:\n", "                print(f\"  • {key}: {value}\")\n", "    \n", "    # 3. 分析摘要\n", "    print(\"\\n 分析摘要:\")\n", "    final_assessment = analysis_results['final_assessment']\n", "    print(f\"  • 综合情感得分: {final_assessment['combined_score']:+.4f}\")\n", "    print(f\"  • 综合情感倾向: {final_assessment['combined_sentiment']}\")\n", "    print(f\"  • 方法一致率: {final_assessment['agreement_rate']:.1%}\")\n", "    print(f\"  • 结果可信度: {final_assessment['confidence']}\")\n", "    \n", "    # 4. 情感关键词统计\n", "    if 'sentiment_keywords' in analysis_results:\n", "        sentiment_keywords = analysis_results['sentiment_keywords']\n", "        positive_keywords = sentiment_keywords['positive_keywords']\n", "        negative_keywords = sentiment_keywords['negative_keywords']\n", "        \n", "        print(\"\\n 情感关键词统计:\")\n", "        print(f\"  • 正面情感关键词数量: {len(positive_keywords)}\")\n", "        print(f\"  • 负面情感关键词数量: {len(negative_keywords)}\")\n", "        \n", "        if positive_keywords:\n", "            avg_pos_strength = np.mean([item[1] for item in positive_keywords])\n", "            avg_pos_freq = np.mean([item[2] for item in positive_keywords])\n", "            print(f\"  • 正面关键词平均情感强度: {avg_pos_strength:.4f}\")\n", "            print(f\"  • 正面关键词平均词频权重: {avg_pos_freq:.6f}\")\n", "        \n", "        if negative_keywords:\n", "            avg_neg_strength = np.mean([item[1] for item in negative_keywords])\n", "            avg_neg_freq = np.mean([item[2] for item in negative_keywords])\n", "            print(f\"  • 负面关键词平均情感强度: {avg_neg_strength:.4f}\")\n", "            print(f\"  • 负面关键词平均词频权重: {avg_neg_freq:.6f}\")\n", "        \n", "        # 情感关键词平衡度\n", "        if 'sentiment_keyword_balance' in final_assessment:\n", "            balance = final_assessment['sentiment_keyword_balance']\n", "            print(f\"  • 情感关键词平衡度: {balance:.1%} ({'偏正面' if balance > 0.6 else '偏负面' if balance < 0.4 else '平衡'})\")\n", "    \n", "    # 5. 上下文情感关键词统计\n", "    if 'contextual_sentiment_keywords' in analysis_results:\n", "        contextual_keywords = analysis_results['contextual_sentiment_keywords']\n", "        if contextual_keywords:\n", "            print(\"\\n 上下文情感关键词统计:\")\n", "            \n", "            positive_context = [kw for kw, score, _ in contextual_keywords if score > 0.02]\n", "            negative_context = [kw for kw, score, _ in contextual_keywords if score < -0.02]\n", "            neutral_context = [kw for kw, score, _ in contextual_keywords if -0.02 <= score <= 0.02]\n", "            \n", "            print(f\"  • 上下文正面倾向关键词: {len(positive_context)} 个\")\n", "            print(f\"  • 上下文负面倾向关键词: {len(negative_context)} 个\")\n", "            print(f\"  • 上下文中性倾向关键词: {len(neutral_context)} 个\")\n", "            \n", "            if contextual_keywords:\n", "                avg_context_score = np.mean([abs(score) for _, score, _ in contextual_keywords])\n", "                print(f\"  • 平均上下文情感强度: {avg_context_score:.4f}\")\n", "    \n", "    print(\"\\n 数据导出完成!\")\n", "    \n", "else:\n", "    print(\" 无法导出数据，分析未成功完成\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}