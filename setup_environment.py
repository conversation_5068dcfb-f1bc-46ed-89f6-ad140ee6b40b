#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
研报情感分析系统 - 环境配置脚本
使用国内镜像源安装兼容版本的依赖库
"""

import subprocess
import sys
import os
import time

def run_command(command, description=""):
    """运行命令并显示结果"""
    print(f"🔧 {description}")
    print(f"   执行: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"   ✅ 成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"   ❌ 失败: {e}")
        if e.stdout:
            print(f"   输出: {e.stdout}")
        if e.stderr:
            print(f"   错误: {e.stderr}")
        return False

def setup_pip_mirrors():
    """配置pip国内镜像源"""
    print("\n📦 配置pip国内镜像源...")
    
    mirrors = [
        "https://pypi.tuna.tsinghua.edu.cn/simple",
        "https://mirrors.aliyun.com/pypi/simple/",
        "https://pypi.douban.com/simple/"
    ]
    
    # 配置清华镜像为主要源
    commands = [
        f"pip config set global.index-url {mirrors[0]}",
        "pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn",
        "pip config set global.timeout 120"
    ]
    
    for cmd in commands:
        run_command(cmd, "配置镜像源")
    
    # 升级pip
    run_command("python -m pip install --upgrade pip", "升级pip")

def create_conda_environment():
    """创建conda虚拟环境"""
    print("\n🚀 创建新的conda虚拟环境...")
    
    # 检查conda是否可用
    if not run_command("conda --version", "检查conda"):
        print("❌ conda不可用，请先安装Anaconda或Miniconda")
        return False
    
    # 删除已存在的环境
    run_command("conda env remove -n sentiment_analysis -y", "删除旧环境（如果存在）")
    
    # 创建新环境
    if not run_command("conda create -n sentiment_analysis python=3.9 -y", "创建新环境"):
        return False
    
    print("✅ 虚拟环境创建成功")
    return True

def install_packages():
    """安装所需的包"""
    print("\n📚 开始安装依赖包...")
    
    # 激活环境的命令前缀
    if os.name == 'nt':  # Windows
        activate_cmd = "conda activate sentiment_analysis && "
    else:  # Linux/Mac
        activate_cmd = "source $(conda info --base)/etc/profile.d/conda.sh && conda activate sentiment_analysis && "
    
    # 包安装列表（按依赖顺序）
    package_groups = [
        {
            "name": "基础科学计算库",
            "packages": [
                "numpy==1.24.3",
                "pandas==2.0.3", 
                "matplotlib==3.7.2",
                "seaborn==0.12.2",
                "scikit-learn==1.3.0"
            ]
        },
        {
            "name": "PDF处理库",
            "packages": [
                "pdfplumber==0.9.0",
                "PyMuPDF==1.23.3"
            ]
        },
        {
            "name": "中文文本处理库",
            "packages": [
                "jieba==0.42.1",
                "textrank4zh==0.3"
            ]
        },
        {
            "name": "可视化库",
            "packages": [
                "wordcloud==1.9.2"
            ]
        },
        {
            "name": "其他必要库",
            "packages": [
                "tqdm==4.65.0",
                "requests==2.31.0",
                "packaging>=20.0",
                "typing-extensions>=4.0.0"
            ]
        },
        {
            "name": "Jupyter相关",
            "packages": [
                "jupyter==1.0.0",
                "ipykernel==6.25.0"
            ]
        }
    ]
    
    # 安装基础包
    for group in package_groups:
        print(f"\n🔧 安装{group['name']}...")
        for package in group['packages']:
            cmd = f"{activate_cmd}pip install {package} -i https://pypi.tuna.tsinghua.edu.cn/simple"
            if not run_command(cmd, f"安装 {package}"):
                print(f"⚠️ {package} 安装失败，尝试继续...")
    
    # 特殊处理：安装PyTorch（使用官方源）
    print(f"\n🤖 安装PyTorch（CPU版本）...")
    torch_cmd = f"{activate_cmd}pip install torch==2.0.1 torchvision==0.15.2 torchaudio==2.0.2 --index-url https://download.pytorch.org/whl/cpu"
    if not run_command(torch_cmd, "安装PyTorch"):
        # 备用方案：使用清华镜像
        backup_cmd = f"{activate_cmd}pip install torch==2.0.1 -i https://pypi.tuna.tsinghua.edu.cn/simple"
        run_command(backup_cmd, "使用清华镜像安装PyTorch")
    
    # 安装transformers（关键！使用兼容版本）
    print(f"\n🤖 安装Transformers（兼容版本）...")
    transformers_packages = [
        "transformers==4.30.0",
        "accelerate==0.20.3", 
        "tokenizers==0.13.3"
    ]
    
    for package in transformers_packages:
        cmd = f"{activate_cmd}pip install {package} -i https://pypi.tuna.tsinghua.edu.cn/simple"
        run_command(cmd, f"安装 {package}")
    
    # 注册Jupyter内核
    print(f"\n📋 注册Jupyter内核...")
    kernel_cmd = f"{activate_cmd}python -m ipykernel install --user --name sentiment_analysis --display-name \"Python (情感分析)\""
    run_command(kernel_cmd, "注册Jupyter内核")

def test_installation():
    """测试安装结果"""
    print("\n🧪 测试安装结果...")
    
    if os.name == 'nt':  # Windows
        activate_cmd = "conda activate sentiment_analysis && "
    else:  # Linux/Mac
        activate_cmd = "source $(conda info --base)/etc/profile.d/conda.sh && conda activate sentiment_analysis && "
    
    test_script = '''
import sys
print(f"Python版本: {sys.version}")
print(f"Python路径: {sys.executable}")

# 测试基础库
try:
    import pandas as pd
    import numpy as np
    import matplotlib.pyplot as plt
    import seaborn as sns
    from sklearn.feature_extraction.text import TfidfVectorizer
    print("✅ 基础科学计算库导入成功")
except Exception as e:
    print(f"❌ 基础库导入失败: {e}")

# 测试PDF处理
try:
    import pdfplumber
    import fitz
    print("✅ PDF处理库导入成功")
except Exception as e:
    print(f"❌ PDF处理库导入失败: {e}")

# 测试中文处理
try:
    import jieba
    from textrank4zh import TextRank4Keyword
    print("✅ 中文文本处理库导入成功")
except Exception as e:
    print(f"❌ 中文处理库导入失败: {e}")

# 测试可视化
try:
    from wordcloud import WordCloud
    print("✅ 可视化库导入成功")
except Exception as e:
    print(f"❌ 可视化库导入失败: {e}")

# 测试深度学习（关键测试）
try:
    import torch
    from transformers import AutoTokenizer, AutoModelForSequenceClassification
    print(f"✅ 深度学习库导入成功 - torch: {torch.__version__}")
    
    # 测试transformers基本功能
    tokenizer = AutoTokenizer.from_pretrained("bert-base-uncased")
    print("✅ Transformers功能测试成功")
    
except Exception as e:
    print(f"❌ 深度学习库导入失败: {e}")

print("🎉 环境测试完成！")
'''
    
    # 将测试脚本写入临时文件
    with open("temp_test.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    # 运行测试
    test_cmd = f"{activate_cmd}python temp_test.py"
    run_command(test_cmd, "运行环境测试")
    
    # 清理临时文件
    try:
        os.remove("temp_test.py")
    except:
        pass

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 研报情感分析系统 - 环境配置脚本")
    print("=" * 60)
    print("📦 使用国内镜像源安装兼容版本的依赖库")
    print("🎯 解决transformers版本冲突问题")
    print("=" * 60)
    
    # 步骤1: 配置镜像源
    setup_pip_mirrors()
    
    # 步骤2: 创建虚拟环境
    if not create_conda_environment():
        print("❌ 环境创建失败，请检查conda安装")
        return
    
    # 步骤3: 安装包
    install_packages()
    
    # 步骤4: 测试安装
    test_installation()
    
    # 完成提示
    print("\n" + "=" * 60)
    print("🎉 环境配置完成！")
    print("=" * 60)
    print("\n📋 使用方法:")
    print("1. 打开命令行/终端")
    print("2. 运行: conda activate sentiment_analysis")
    print("3. 运行: jupyter notebook")
    print("4. 在Jupyter中选择内核: Python (情感分析)")
    print("5. 打开您的notebook文件")
    print("\n💡 提示:")
    print("- 如果遇到问题，请重新运行此脚本")
    print("- 确保网络连接正常")
    print("- 可以运行 python test_new_environment.py 进行详细测试")
    print("\n🎊 现在您可以愉快地使用研报情感分析系统了！")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n❌ 用户中断安装")
    except Exception as e:
        print(f"\n❌ 安装过程中发生错误: {e}")
    finally:
        input("\n按回车键退出...")
