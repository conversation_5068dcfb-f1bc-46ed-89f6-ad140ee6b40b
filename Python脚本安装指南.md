# 🐍 Python脚本安装指南

## 📋 脚本说明

我为您准备了3个Python脚本来解决依赖冲突问题：

### 🔧 脚本选择

| 脚本名称 | 适用场景 | 推荐度 |
|---------|---------|--------|
| `create_conda_env.py` | 有conda环境，想创建独立虚拟环境 | ⭐⭐⭐⭐⭐ |
| `install_with_mirrors.py` | 在当前环境直接安装，使用国内镜像 | ⭐⭐⭐⭐ |
| `setup_environment.py` | 完整的环境配置（包含conda创建） | ⭐⭐⭐ |

## 🚀 推荐方案：使用conda虚拟环境

### 步骤1: 运行conda环境创建脚本
```bash
python create_conda_env.py
```

**这个脚本会：**
- ✅ 检查conda是否可用
- ✅ 配置国内镜像源（清华大学镜像）
- ✅ 创建名为 `sentiment_analysis` 的新环境
- ✅ 安装所有兼容版本的依赖包
- ✅ 注册Jupyter内核
- ✅ 测试安装结果

### 步骤2: 激活环境并使用
```bash
# 激活环境
conda activate sentiment_analysis

# 启动Jupyter
jupyter notebook
```

### 步骤3: 在Jupyter中选择正确内核
在Jupyter中选择内核：`Python (情感分析)`

## 🔄 备用方案：直接安装

如果您不想创建新环境，可以在当前环境直接安装：

```bash
python install_with_mirrors.py
```

**这个脚本会：**
- ✅ 使用多个国内镜像源（清华、阿里云、豆瓣）
- ✅ 自动重试失败的安装
- ✅ 安装兼容版本的所有依赖
- ✅ 测试安装结果

## 📦 国内镜像源配置

脚本会自动配置以下镜像源：

### Conda镜像源
- 清华大学：`https://mirrors.tuna.tsinghua.edu.cn/anaconda/`

### Pip镜像源
- 清华大学：`https://pypi.tuna.tsinghua.edu.cn/simple`
- 阿里云：`https://mirrors.aliyun.com/pypi/simple/`
- 豆瓣：`https://pypi.douban.com/simple/`

## 🎯 关键版本配置

脚本会安装以下兼容版本：

```
# 基础库
numpy==1.24.3
pandas==2.0.3
matplotlib==3.7.2
seaborn==0.12.2
scikit-learn==1.3.0

# PDF处理
pdfplumber==0.9.0
PyMuPDF==1.23.3

# 中文处理
jieba==0.42.1
textrank4zh==0.3

# 可视化
wordcloud==1.9.2

# 深度学习（关键！）
torch==2.0.1
transformers==4.30.0  # 避免OffloadedHybridCache错误
accelerate==0.20.3
tokenizers==0.13.3
```

## 🧪 测试安装结果

安装完成后，运行测试脚本：

```bash
python test_new_environment.py
```

或者手动测试：

```python
# 测试基础库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 测试中文处理
import jieba
from textrank4zh import TextRank4Keyword

# 测试深度学习（关键测试）
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification

print("🎉 所有库导入成功！")
```

## 🔧 故障排除

### 问题1: conda命令不存在
**解决方案**：
```bash
# 下载并安装Miniconda（推荐）
# Windows: https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe
# 或使用清华镜像：https://mirrors.tuna.tsinghua.edu.cn/anaconda/miniconda/
```

### 问题2: 网络连接超时
**解决方案**：
- 脚本已配置多个镜像源，会自动重试
- 检查网络连接
- 尝试使用手机热点

### 问题3: 权限问题
**解决方案**：
```bash
# 使用管理员权限运行命令提示符
# 或者在命令前加 sudo（Linux/Mac）
sudo python create_conda_env.py
```

### 问题4: transformers仍然报错
**解决方案**：
```bash
# 在新环境中手动安装更早版本
conda activate sentiment_analysis
pip uninstall transformers -y
pip install transformers==4.21.0 -i https://pypi.tuna.tsinghua.edu.cn/simple
```

## 📋 使用流程总结

### 完整流程
1. **运行脚本**：`python create_conda_env.py`
2. **等待完成**：约5-15分钟（取决于网络速度）
3. **激活环境**：`conda activate sentiment_analysis`
4. **启动Jupyter**：`jupyter notebook`
5. **选择内核**：`Python (情感分析)`
6. **打开notebook**：加载您的研报情感分析文件
7. **开始使用**：享受无错误的分析体验

### 验证成功的标志
- ✅ 看到 "🎉 所有测试通过！环境配置成功！"
- ✅ transformers导入无错误
- ✅ TextRank4zh正常工作
- ✅ 词云图显示中文关键词

## 💡 小贴士

1. **网络优化**：脚本使用国内镜像，下载速度更快
2. **版本锁定**：所有版本都经过测试，确保兼容
3. **环境隔离**：新环境不影响现有Python环境
4. **自动重试**：网络失败时自动尝试其他镜像源
5. **详细日志**：安装过程中显示详细信息，便于排错

## 🎉 预期效果

脚本运行成功后，您将拥有：
- 🤖 **完全可用的FinBERT功能**
- 🔤 **正常工作的TextRank4zh**
- 🎨 **正确显示中文的词云图**
- 📊 **所有可视化功能正常**
- ⚡ **稳定无错误的运行环境**

现在请运行 `python create_conda_env.py` 开始配置您的专属环境！🚀
