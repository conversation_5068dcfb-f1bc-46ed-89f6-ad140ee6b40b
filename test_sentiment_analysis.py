#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试情感分析完整版notebook的核心功能
"""

import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 基础库
import pandas as pd
import numpy as np
import jieba
import jieba.analyse
from collections import Counter, defaultdict
from typing import List, Tuple, Dict

# 可视化
import matplotlib.pyplot as plt
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False

def test_text_processing():
    """测试文本处理功能"""
    print("🔄 测试文本处理功能...")
    
    # 测试文本
    test_text = """
    这是一份关于金融科技公司的研究报告。该公司在过去一年中表现优秀，
    营收增长显著，盈利能力强劲。但是也面临一些风险和挑战，
    包括市场竞争激烈和监管政策的不确定性。
    """
    
    # 分词测试
    words = jieba.lcut(test_text)
    print(f"✅ 分词成功，共 {len(words)} 个词")
    
    # 关键词提取测试
    keywords = jieba.analyse.textrank(test_text, topK=10, withWeight=True)
    print(f"✅ 关键词提取成功，共 {len(keywords)} 个关键词")
    
    return words, keywords

def test_sentiment_analysis():
    """测试情感分析功能"""
    print("📊 测试情感分析功能...")
    
    # 简单的情感词典
    positive_words = ['优秀', '增长', '显著', '强劲', '盈利']
    negative_words = ['风险', '挑战', '激烈', '不确定性']
    
    sentiment_dict = {}
    for word in positive_words:
        sentiment_dict[word] = 1.0
    for word in negative_words:
        sentiment_dict[word] = -1.0
    
    # 测试文本
    test_text = "该公司表现优秀，营收增长显著，但面临风险和挑战。"
    words = jieba.lcut(test_text)
    
    # 计算情感得分
    total_score = 0
    matched_words = []
    
    for word in words:
        if word in sentiment_dict:
            score = sentiment_dict[word]
            total_score += score
            matched_words.append((word, score))
    
    overall_score = total_score / len(words) if words else 0
    
    print(f"✅ 情感分析完成，整体得分: {overall_score:.4f}")
    print(f"✅ 匹配情感词: {matched_words}")
    
    return overall_score, matched_words

def test_visualization():
    """测试可视化功能"""
    print("🎨 测试可视化功能...")
    
    # 创建测试数据
    methods = ['Dictionary', 'SnowNLP', 'Rules']
    scores = [0.15, 0.08, 0.12]
    
    # 创建对比图
    fig, ax = plt.subplots(figsize=(10, 6))
    colors = ['green' if s > 0 else 'red' if s < 0 else 'gray' for s in scores]
    bars = ax.bar(methods, scores, color=colors, alpha=0.7)
    
    # 添加数值标签
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{score:.3f}', ha='center', va='bottom', fontweight='bold')
    
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax.set_title('Sentiment Analysis Methods Comparison', fontsize=14, fontweight='bold')
    ax.set_ylabel('Sentiment Score', fontsize=12)
    ax.grid(axis='y', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('sentiment_comparison_test.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("✅ 可视化测试成功，图表已保存为 sentiment_comparison_test.png")

def test_keyword_extraction():
    """测试关键词提取功能"""
    print("🔍 测试关键词提取功能...")
    
    test_text = """
    金融科技公司在数字化转型中发挥重要作用。人工智能、大数据、
    区块链等技术推动了金融服务的创新。投资者对这些公司的前景
    保持乐观态度，认为它们具有良好的增长潜力。
    """
    
    # 使用多种方法提取关键词
    all_keywords = defaultdict(float)
    
    # 方法1: jieba TextRank
    keywords_jieba = jieba.analyse.textrank(test_text, topK=10, withWeight=True)
    for word, weight in keywords_jieba:
        all_keywords[word] += weight * 0.5
    
    # 方法2: jieba TF-IDF
    keywords_tfidf = jieba.analyse.extract_tags(test_text, topK=10, withWeight=True)
    for word, weight in keywords_tfidf:
        all_keywords[word] += weight * 0.5
    
    # 合并并排序
    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)
    
    print(f"✅ 关键词提取成功，共 {len(sorted_keywords)} 个关键词")
    print("前5个关键词:")
    for i, (word, weight) in enumerate(sorted_keywords[:5], 1):
        print(f"  {i}. {word} (权重: {weight:.4f})")
    
    return sorted_keywords

def main():
    """主测试函数"""
    print("🚀 开始测试情感分析系统...")
    print("=" * 50)
    
    try:
        # 测试1: 文本处理
        words, keywords = test_text_processing()
        
        # 测试2: 情感分析
        sentiment_score, matched_words = test_sentiment_analysis()
        
        # 测试3: 关键词提取
        extracted_keywords = test_keyword_extraction()
        
        # 测试4: 可视化
        test_visualization()
        
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        print(f"  • 文本处理: ✅ 正常")
        print(f"  • 情感分析: ✅ 正常 (得分: {sentiment_score:.4f})")
        print(f"  • 关键词提取: ✅ 正常 (共{len(extracted_keywords)}个)")
        print(f"  • 可视化: ✅ 正常")
        print("\n🎉 所有测试通过！情感分析系统功能正常。")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
