#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试字体修复效果
"""

import os
import json
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from wordcloud import WordCloud
import platform

def test_font_fix():
    """测试字体修复效果"""
    
    print("测试字体修复效果...")
    print("=" * 50)
    
    # 检查修复后的文件
    fixed_file = '研报情感分析完整版_字体修复版.ipynb'
    if not os.path.exists(fixed_file):
        print(f"错误: 修复文件 {fixed_file} 不存在")
        return False
    
    print(f"[成功] 找到修复文件: {fixed_file}")
    
    # 读取文件
    try:
        with open(fixed_file, 'r', encoding='utf-8') as f:
            notebook = json.load(f)
        print(f"[成功] 成功读取修复文件")
    except Exception as e:
        print(f"[失败] 读取文件失败: {e}")
        return False
    
    # 检查修复内容
    has_font_fix = False
    has_wordcloud_fix = False
    has_encoding_fix = False
    
    for cell in notebook.get('cells', []):
        if 'source' in cell:
            source_text = ''.join(cell['source']) if isinstance(cell['source'], list) else cell['source']
            
            # 检查字体修复
            if 'get_system_font_path()' in source_text and 'SYSTEM_FONT_PATH' in source_text:
                has_font_fix = True
            
            # 检查WordCloud修复
            if 'font_path=SYSTEM_FONT_PATH' in source_text:
                has_wordcloud_fix = True
            
            # 检查编码修复
            if 'for encoding in [' in source_text and 'utf-8' in source_text and 'gbk' in source_text:
                has_encoding_fix = True
    
    print("\n修复内容检查:")
    print(f"  字体路径检测: {'[成功] 已修复' if has_font_fix else '[警告] 未发现'}")
    print(f"  WordCloud字体: {'[成功] 已修复' if has_wordcloud_fix else '[警告] 未发现'}")
    print(f"  编码问题: {'[成功] 已修复' if has_encoding_fix else '[警告] 未发现'}")
    
    # 测试系统字体
    print("\n系统字体测试:")
    system = platform.system()
    print(f"操作系统: {system}")
    
    # 获取系统字体路径
    def get_system_font_path():
        """获取系统默认字体路径"""
        system_fonts = []
        
        if system == 'Windows':
            system_fonts = [
                'C:/Windows/Fonts/arial.ttf',
                'C:/Windows/Fonts/simhei.ttf',
                'C:/Windows/Fonts/msyh.ttc'
            ]
        elif system == 'Darwin':  # macOS
            system_fonts = [
                '/System/Library/Fonts/Arial.ttf',
                '/System/Library/Fonts/PingFang.ttc'
            ]
        else:  # Linux
            system_fonts = [
                '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
                '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc'
            ]
        
        for font_path in system_fonts:
            if os.path.exists(font_path):
                return font_path
        
        # 使用matplotlib查找字体
        try:
            font_list = fm.findSystemFonts()
            if font_list:
                return font_list[0]
        except:
            pass
        
        return None
    
    font_path = get_system_font_path()
    print(f"检测到字体: {font_path}")
    
    # 测试matplotlib字体
    try:
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']
        
        fig, ax = plt.subplots(figsize=(6, 4))
        ax.text(0.5, 0.5, 'Font Test - 字体测试', ha='center', va='center', fontsize=16)
        ax.set_title('Matplotlib Font Test')
        plt.savefig('matplotlib_font_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("[成功] matplotlib字体测试通过")
        matplotlib_ok = True
    except Exception as e:
        print(f"[失败] matplotlib字体测试失败: {e}")
        matplotlib_ok = False
    
    # 测试WordCloud字体
    try:
        # 创建测试词云
        test_words = {
            'test': 0.8,
            'font': 0.6,
            'wordcloud': 0.7,
            'system': 0.5,
            'default': 0.4
        }
        
        wordcloud = WordCloud(
            width=400, height=200,
            background_color='white',
            font_path=font_path,
            max_words=10,
            random_state=42
        ).generate_from_frequencies(test_words)
        
        plt.figure(figsize=(8, 4))
        plt.imshow(wordcloud, interpolation='bilinear')
        plt.axis('off')
        plt.title('WordCloud Font Test')
        plt.savefig('wordcloud_font_test.png', dpi=150, bbox_inches='tight')
        plt.close()
        print("[成功] WordCloud字体测试通过")
        wordcloud_ok = True
    except Exception as e:
        print(f"[失败] WordCloud字体测试失败: {e}")
        wordcloud_ok = False
    
    # 总体评估
    print("\n" + "=" * 50)
    print("字体修复效果评估:")
    
    if has_font_fix and has_wordcloud_fix:
        print("[成功] 字体修复代码已正确添加")
    else:
        print("[警告] 字体修复代码可能不完整")
    
    if matplotlib_ok:
        print("[成功] matplotlib字体工作正常")
    else:
        print("[警告] matplotlib字体可能有问题")
    
    if wordcloud_ok:
        print("[成功] WordCloud字体工作正常，词云应该能正确显示")
    else:
        print("[警告] WordCloud字体可能有问题")
    
    if has_encoding_fix:
        print("[成功] 编码问题已修复")
    else:
        print("[警告] 编码修复可能不完整")
    
    # 使用建议
    print("\n使用建议:")
    if has_font_fix and has_wordcloud_fix and matplotlib_ok:
        print("推荐使用: 研报情感分析完整版_字体修复版.ipynb")
        print("特点:")
        print("  - 使用系统默认字体，兼容性更好")
        print("  - 词云图应该能正确显示文字（不再是圈圈）")
        print("  - 编码问题已解决")
        print("  - 所有图表都能正常显示")
    else:
        print("建议检查系统字体配置或使用备用方案")
    
    return has_font_fix and has_wordcloud_fix

def main():
    """主函数"""
    success = test_font_fix()
    
    print("\n" + "=" * 50)
    if success:
        print("字体修复验证通过！")
        print("现在词云图应该能正确显示文字，不再是圈圈了。")
    else:
        print("字体修复可能还有问题，建议进一步检查。")

if __name__ == "__main__":
    main()
