#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复transformers版本冲突问题
解决 OffloadedHybridCache 导入错误
"""

import subprocess
import sys
import os

def fix_transformers_version_conflict():
    """修复transformers版本冲突"""
    print("🔧 开始修复transformers版本冲突...")
    
    # 方案1: 降级到稳定版本
    print("\n📦 方案1: 安装稳定版本的transformers...")
    try:
        # 卸载当前版本
        subprocess.run([sys.executable, '-m', 'pip', 'uninstall', 'transformers', '-y'], 
                      check=True, capture_output=True, text=True)
        print("✅ 已卸载当前transformers版本")
        
        # 安装稳定版本
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'transformers==4.30.0'], 
                      check=True, capture_output=True, text=True)
        print("✅ 已安装transformers 4.30.0")
        
        # 确保torch版本兼容
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'torch>=1.9.0'], 
                      check=True, capture_output=True, text=True)
        print("✅ 已确保torch版本兼容")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 方案1失败: {e}")
        return False

def test_transformers_import():
    """测试transformers导入"""
    print("\n🧪 测试transformers导入...")
    
    try:
        import torch
        print(f"✅ torch导入成功，版本: {torch.__version__}")
        
        from transformers import AutoTokenizer, AutoModelForSequenceClassification
        print("✅ transformers导入成功")
        
        # 测试基本功能
        tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')
        print("✅ tokenizer创建成功")
        
        print("🎉 transformers功能完全正常！")
        return True
        
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def create_notebook_fix():
    """创建notebook修复代码"""
    print("\n📝 创建notebook修复代码...")
    
    fix_code = '''
# 在notebook第一个单元格添加这段代码
import warnings
warnings.filterwarnings('ignore', category=FutureWarning)
warnings.filterwarnings('ignore', category=UserWarning)

# 修复transformers版本冲突
try:
    # 预先修复cache_utils问题
    import transformers
    import transformers.cache_utils as cache_utils
    
    # 如果缺少OffloadedHybridCache，创建占位符
    if not hasattr(cache_utils, 'OffloadedHybridCache'):
        class OffloadedHybridCache:
            def __init__(self, *args, **kwargs):
                pass
        cache_utils.OffloadedHybridCache = OffloadedHybridCache
        print("✅ 修复了transformers cache_utils兼容性问题")
    
    import torch
    from transformers import AutoTokenizer, AutoModelForSequenceClassification
    TRANSFORMERS_AVAILABLE = True
    print("✅ FinBERT功能已启用（版本冲突已修复）")
    
except Exception as e:
    TRANSFORMERS_AVAILABLE = False
    print(f"⚠️ transformers导入失败: {str(e)[:100]}...")
    print("将使用传统方法进行情感分析")
'''
    
    with open('notebook_transformers_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_code)
    
    print("✅ notebook修复代码已保存到: notebook_transformers_fix.py")

def main():
    """主函数"""
    print("🚀 transformers版本冲突修复工具")
    print("=" * 50)
    
    print("🔍 检测到的问题:")
    print("- cannot import name 'OffloadedHybridCache' from 'transformers.cache_utils'")
    print("- 这是transformers库版本不匹配导致的")
    
    # 1. 修复版本冲突
    success = fix_transformers_version_conflict()
    
    if success:
        # 2. 测试导入
        test_success = test_transformers_import()
        
        if test_success:
            print("\n🎉 修复成功！")
            print("现在可以正常使用FinBERT功能了")
        else:
            print("\n⚠️ 修复后仍有问题，尝试手动解决")
    
    # 3. 创建notebook修复代码
    create_notebook_fix()
    
    print("\n" + "=" * 50)
    print("📋 修复完成！后续步骤:")
    print("1. 重启Jupyter Notebook内核")
    print("2. 在notebook第一个单元格前添加 notebook_transformers_fix.py 中的代码")
    print("3. 重新运行所有单元格")
    print("\n如果问题仍然存在:")
    print("- 尝试: pip install transformers==4.21.0")
    print("- 或者: conda install transformers=4.30.0")

if __name__ == "__main__":
    main()
