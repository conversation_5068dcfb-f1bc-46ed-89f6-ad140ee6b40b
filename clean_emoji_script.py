#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理notebook中的表情符号
"""

import json
import re

def clean_emojis_from_notebook(input_file, output_file):
    """从notebook中清理表情符号"""
    
    # 读取notebook
    with open(input_file, 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # 表情符号映射
    emoji_map = {
        '✅': '[成功]',
        '❌': '[失败]',
        '⚠️': '[警告]',
        '📄': '[文档]',
        '🔄': '[处理]',
        '📝': '[文本]',
        '✂️': '[分词]',
        '📊': '[统计]',
        '🔍': '[搜索]',
        '📚': '[词典]',
        '📈': '[结果]',
        '🎨': '[可视化]',
        '🚀': '[开始]',
        '🎉': '[完成]',
        '💡': '[提示]',
        '🎯': '[目标]',
        '🔧': '[修复]',
        '📋': '[报告]',
        '🆘': '[帮助]',
        '🎊': '[庆祝]',
        '📁': '[文件]',
        '💭': '[思考]',
        '🤖': '[AI]',
        '🧪': '[测试]',
        '🔬': '[分析]',
        '📦': '[包]',
        '⭐': '[星]',
        '🌟': '[亮星]',
        '🔥': '[火]',
        '💪': '[强]',
        '👍': '[赞]',
        '👎': '[踩]',
        '🎪': '[娱乐]',
        '🎭': '[戏剧]',
        '🎪': '[马戏]',
        '🎨': '[艺术]',
        '🎵': '[音乐]',
        '🎶': '[音符]',
        '🎸': '[吉他]',
        '🎹': '[钢琴]',
        '🎺': '[喇叭]',
        '🎻': '[小提琴]',
        '🥁': '[鼓]',
        '🎤': '[麦克风]',
        '🎧': '[耳机]',
        '📻': '[收音机]',
        '📺': '[电视]',
        '📷': '[相机]',
        '📸': '[拍照]',
        '📹': '[摄像]',
        '🎬': '[电影]',
        '🎮': '[游戏]',
        '🕹️': '[手柄]',
        '🎯': '[靶心]',
        '🎲': '[骰子]',
        '🃏': '[扑克]',
        '🎴': '[花牌]',
        '🀄': '[麻将]',
        '🎳': '[保龄球]',
        '⚽': '[足球]',
        '🏀': '[篮球]',
        '🏈': '[橄榄球]',
        '⚾': '[棒球]',
        '🎾': '[网球]',
        '🏐': '[排球]',
        '🏉': '[橄榄球]',
        '🎱': '[台球]',
        '🏓': '[乒乓球]',
        '🏸': '[羽毛球]',
        '🥅': '[球门]',
        '⛳': '[高尔夫]',
        '🏹': '[弓箭]',
        '🎣': '[钓鱼]',
        '🥊': '[拳击]',
        '🥋': '[武术]',
        '🎿': '[滑雪]',
        '⛷️': '[滑雪者]',
        '🏂': '[滑雪板]',
        '🏋️': '[举重]',
        '🤼': '[摔跤]',
        '🤸': '[体操]',
        '⛹️': '[篮球]',
        '🤺': '[击剑]',
        '🏇': '[赛马]',
        '⛷️': '[滑雪]',
        '🏌️': '[高尔夫]',
        '🏄': '[冲浪]',
        '🚣': '[划船]',
        '🏊': '[游泳]',
        '⛹️': '[运动]',
        '🏋️': '[健身]',
        '🚴': '[骑行]',
        '🚵': '[山地车]',
        '🤸': '[翻跟头]',
        '🤼': '[摔跤]',
        '🤽': '[水球]',
        '🤾': '[手球]',
        '🤹': '[杂技]'
    }
    
    def clean_text(text):
        """清理文本中的表情符号"""
        if not isinstance(text, str):
            return text
        
        # 替换已知的表情符号
        for emoji, replacement in emoji_map.items():
            text = text.replace(emoji, replacement)
        
        # 移除其他Unicode表情符号
        # 这个正则表达式匹配大部分Unicode表情符号
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+", flags=re.UNICODE)
        
        text = emoji_pattern.sub('', text)
        
        return text
    
    # 处理notebook中的每个cell
    for cell in notebook['cells']:
        if 'source' in cell:
            # 处理source内容
            if isinstance(cell['source'], list):
                cell['source'] = [clean_text(line) for line in cell['source']]
            else:
                cell['source'] = clean_text(cell['source'])
        
        # 处理输出内容
        if 'outputs' in cell:
            for output in cell['outputs']:
                if 'text' in output:
                    if isinstance(output['text'], list):
                        output['text'] = [clean_text(line) for line in output['text']]
                    else:
                        output['text'] = clean_text(output['text'])
    
    # 保存清理后的notebook
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)
    
    print(f"已清理表情符号并保存到: {output_file}")

def main():
    """主函数"""
    print("开始清理notebook中的表情符号...")
    
    # 清理原版文件
    clean_emojis_from_notebook('研报情感分析完整版.ipynb', '研报情感分析完整版_无表情符号.ipynb')
    
    # 如果修复版存在，也清理它
    try:
        clean_emojis_from_notebook('研报情感分析完整版_修复版.ipynb', '研报情感分析完整版_修复版_无表情符号.ipynb')
        print("修复版也已清理完成")
    except FileNotFoundError:
        print("修复版文件不存在，跳过")
    
    print("表情符号清理完成！")

if __name__ == "__main__":
    main()
