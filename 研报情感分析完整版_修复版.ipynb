# 基础库
import os
import sys
import time
import re
import warnings
from datetime import datetime
from collections import Counter, defaultdict
import concurrent.futures
from typing import List, Tuple, Dict, Optional

# 数据处理
import pandas as pd
import numpy as np

# PDF处理
import pdfplumber
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("警告: PyMuPDF不可用，将使用其他PDF提取方法")

# 文本处理
import jieba
import jieba.analyse
from tqdm import tqdm

# TextRank
try:
    from textrank4zh import TextRank4Keyword, TextRank4Sentence
    TEXTRANK4ZH_AVAILABLE = True
except ImportError:
    TEXTRANK4ZH_AVAILABLE = False
    print("警告: textrank4zh不可用，将使用jieba的TextRank")

# 机器学习
from sklearn.feature_extraction.text import TfidfVectorizer

# 情感分析库
try:
    from snownlp import SnowNLP
    SNOWNLP_AVAILABLE = True
except ImportError:
    SNOWNLP_AVAILABLE = False
    print("警告: SnowNLP不可用，将使用其他情感分析方法")

# 可视化
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from wordcloud import WordCloud

# 设置matplotlib字体，支持中文显示
import platform
system = platform.system()

# 根据操作系统设置中文字体
if system == 'Windows':
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']
elif system == 'Darwin':  # macOS
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'sans-serif']
else:  # Linux
    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'sans-serif']

plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 测试中文字体
try:
    fig, ax = plt.subplots(figsize=(1, 1))
    ax.text(0.5, 0.5, '测试中文', ha='center', va='center')
    plt.close(fig)
    print("✅ 中文字体设置成功")
    CHINESE_FONT_AVAILABLE = True
except Exception as e:
    print(f"⚠️ 中文字体设置可能有问题: {e}，将使用英文标签")
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']
    CHINESE_FONT_AVAILABLE = False

# 忽略警告
warnings.filterwarnings('ignore')

print("✅ 所有必要的库已导入完成")
print(f"PyMuPDF可用: {PYMUPDF_AVAILABLE}")
print(f"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}")
print(f"SnowNLP可用: {SNOWNLP_AVAILABLE}")
print(f"中文字体可用: {CHINESE_FONT_AVAILABLE}")
print("📊 将使用多种传统方法进行情感分析对比")

def extract_text_with_pymupdf(pdf_path: str) -> Tuple[str, List[str]]:
    """
    使用PyMuPDF提取PDF文本
    
    参数:
        pdf_path: PDF文件路径
    
    返回:
        (完整文本, 按页分割的文本列表)
    """
    if not PYMUPDF_AVAILABLE:
        return None, []
    
    try:
        doc = fitz.open(pdf_path)
        all_text = ""
        page_texts = []
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            text = page.get_text()
            
            if text.strip():
                all_text += text + "\n"
                page_texts.append(text)
        
        doc.close()
        return all_text, page_texts
        
    except Exception as e:
        print(f"PyMuPDF提取失败: {e}")
        return None, []

def extract_text_with_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:
    """
    使用pdfplumber提取PDF文本和表格
    
    参数:
        pdf_path: PDF文件路径
    
    返回:
        (完整文本, 表格列表)
    """
    try:
        all_text = ""
        all_tables = []
        
        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                # 提取文本
                text = page.extract_text()
                if text:
                    all_text += text + "\n"
                
                # 提取表格
                tables = page.extract_tables()
                for table in tables:
                    if table and len(table) > 1:
                        try:
                            df = pd.DataFrame(table[1:], columns=table[0])
                            all_tables.append(df)
                        except Exception as e:
                            print(f"表格处理失败: {e}")
        
        return all_text, all_tables
        
    except Exception as e:
        print(f"pdfplumber提取失败: {e}")
        return "", []

def extract_text_and_tables_from_pdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:
    """
    从PDF文件中提取文本和表格，使用多种方法确保提取完整性
    
    参数:
        pdf_path: PDF文件路径
    
    返回:
        (提取的文本, 表格列表)
    """
    print(f"📄 开始提取PDF文件: {os.path.basename(pdf_path)}")
    
    # 方法1: PyMuPDF提取文本
    pymupdf_text, _ = extract_text_with_pymupdf(pdf_path)
    
    # 方法2: pdfplumber提取文本和表格
    pdfplumber_text, tables = extract_text_with_pdfplumber(pdf_path)
    
    # 选择最佳文本提取结果
    if pymupdf_text and len(pymupdf_text.strip()) > len(pdfplumber_text.strip()):
        best_text = pymupdf_text
        print(f"✅ 使用PyMuPDF提取的文本 (长度: {len(best_text)} 字符)")
    else:
        best_text = pdfplumber_text
        print(f"✅ 使用pdfplumber提取的文本 (长度: {len(best_text)} 字符)")
    
    print(f"📊 提取到 {len(tables)} 个表格")
    
    return best_text, tables

print("✅ PDF文本提取模块已定义")

def load_stopwords(stopwords_path: str) -> set:
    """
    加载停用词
    
    参数:
        stopwords_path: 停用词文件路径
    
    返回:
        停用词集合
    """
    stopwords = set()
    
    # 默认停用词
    default_stopwords = {
        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',
        '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'
    }
    stopwords.update(default_stopwords)
    
    # 从文件加载停用词
    if os.path.exists(stopwords_path):
        try:
            with open(stopwords_path, 'r', encoding='utf-8') as f:
                for line in f:
                    word = line.strip()
                    if word:
                        stopwords.add(word)
            print(f"✅ 从文件加载了 {len(stopwords)} 个停用词")
        except Exception as e:
            print(f"⚠️ 加载停用词文件失败: {e}，使用默认停用词")
    else:
        print(f"⚠️ 停用词文件不存在: {stopwords_path}，使用默认停用词")
    
    return stopwords

def clean_text(text: str) -> str:
    """
    清洗文本，去除特殊字符等
    
    参数:
        text: 待清洗的文本
    
    返回:
        清洗后的文本
    """
    # 去除URL
    text = re.sub(r'https?://\S+|www\.\S+', '', text)
    
    # 去除HTML标签
    text = re.sub(r'<.*?>', '', text)
    
    # 去除邮箱
    text = re.sub(r'\S*@\S*\s?', '', text)
    
    # 保留中文、英文、数字和基本标点
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9.,，。、；：''\"\"（）()？?!！\s]+', ' ', text)
    
    # 去除多余的空白字符
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def preprocess_text(text: str, stopwords_path: str, min_word_len: int = 2) -> Tuple[List[str], str]:
    """
    文本预处理：分词、去停用词、过滤
    
    参数:
        text: 待处理的文本
        stopwords_path: 停用词文件路径
        min_word_len: 最小词长度
    
    返回:
        (过滤后的词列表, 过滤后的文本)
    """
    print("🔄 开始文本预处理...")
    
    if not text or len(text.strip()) == 0:
        print("❌ 输入文本为空")
        return [], ""
    
    # 清洗文本
    cleaned_text = clean_text(text)
    print(f"📝 文本清洗完成，长度: {len(cleaned_text)} 字符")
    
    # 加载停用词
    stopwords = load_stopwords(stopwords_path)
    
    # 分词
    print("✂️ 开始分词...")
    words = jieba.lcut(cleaned_text)
    print(f"📊 分词完成，共 {len(words)} 个词")
    
    # 过滤词语
    filtered_words = []
    for word in words:
        word = word.strip()
        if (len(word) >= min_word_len and 
            word not in stopwords and 
            not word.isdigit() and 
            not re.match(r'^[\W_]+$', word)):
            filtered_words.append(word)
    
    # 重新组合文本
    filtered_text = ' '.join(filtered_words)
    
    print(f"✅ 文本预处理完成，过滤后共 {len(filtered_words)} 个有效词")
    
    return filtered_words, filtered_text

print("✅ 文本预处理模块已定义")

def extract_keywords_textrank4zh(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    使用TextRank4zh提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    if not TEXTRANK4ZH_AVAILABLE:
        return []
    
    try:
        tr4w = TextRank4Keyword()
        tr4w.analyze(text=text, lower=True, window=2)
        keywords = tr4w.get_keywords(num=num_keywords, word_min_len=2)
        return [(item.word, item.weight) for item in keywords]
    except Exception as e:
        print(f"TextRank4zh提取失败: {e}")
        return []

def extract_keywords_jieba(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    使用jieba的TextRank提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    try:
        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)
        return list(keywords)
    except Exception as e:
        print(f"jieba TextRank提取失败: {e}")
        return []

def extract_keywords_tfidf(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    使用TF-IDF提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    try:
        # 分词
        words = jieba.lcut(text)
        text_processed = ' '.join(words)
        
        # TF-IDF
        vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))
        tfidf_matrix = vectorizer.fit_transform([text_processed])
        
        # 获取特征名和权重
        feature_names = vectorizer.get_feature_names_out()
        tfidf_scores = tfidf_matrix.toarray()[0]
        
        # 排序并返回前num_keywords个
        word_scores = list(zip(feature_names, tfidf_scores))
        word_scores.sort(key=lambda x: x[1], reverse=True)
        
        return word_scores[:num_keywords]
    except Exception as e:
        print(f"TF-IDF提取失败: {e}")
        return []

def extract_keywords(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:
    """
    综合多种方法提取关键词
    
    参数:
        text: 待提取关键词的文本
        num_keywords: 提取的关键词数量
    
    返回:
        关键词列表，每个元素为(词, 权重)元组
    """
    print("🔍 开始提取关键词...")
    
    all_keywords = defaultdict(float)
    
    # 方法1: TextRank4zh
    if TEXTRANK4ZH_AVAILABLE:
        keywords_tr4zh = extract_keywords_textrank4zh(text, num_keywords)
        if keywords_tr4zh:
            print(f"✅ TextRank4zh提取到 {len(keywords_tr4zh)} 个关键词")
            for word, weight in keywords_tr4zh:
                all_keywords[word] += weight * 0.4
    
    # 方法2: jieba TextRank
    keywords_jieba = extract_keywords_jieba(text, num_keywords)
    if keywords_jieba:
        print(f"✅ jieba TextRank提取到 {len(keywords_jieba)} 个关键词")
        for word, weight in keywords_jieba:
            all_keywords[word] += weight * 0.3
    
    # 方法3: TF-IDF
    keywords_tfidf = extract_keywords_tfidf(text, num_keywords)
    if keywords_tfidf:
        print(f"✅ TF-IDF提取到 {len(keywords_tfidf)} 个关键词")
        for word, weight in keywords_tfidf:
            all_keywords[word] += weight * 0.3
    
    # 合并并排序
    if not all_keywords:
        print("❌ 所有方法都未能提取到关键词")
        return []
    
    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)
    result = sorted_keywords[:num_keywords]
    
    print(f"✅ 关键词提取完成，共 {len(result)} 个关键词")
    
    return result

print("✅ 关键词提取模块已定义")

def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:
    """
    加载情感词典（支持多种编码）
    
    参数:
        positive_path: 正面词典文件路径
        negative_path: 负面词典文件路径
    
    返回:
        情感词典，正面词为正值，负面词为负值
    """
    sentiment_dict = {}
    
    # 默认情感词
    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']
    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']
    
    # 添加默认词
    for word in default_positive:
        sentiment_dict[word] = 1.0
    for word in default_negative:
        sentiment_dict[word] = -1.0
    
    # 加载正面词典
    if os.path.exists(positive_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if positive_path.endswith('.csv'):
                    df = pd.read_csv(positive_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = 1.0
                else:
                    with open(positive_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = 1.0
                print(f"✅ 使用 {encoding} 编码加载正面词典: {positive_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载正面词典: {positive_path}")
    
    # 加载负面词典
    if os.path.exists(negative_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if negative_path.endswith('.csv'):
                    df = pd.read_csv(negative_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = -1.0
                else:
                    with open(negative_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = -1.0
                print(f"✅ 使用 {encoding} 编码加载负面词典: {negative_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载负面词典: {negative_path}")
    
    print(f"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词")
    return sentiment_dict

print("✅ 修复版情感词典加载函数已定义")

def sentiment_analysis_by_dict(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]], List[Tuple[str, float]]]:
    """
    基于情感词典的情感分析
    """
    print("📊 开始基于词典的情感分析...")
    
    words = jieba.lcut(text)
    total_score = 0
    matched_words = []
    
    for word in words:
        if word in sentiment_dict:
            score = sentiment_dict[word]
            total_score += score
            matched_words.append((word, score))
    
    overall_score = total_score / len(words) if words else 0
    
    # 计算关键词情感得分
    keyword_scores = []
    for keyword, weight in keywords:
        score = sentiment_dict.get(keyword, 0)
        keyword_scores.append((keyword, score, weight))
    
    print(f"✅ 词典情感分析完成，整体得分: {overall_score:.4f}，匹配 {len(matched_words)} 个情感词")
    return overall_score, keyword_scores, matched_words

def sentiment_analysis_by_snownlp(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:
    """
    使用SnowNLP进行情感分析
    """
    try:
        from snownlp import SnowNLP
        s = SnowNLP(text)
        overall_score = 2 * s.sentiments - 1  # 转换为[-1,1]范围
        
        keyword_scores = []
        for keyword, weight in keywords:
            s = SnowNLP(keyword)
            score = 2 * s.sentiments - 1
            keyword_scores.append((keyword, score, weight))
        
        print(f"✅ SnowNLP情感分析完成，整体得分: {overall_score:.4f}")
        return overall_score, keyword_scores
    except ImportError:
        print("⚠️ SnowNLP不可用，使用简单规则")
        return sentiment_analysis_by_rules(text, keywords)

def sentiment_analysis_by_rules(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:
    """
    使用简单规则进行情感分析
    """
    print("📊 开始基于规则的情感分析...")
    
    positive_words = ['好', '优秀', '增长', '上涨', '盈利', '成功', '发展', '提升', '改善', '积极', '正面', '乐观']
    negative_words = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '下降', '减少', '恶化', '消极', '负面']
    
    words = jieba.lcut(text)
    pos_count = sum(1 for word in words if any(pw in word for pw in positive_words))
    neg_count = sum(1 for word in words if any(nw in word for nw in negative_words))
    
    overall_score = (pos_count - neg_count) / len(words) if words else 0
    
    keyword_scores = []
    for keyword, weight in keywords:
        pos_in_keyword = sum(1 for pw in positive_words if pw in keyword)
        neg_in_keyword = sum(1 for nw in negative_words if nw in keyword)
        
        if pos_in_keyword > 0 or neg_in_keyword > 0:
            score = (pos_in_keyword - neg_in_keyword) / max(1, pos_in_keyword + neg_in_keyword)
        else:
            score = 0
        
        keyword_scores.append((keyword, score, weight))
    
    print(f"✅ 规则情感分析完成，整体得分: {overall_score:.4f}")
    return overall_score, keyword_scores

# 定义颜色方案
COLORS = {
    'positive': '#2E8B57',  # 海绿色
    'negative': '#DC143C',  # 深红色
    'neutral': '#708090',   # 石板灰
    'background': '#F8F9FA',
    'highlight': '#4169E1',  # 皇家蓝
    'secondary': '#FFD700'   # 金色
}

def create_sentiment_comparison_chart(dict_score: float, snownlp_score: float, rules_score: float) -> None:
    """
    创建多种情感分析方法对比图表（修复版）
    """
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 智能选择标签语言
    if CHINESE_FONT_AVAILABLE:
        methods = ['词典分析', 'SnowNLP分析', '规则分析']
        title = '多种情感分析方法对比'
        ylabel = '情感得分\n(负面 < 0 < 正面)'
    else:
        methods = ['Dictionary Analysis', 'SnowNLP Analysis', 'Rules Analysis']
        title = 'Multiple Sentiment Analysis Methods Comparison'
        ylabel = 'Sentiment Score\n(Negative < 0 < Positive)'
    
    scores = [dict_score, snownlp_score, rules_score]
    colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral'] for s in scores]
    
    bars = ax.bar(methods, scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1)
    
    # 添加数值标签
    for bar, score in zip(bars, scores):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + (0.02 if height >= 0 else -0.05),
                f'{score:.3f}', ha='center', va='bottom' if height >= 0 else 'top',
                fontweight='bold', fontsize=12)
    
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
    ax.set_ylabel(ylabel, fontsize=12, fontweight='bold')
    
    y_max = max(abs(min(scores)), abs(max(scores)), 0.5)
    ax.set_ylim(-y_max*1.2, y_max*1.2)
    ax.grid(axis='y', alpha=0.3, linestyle='--')
    
    plt.xticks(rotation=45, ha='right')
    plt.tight_layout()
    plt.show()

print("✅ 完整的情感分析和可视化模块已定义")

def analyze_research_report(pdf_path: str, stopwords_path: str = 'data/stopwords.txt', 
                          positive_dict_path: str = 'data/正面词典.csv', 
                          negative_dict_path: str = 'data/负面词典.csv') -> Dict:
    """
    完整的研报情感分析流程（修复版）
    """
    print("🚀 开始研报情感分析...")
    print("=" * 60)
    
    results = {}
    
    # 1. PDF文本提取
    print("\n📄 步骤1: PDF文本提取")
    text, tables = extract_text_and_tables_from_pdf(pdf_path)
    
    if not text or len(text.strip()) < 100:
        print("❌ PDF文本提取失败或文本过短")
        return {}
    
    results['original_text'] = text
    results['tables'] = tables
    
    # 2. 文本预处理
    print("\n🔄 步骤2: 文本预处理")
    filtered_words, filtered_text = preprocess_text(text, stopwords_path)
    
    if not filtered_words:
        print("❌ 文本预处理失败")
        return {}
    
    results['filtered_text'] = filtered_text
    results['filtered_words'] = filtered_words
    
    # 3. 关键词提取
    print("\n🔍 步骤3: 关键词提取")
    keywords = extract_keywords(filtered_text, num_keywords=20)
    
    if not keywords:
        print("❌ 关键词提取失败")
        return {}
    
    results['keywords'] = keywords
    
    # 4. 加载情感词典
    print("\n📚 步骤4: 加载情感词典")
    sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)
    results['sentiment_dict'] = sentiment_dict
    
    # 5. 多种情感分析方法
    print("\n📊 步骤5: 多种情感分析方法对比")
    
    # 方法1: 词典法
    dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(text, keywords, sentiment_dict)
    results['dict_analysis'] = {
        'overall_score': dict_score,
        'keyword_scores': dict_keywords,
        'matched_words': matched_words
    }
    
    # 方法2: SnowNLP法
    snownlp_score, snownlp_keywords = sentiment_analysis_by_snownlp(text, keywords)
    results['snownlp_analysis'] = {
        'overall_score': snownlp_score,
        'keyword_scores': snownlp_keywords
    }
    
    # 方法3: 规则法
    rules_score, rules_keywords = sentiment_analysis_by_rules(text, keywords)
    results['rules_analysis'] = {
        'overall_score': rules_score,
        'keyword_scores': rules_keywords
    }
    
    # 6. 结果汇总
    print("\n📈 步骤6: 结果汇总")
    print("=" * 40)
    print(f"词典法情感得分: {dict_score:.4f}")
    print(f"SnowNLP情感得分: {snownlp_score:.4f}")
    print(f"规则法情感得分: {rules_score:.4f}")
    
    # 计算平均得分
    avg_score = (dict_score + snownlp_score + rules_score) / 3
    results['average_score'] = avg_score
    print(f"平均情感得分: {avg_score:.4f}")
    
    # 情感倾向判断
    if avg_score > 0.1:
        sentiment_label = "正面"
    elif avg_score < -0.1:
        sentiment_label = "负面"
    else:
        sentiment_label = "中性"
    
    results['sentiment_label'] = sentiment_label
    print(f"整体情感倾向: {sentiment_label}")
    print("=" * 40)
    
    # 7. 可视化
    print("\n🎨 步骤7: 生成可视化图表")
    create_sentiment_comparison_chart(dict_score, snownlp_score, rules_score)
    
    print("\n✅ 研报情感分析完成！")
    
    return results

print("✅ 主要执行流程已定义")

# 查找PDF文件
pdf_files = []
data_dir = 'data'

if os.path.exists(data_dir):
    for file in os.listdir(data_dir):
        if file.endswith('.pdf'):
            pdf_files.append(os.path.join(data_dir, file))

if pdf_files:
    pdf_path = pdf_files[0]  # 使用第一个找到的PDF文件
    print(f"✅ 找到PDF文件: {pdf_path}")
    
    # 执行完整的情感分析流程
    results = analyze_research_report(pdf_path)
    
    if results:
        print("\n📊 分析结果详情:")
        print(f"原始文本长度: {len(results.get('original_text', ''))} 字符")
        print(f"关键词数量: {len(results.get('keywords', []))}")
        print(f"情感词典大小: {len(results.get('sentiment_dict', {}))}")
        
        # 显示前10个关键词
        keywords = results.get('keywords', [])
        if keywords:
            print("\n🔍 前10个关键词:")
            for i, (word, weight) in enumerate(keywords[:10], 1):
                print(f"  {i}. {word} (权重: {weight:.4f})")
        
        # 显示匹配的情感词
        matched_words = results.get('dict_analysis', {}).get('matched_words', [])
        if matched_words:
            print(f"\n💭 匹配到的情感词 (前20个):")
            for i, (word, score) in enumerate(matched_words[:20], 1):
                emotion = "正面" if score > 0 else "负面"
                print(f"  {i}. {word} ({emotion}: {score:.1f})")
        
        print("\n🎯 最终结论:")
        print(f"  • 整体情感倾向: {results.get('sentiment_label', '未知')}")
        print(f"  • 平均情感得分: {results.get('average_score', 0):.4f}")
        
        # 各方法得分对比
        dict_score = results.get('dict_analysis', {}).get('overall_score', 0)
        snownlp_score = results.get('snownlp_analysis', {}).get('overall_score', 0)
        rules_score = results.get('rules_analysis', {}).get('overall_score', 0)
        
        print(f"  • 词典法得分: {dict_score:.4f}")
        print(f"  • SnowNLP得分: {snownlp_score:.4f}")
        print(f"  • 规则法得分: {rules_score:.4f}")
        
    else:
        print("❌ 分析失败，请检查PDF文件和数据文件")
        
else:
    print("⚠️ 未找到PDF文件，请将PDF文件放在data目录下")
    print("\n📝 演示模式: 使用示例文本进行分析")
    
    # 使用示例文本进行演示
    demo_text = """
    该公司在本季度表现优秀，营业收入实现了显著增长，达到了历史新高。
    公司的盈利能力持续提升，净利润同比增长25%，超出市场预期。
    管理层对未来发展前景保持乐观态度，预计下一季度将继续保持强劲的增长势头。
    然而，公司也面临一些挑战和风险，包括市场竞争加剧和原材料成本上升的压力。
    尽管存在这些困难，公司凭借其创新能力和市场领先地位，有信心克服当前的挑战。
    投资者对公司的长期发展前景充满信心，股价在近期表现稳定。
    """
    
    print("\n🔄 开始演示分析...")
    
    # 文本预处理
    filtered_words, filtered_text = preprocess_text(demo_text, 'data/stopwords.txt')
    
    # 关键词提取
    keywords = extract_keywords(filtered_text, num_keywords=10)
    
    # 加载情感词典
    sentiment_dict = load_sentiment_dict('data/正面词典.csv', 'data/负面词典.csv')
    
    # 情感分析
    dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(demo_text, keywords, sentiment_dict)
    snownlp_score, snownlp_keywords = sentiment_analysis_by_snownlp(demo_text, keywords)
    rules_score, rules_keywords = sentiment_analysis_by_rules(demo_text, keywords)
    
    # 结果展示
    avg_score = (dict_score + snownlp_score + rules_score) / 3
    sentiment_label = "正面" if avg_score > 0.1 else "负面" if avg_score < -0.1 else "中性"
    
    print("\n📊 演示分析结果:")
    print(f"词典法得分: {dict_score:.4f}")
    print(f"SnowNLP得分: {snownlp_score:.4f}")
    print(f"规则法得分: {rules_score:.4f}")
    print(f"平均得分: {avg_score:.4f}")
    print(f"整体情感倾向: {sentiment_label}")
    
    # 生成可视化
    create_sentiment_comparison_chart(dict_score, snownlp_score, rules_score)
    
    print("\n✅ 演示分析完成！")

print("\n🎉 修复版情感分析系统运行完成！")
print("\n📋 修复内容总结:")
print("  ✅ 解决了情感词典编码问题（支持GBK、UTF-8等多种编码）")
print("  ✅ 修复了中文字体显示问题")
print("  ✅ 优化了容错机制和错误处理")
print("  ✅ 增强了可视化效果")
print("\n💡 现在您可以正常使用所有功能了！")

# 设置文件路径
PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'

# 检查文件是否存在
if not os.path.exists(PDF_PATH):
    print(f"❌ PDF文件不存在: {PDF_PATH}")
    print("请确保PDF文件在正确的位置")
else:
    print(f"✅ 找到PDF文件: {PDF_PATH}")
    
    # 执行完整分析
    analysis_results = analyze_research_report(
        pdf_path=PDF_PATH,
        stopwords_path=STOPWORDS_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH
    )
    
    if analysis_results:
        print("\n🎨 开始生成可视化图表...")
        
        # 1. 情感分析方法对比图
        create_sentiment_comparison_chart(
            analysis_results['dict_analysis']['overall_score'],
            analysis_results['snownlp_analysis']['overall_score'],
            analysis_results['rules_analysis']['overall_score']
        )
        
        # 2. 关键词情感分布图（词典法）
        create_keyword_sentiment_chart(
            analysis_results['dict_analysis']['keyword_scores'],
            "Dictionary Method"
        )
        
        # 3. 关键词情感分布图（SnowNLP法）
        create_keyword_sentiment_chart(
            analysis_results['snownlp_analysis']['keyword_scores'],
            "SnowNLP Method"
        )
        
        # 4. 关键词云图
        create_wordcloud(analysis_results['keywords'])
        
        # 5. 显示详细结果
        print("\n📋 详细分析结果:")
        print("=" * 50)
        
        print(f"\n📄 文本统计:")
        print(f"  • 原始文本长度: {len(analysis_results['original_text'])} 字符")
        print(f"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个")
        print(f"  • 提取表格数: {len(analysis_results['tables'])} 个")
        
        print(f"\n🔍 关键词 (前10个):")
        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):
            print(f"  {i:2d}. {word} (权重: {weight:.4f})")
        
        print(f"\n📊 情感分析结果:")
        print(f"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}")
        print(f"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}")
        print(f"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}")
        print(f"  • 平均得分: {analysis_results['average_score']:.4f}")
        print(f"  • 情感倾向: {analysis_results['sentiment_label']}")
        
        print(f"\n💡 匹配的情感词 (前10个):")
        matched_words = analysis_results['dict_analysis']['matched_words'][:10]
        for word, score in matched_words:
            sentiment = "正面" if score > 0 else "负面"
            print(f"  • {word} ({sentiment}, {score:+.1f})")
        
        print("\n✅ 所有分析和可视化完成！")
    else:
        print("❌ 分析失败，请检查输入文件")

# 设置文件路径
PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'

# 检查文件是否存在
if not os.path.exists(PDF_PATH):
    print(f"❌ PDF文件不存在: {PDF_PATH}")
    print("请确保PDF文件在正确的位置")
else:
    print(f"✅ 找到PDF文件: {PDF_PATH}")
    
    # 执行完整分析
    analysis_results = analyze_research_report(
        pdf_path=PDF_PATH,
        stopwords_path=STOPWORDS_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH
    )
    
    if analysis_results:
        print("\n🎨 开始生成可视化图表...")
        
        # 1. 情感分析方法对比图
        create_sentiment_comparison_chart(
            analysis_results['dict_analysis']['overall_score'],
            analysis_results['snownlp_analysis']['overall_score'],
            analysis_results['rules_analysis']['overall_score']
        )
        
        # 2. 关键词情感分布图（词典法）
        create_keyword_sentiment_chart(
            analysis_results['dict_analysis']['keyword_scores'],
            "Dictionary Method"
        )
        
        # 3. 关键词情感分布图（SnowNLP法）
        create_keyword_sentiment_chart(
            analysis_results['snownlp_analysis']['keyword_scores'],
            "SnowNLP Method"
        )
        
        # 4. 关键词云图
        create_wordcloud(analysis_results['keywords'])
        
        # 5. 显示详细结果
        print("\n📋 详细分析结果:")
        print("=" * 50)
        
        print(f"\n📄 文本统计:")
        print(f"  • 原始文本长度: {len(analysis_results['original_text'])} 字符")
        print(f"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个")
        print(f"  • 提取表格数: {len(analysis_results['tables'])} 个")
        
        print(f"\n🔍 关键词 (前10个):")
        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):
            print(f"  {i:2d}. {word} (权重: {weight:.4f})")
        
        print(f"\n📊 情感分析结果:")
        print(f"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}")
        print(f"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}")
        print(f"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}")
        print(f"  • 平均得分: {analysis_results['average_score']:.4f}")
        print(f"  • 情感倾向: {analysis_results['sentiment_label']}")
        
        print(f"\n💡 匹配的情感词 (前10个):")
        matched_words = analysis_results['dict_analysis']['matched_words'][:10]
        for word, score in matched_words:
            sentiment = "正面" if score > 0 else "负面"
            print(f"  • {word} ({sentiment}, {score:+.1f})")
        
        print("\n✅ 所有分析和可视化完成！")
    else:
        print("❌ 分析失败，请检查输入文件")

# 设置文件路径
PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'

# 检查文件是否存在
if not os.path.exists(PDF_PATH):
    print(f"❌ PDF文件不存在: {PDF_PATH}")
    print("请确保PDF文件在正确的位置")
else:
    print(f"✅ 找到PDF文件: {PDF_PATH}")
    
    # 执行完整分析
    analysis_results = analyze_research_report(
        pdf_path=PDF_PATH,
        stopwords_path=STOPWORDS_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH
    )
    
    if analysis_results:
        print("\n🎨 开始生成可视化图表...")
        
        # 1. 情感分析方法对比图
        create_sentiment_comparison_chart(
            analysis_results['dict_analysis']['overall_score'],
            analysis_results['snownlp_analysis']['overall_score'],
            analysis_results['rules_analysis']['overall_score']
        )
        
        # 2. 关键词情感分布图（词典法）
        create_keyword_sentiment_chart(
            analysis_results['dict_analysis']['keyword_scores'],
            "Dictionary Method"
        )
        
        # 3. 关键词情感分布图（SnowNLP法）
        create_keyword_sentiment_chart(
            analysis_results['snownlp_analysis']['keyword_scores'],
            "SnowNLP Method"
        )
        
        # 4. 关键词云图
        create_wordcloud(analysis_results['keywords'])
        
        # 5. 显示详细结果
        print("\n📋 详细分析结果:")
        print("=" * 50)
        
        print(f"\n📄 文本统计:")
        print(f"  • 原始文本长度: {len(analysis_results['original_text'])} 字符")
        print(f"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个")
        print(f"  • 提取表格数: {len(analysis_results['tables'])} 个")
        
        print(f"\n🔍 关键词 (前10个):")
        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):
            print(f"  {i:2d}. {word} (权重: {weight:.4f})")
        
        print(f"\n📊 情感分析结果:")
        print(f"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}")
        print(f"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}")
        print(f"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}")
        print(f"  • 平均得分: {analysis_results['average_score']:.4f}")
        print(f"  • 情感倾向: {analysis_results['sentiment_label']}")
        
        print(f"\n💡 匹配的情感词 (前10个):")
        matched_words = analysis_results['dict_analysis']['matched_words'][:10]
        for word, score in matched_words:
            sentiment = "正面" if score > 0 else "负面"
            print(f"  • {word} ({sentiment}, {score:+.1f})")
        
        print("\n✅ 所有分析和可视化完成！")
    else:
        print("❌ 分析失败，请检查输入文件")

# 设置文件路径
PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'

# 检查文件是否存在
if not os.path.exists(PDF_PATH):
    print(f"❌ PDF文件不存在: {PDF_PATH}")
    print("请确保PDF文件在正确的位置")
else:
    print(f"✅ 找到PDF文件: {PDF_PATH}")
    
    # 执行完整分析
    analysis_results = analyze_research_report(
        pdf_path=PDF_PATH,
        stopwords_path=STOPWORDS_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH
    )
    
    if analysis_results:
        print("\n🎨 开始生成可视化图表...")
        
        # 1. 情感分析方法对比图
        create_sentiment_comparison_chart(
            analysis_results['dict_analysis']['overall_score'],
            analysis_results['snownlp_analysis']['overall_score'],
            analysis_results['rules_analysis']['overall_score']
        )
        
        # 2. 关键词情感分布图（词典法）
        create_keyword_sentiment_chart(
            analysis_results['dict_analysis']['keyword_scores'],
            "Dictionary Method"
        )
        
        # 3. 关键词情感分布图（SnowNLP法）
        create_keyword_sentiment_chart(
            analysis_results['snownlp_analysis']['keyword_scores'],
            "SnowNLP Method"
        )
        
        # 4. 关键词云图
        create_wordcloud(analysis_results['keywords'])
        
        # 5. 显示详细结果
        print("\n📋 详细分析结果:")
        print("=" * 50)
        
        print(f"\n📄 文本统计:")
        print(f"  • 原始文本长度: {len(analysis_results['original_text'])} 字符")
        print(f"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个")
        print(f"  • 提取表格数: {len(analysis_results['tables'])} 个")
        
        print(f"\n🔍 关键词 (前10个):")
        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):
            print(f"  {i:2d}. {word} (权重: {weight:.4f})")
        
        print(f"\n📊 情感分析结果:")
        print(f"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}")
        print(f"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}")
        print(f"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}")
        print(f"  • 平均得分: {analysis_results['average_score']:.4f}")
        print(f"  • 情感倾向: {analysis_results['sentiment_label']}")
        
        print(f"\n💡 匹配的情感词 (前10个):")
        matched_words = analysis_results['dict_analysis']['matched_words'][:10]
        for word, score in matched_words:
            sentiment = "正面" if score > 0 else "负面"
            print(f"  • {word} ({sentiment}, {score:+.1f})")
        
        print("\n✅ 所有分析和可视化完成！")
    else:
        print("❌ 分析失败，请检查输入文件")

# 设置文件路径
PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'

# 检查文件是否存在
if not os.path.exists(PDF_PATH):
    print(f"❌ PDF文件不存在: {PDF_PATH}")
    print("请确保PDF文件在正确的位置")
else:
    print(f"✅ 找到PDF文件: {PDF_PATH}")
    
    # 执行完整分析
    analysis_results = analyze_research_report(
        pdf_path=PDF_PATH,
        stopwords_path=STOPWORDS_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH
    )
    
    if analysis_results:
        print("\n🎨 开始生成可视化图表...")
        
        # 1. 情感分析方法对比图
        create_sentiment_comparison_chart(
            analysis_results['dict_analysis']['overall_score'],
            analysis_results['snownlp_analysis']['overall_score'],
            analysis_results['rules_analysis']['overall_score']
        )
        
        # 2. 关键词情感分布图（词典法）
        create_keyword_sentiment_chart(
            analysis_results['dict_analysis']['keyword_scores'],
            "Dictionary Method"
        )
        
        # 3. 关键词情感分布图（SnowNLP法）
        create_keyword_sentiment_chart(
            analysis_results['snownlp_analysis']['keyword_scores'],
            "SnowNLP Method"
        )
        
        # 4. 关键词云图
        create_wordcloud(analysis_results['keywords'])
        
        # 5. 显示详细结果
        print("\n📋 详细分析结果:")
        print("=" * 50)
        
        print(f"\n📄 文本统计:")
        print(f"  • 原始文本长度: {len(analysis_results['original_text'])} 字符")
        print(f"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个")
        print(f"  • 提取表格数: {len(analysis_results['tables'])} 个")
        
        print(f"\n🔍 关键词 (前10个):")
        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):
            print(f"  {i:2d}. {word} (权重: {weight:.4f})")
        
        print(f"\n📊 情感分析结果:")
        print(f"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}")
        print(f"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}")
        print(f"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}")
        print(f"  • 平均得分: {analysis_results['average_score']:.4f}")
        print(f"  • 情感倾向: {analysis_results['sentiment_label']}")
        
        print(f"\n💡 匹配的情感词 (前10个):")
        matched_words = analysis_results['dict_analysis']['matched_words'][:10]
        for word, score in matched_words:
            sentiment = "正面" if score > 0 else "负面"
            print(f"  • {word} ({sentiment}, {score:+.1f})")
        
        print("\n✅ 所有分析和可视化完成！")
    else:
        print("❌ 分析失败，请检查输入文件")

# 设置文件路径
PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'

# 检查文件是否存在
if not os.path.exists(PDF_PATH):
    print(f"❌ PDF文件不存在: {PDF_PATH}")
    print("请确保PDF文件在正确的位置")
else:
    print(f"✅ 找到PDF文件: {PDF_PATH}")
    
    # 执行完整分析
    analysis_results = analyze_research_report(
        pdf_path=PDF_PATH,
        stopwords_path=STOPWORDS_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH
    )
    
    if analysis_results:
        print("\n🎨 开始生成可视化图表...")
        
        # 1. 情感分析方法对比图
        create_sentiment_comparison_chart(
            analysis_results['dict_analysis']['overall_score'],
            analysis_results['snownlp_analysis']['overall_score'],
            analysis_results['rules_analysis']['overall_score']
        )
        
        # 2. 关键词情感分布图（词典法）
        create_keyword_sentiment_chart(
            analysis_results['dict_analysis']['keyword_scores'],
            "Dictionary Method"
        )
        
        # 3. 关键词情感分布图（SnowNLP法）
        create_keyword_sentiment_chart(
            analysis_results['snownlp_analysis']['keyword_scores'],
            "SnowNLP Method"
        )
        
        # 4. 关键词云图
        create_wordcloud(analysis_results['keywords'])
        
        # 5. 显示详细结果
        print("\n📋 详细分析结果:")
        print("=" * 50)
        
        print(f"\n📄 文本统计:")
        print(f"  • 原始文本长度: {len(analysis_results['original_text'])} 字符")
        print(f"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个")
        print(f"  • 提取表格数: {len(analysis_results['tables'])} 个")
        
        print(f"\n🔍 关键词 (前10个):")
        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):
            print(f"  {i:2d}. {word} (权重: {weight:.4f})")
        
        print(f"\n📊 情感分析结果:")
        print(f"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}")
        print(f"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}")
        print(f"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}")
        print(f"  • 平均得分: {analysis_results['average_score']:.4f}")
        print(f"  • 情感倾向: {analysis_results['sentiment_label']}")
        
        print(f"\n💡 匹配的情感词 (前10个):")
        matched_words = analysis_results['dict_analysis']['matched_words'][:10]
        for word, score in matched_words:
            sentiment = "正面" if score > 0 else "负面"
            print(f"  • {word} ({sentiment}, {score:+.1f})")
        
        print("\n✅ 所有分析和可视化完成！")
    else:
        print("❌ 分析失败，请检查输入文件")

# 设置文件路径
PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'
STOPWORDS_PATH = 'data/stopwords.txt'
POSITIVE_DICT_PATH = 'data/正面词典.csv'
NEGATIVE_DICT_PATH = 'data/负面词典.csv'

# 检查文件是否存在
if not os.path.exists(PDF_PATH):
    print(f"❌ PDF文件不存在: {PDF_PATH}")
    print("请确保PDF文件在正确的位置")
else:
    print(f"✅ 找到PDF文件: {PDF_PATH}")
    
    # 执行完整分析
    analysis_results = analyze_research_report(
        pdf_path=PDF_PATH,
        stopwords_path=STOPWORDS_PATH,
        positive_dict_path=POSITIVE_DICT_PATH,
        negative_dict_path=NEGATIVE_DICT_PATH
    )
    
    if analysis_results:
        print("\n🎨 开始生成可视化图表...")
        
        # 1. 情感分析方法对比图
        create_sentiment_comparison_chart(
            analysis_results['dict_analysis']['overall_score'],
            analysis_results['snownlp_analysis']['overall_score'],
            analysis_results['rules_analysis']['overall_score']
        )
        
        # 2. 关键词情感分布图（词典法）
        create_keyword_sentiment_chart(
            analysis_results['dict_analysis']['keyword_scores'],
            "Dictionary Method"
        )
        
        # 3. 关键词情感分布图（SnowNLP法）
        create_keyword_sentiment_chart(
            analysis_results['snownlp_analysis']['keyword_scores'],
            "SnowNLP Method"
        )
        
        # 4. 关键词云图
        create_wordcloud(analysis_results['keywords'])
        
        # 5. 显示详细结果
        print("\n📋 详细分析结果:")
        print("=" * 50)
        
        print(f"\n📄 文本统计:")
        print(f"  • 原始文本长度: {len(analysis_results['original_text'])} 字符")
        print(f"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个")
        print(f"  • 提取表格数: {len(analysis_results['tables'])} 个")
        
        print(f"\n🔍 关键词 (前10个):")
        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):
            print(f"  {i:2d}. {word} (权重: {weight:.4f})")
        
        print(f"\n📊 情感分析结果:")
        print(f"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}")
        print(f"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}")
        print(f"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}")
        print(f"  • 平均得分: {analysis_results['average_score']:.4f}")
        print(f"  • 情感倾向: {analysis_results['sentiment_label']}")
        
        print(f"\n💡 匹配的情感词 (前10个):")
        matched_words = analysis_results['dict_analysis']['matched_words'][:10]
        for word, score in matched_words:
            sentiment = "正面" if score > 0 else "负面"
            print(f"  • {word} ({sentiment}, {score:+.1f})")
        
        print("\n✅ 所有分析和可视化完成！")
    else:
        print("❌ 分析失败，请检查输入文件")