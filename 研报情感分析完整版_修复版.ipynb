{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 研报情感分析系统 - 完整修复版\n", "\n", "本notebook整合了研报情感分析的完整流程，包括：\n", "1. PDF文本提取\n", "2. 文本预处理\n", "3. 关键词提取\n", "4. 多种情感分析方法对比\n", "5. 结果可视化\n", "\n", "**修复内容：**\n", "- ✅ 解决了情感词典编码问题（支持GBK、UTF-8等多种编码）\n", "- ✅ 修复了中文字体显示问题\n", "- ✅ 优化了容错机制\n", "\n", "所有结果都在notebook内展示，无需外部文件。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库和模块"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 中文字体设置成功\n", "✅ 所有必要的库已导入完成\n", "PyMuPDF可用: True\n", "TextRank4zh可用: True\n", "SnowNLP可用: True\n", "中文字体可用: True\n", "📊 将使用多种传统方法进行情感分析对比\n"]}], "source": ["# 基础库\n", "import os\n", "import sys\n", "import time\n", "import re\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import concurrent.futures\n", "from typing import List, Tuple, Dict, Optional\n", "\n", "# 数据处理\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF处理\n", "import pdfplumber\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"警告: PyMuPDF不可用，将使用其他PDF提取方法\")\n", "\n", "# 文本处理\n", "import jieba\n", "import jieba.analyse\n", "from tqdm import tqdm\n", "\n", "# TextRank\n", "try:\n", "    from textrank4zh import TextRank4Keyword, TextRank4Sentence\n", "    TEXTRANK4ZH_AVAILABLE = True\n", "except ImportError:\n", "    TEXTRANK4ZH_AVAILABLE = False\n", "    print(\"警告: textrank4zh不可用，将使用jieba的TextRank\")\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 情感分析库\n", "try:\n", "    from snownlp import SnowNLP\n", "    SNOWNLP_AVAILABLE = True\n", "except ImportError:\n", "    SNOWNLP_AVAILABLE = False\n", "    print(\"警告: SnowNLP不可用，将使用其他情感分析方法\")\n", "\n", "# 可视化\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "import seaborn as sns\n", "from wordcloud import WordCloud\n", "\n", "# 设置matplotlib字体，支持中文显示\n", "import platform\n", "system = platform.system()\n", "\n", "# 根据操作系统设置中文字体\n", "if system == 'Windows':\n", "    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']\n", "elif system == 'Darwin':  # macOS\n", "    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'sans-serif']\n", "else:  # Linux\n", "    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'sans-serif']\n", "\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['font.size'] = 12\n", "\n", "# 测试中文字体\n", "try:\n", "    fig, ax = plt.subplots(figsize=(1, 1))\n", "    ax.text(0.5, 0.5, '测试中文', ha='center', va='center')\n", "    plt.close(fig)\n", "    print(\"✅ 中文字体设置成功\")\n", "    CHINESE_FONT_AVAILABLE = True\n", "except Exception as e:\n", "    print(f\"⚠️ 中文字体设置可能有问题: {e}，将使用英文标签\")\n", "    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']\n", "    CHINESE_FONT_AVAILABLE = False\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ 所有必要的库已导入完成\")\n", "print(f\"PyMuPDF可用: {PYMUPDF_AVAILABLE}\")\n", "print(f\"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}\")\n", "print(f\"SnowNLP可用: {SNOWNLP_AVAILABLE}\")\n", "print(f\"中文字体可用: {CHINESE_FONT_AVAILABLE}\")\n", "print(\"📊 将使用多种传统方法进行情感分析对比\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. PDF文本提取模块"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ PDF文本提取模块已定义\n"]}], "source": ["def extract_text_with_pymupdf(pdf_path: str) -> Tuple[str, List[str]]:\n", "    \"\"\"\n", "    使用PyMuPDF提取PDF文本\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 按页分割的文本列表)\n", "    \"\"\"\n", "    if not PYMUPDF_AVAILABLE:\n", "        return None, []\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        all_text = \"\"\n", "        page_texts = []\n", "        \n", "        for page_num in range(len(doc)):\n", "            page = doc[page_num]\n", "            text = page.get_text()\n", "            \n", "            if text.strip():\n", "                all_text += text + \"\\n\"\n", "                page_texts.append(text)\n", "        \n", "        doc.close()\n", "        return all_text, page_texts\n", "        \n", "    except Exception as e:\n", "        print(f\"PyMuPDF提取失败: {e}\")\n", "        return None, []\n", "\n", "def extract_text_with_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    使用pdfplumber提取PDF文本和表格\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 表格列表)\n", "    \"\"\"\n", "    try:\n", "        all_text = \"\"\n", "        all_tables = []\n", "        \n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            for page_num, page in enumerate(pdf.pages):\n", "                # 提取文本\n", "                text = page.extract_text()\n", "                if text:\n", "                    all_text += text + \"\\n\"\n", "                \n", "                # 提取表格\n", "                tables = page.extract_tables()\n", "                for table in tables:\n", "                    if table and len(table) > 1:\n", "                        try:\n", "                            df = pd.DataFrame(table[1:], columns=table[0])\n", "                            all_tables.append(df)\n", "                        except Exception as e:\n", "                            print(f\"表格处理失败: {e}\")\n", "        \n", "        return all_text, all_tables\n", "        \n", "    except Exception as e:\n", "        print(f\"pdfplumber提取失败: {e}\")\n", "        return \"\", []\n", "\n", "def extract_text_and_tables_from_pdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    从PDF文件中提取文本和表格，使用多种方法确保提取完整性\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (提取的文本, 表格列表)\n", "    \"\"\"\n", "    print(f\"📄 开始提取PDF文件: {os.path.basename(pdf_path)}\")\n", "    \n", "    # 方法1: PyMuPDF提取文本\n", "    pymupdf_text, _ = extract_text_with_pymupdf(pdf_path)\n", "    \n", "    # 方法2: pdfplumber提取文本和表格\n", "    pdfplumber_text, tables = extract_text_with_pdfplumber(pdf_path)\n", "    \n", "    # 选择最佳文本提取结果\n", "    if pymupdf_text and len(pymupdf_text.strip()) > len(pdfplumber_text.strip()):\n", "        best_text = pymupdf_text\n", "        print(f\"✅ 使用PyMuPDF提取的文本 (长度: {len(best_text)} 字符)\")\n", "    else:\n", "        best_text = pdfplumber_text\n", "        print(f\"✅ 使用pdfplumber提取的文本 (长度: {len(best_text)} 字符)\")\n", "    \n", "    print(f\"📊 提取到 {len(tables)} 个表格\")\n", "    \n", "    return best_text, tables\n", "\n", "print(\"✅ PDF文本提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 文本预处理模块"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 文本预处理模块已定义\n"]}], "source": ["def load_stopwords(stopwords_path: str) -> set:\n", "    \"\"\"\n", "    加载停用词\n", "    \n", "    参数:\n", "        stopwords_path: 停用词文件路径\n", "    \n", "    返回:\n", "        停用词集合\n", "    \"\"\"\n", "    stopwords = set()\n", "    \n", "    # 默认停用词\n", "    default_stopwords = {\n", "        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',\n", "        '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'\n", "    }\n", "    stopwords.update(default_stopwords)\n", "    \n", "    # 从文件加载停用词\n", "    if os.path.exists(stopwords_path):\n", "        try:\n", "            with open(stopwords_path, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    word = line.strip()\n", "                    if word:\n", "                        stopwords.add(word)\n", "            print(f\"✅ 从文件加载了 {len(stopwords)} 个停用词\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载停用词文件失败: {e}，使用默认停用词\")\n", "    else:\n", "        print(f\"⚠️ 停用词文件不存在: {stopwords_path}，使用默认停用词\")\n", "    \n", "    return stopwords\n", "\n", "def clean_text(text: str) -> str:\n", "    \"\"\"\n", "    清洗文本，去除特殊字符等\n", "    \n", "    参数:\n", "        text: 待清洗的文本\n", "    \n", "    返回:\n", "        清洗后的文本\n", "    \"\"\"\n", "    # 去除URL\n", "    text = re.sub(r'https?://\\S+|www\\.\\S+', '', text)\n", "    \n", "    # 去除HTML标签\n", "    text = re.sub(r'<.*?>', '', text)\n", "    \n", "    # 去除邮箱\n", "    text = re.sub(r'\\S*@\\S*\\s?', '', text)\n", "    \n", "    # 保留中文、英文、数字和基本标点\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9.,，。、；：''\\\"\\\"（）()？?!！\\s]+', ' ', text)\n", "    \n", "    # 去除多余的空白字符\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    return text\n", "\n", "def preprocess_text(text: str, stopwords_path: str, min_word_len: int = 2) -> Tuple[List[str], str]:\n", "    \"\"\"\n", "    文本预处理：分词、去停用词、过滤\n", "    \n", "    参数:\n", "        text: 待处理的文本\n", "        stopwords_path: 停用词文件路径\n", "        min_word_len: 最小词长度\n", "    \n", "    返回:\n", "        (过滤后的词列表, 过滤后的文本)\n", "    \"\"\"\n", "    print(\"🔄 开始文本预处理...\")\n", "    \n", "    if not text or len(text.strip()) == 0:\n", "        print(\"❌ 输入文本为空\")\n", "        return [], \"\"\n", "    \n", "    # 清洗文本\n", "    cleaned_text = clean_text(text)\n", "    print(f\"📝 文本清洗完成，长度: {len(cleaned_text)} 字符\")\n", "    \n", "    # 加载停用词\n", "    stopwords = load_stopwords(stopwords_path)\n", "    \n", "    # 分词\n", "    print(\"✂️ 开始分词...\")\n", "    words = jieba.lcut(cleaned_text)\n", "    print(f\"📊 分词完成，共 {len(words)} 个词\")\n", "    \n", "    # 过滤词语\n", "    filtered_words = []\n", "    for word in words:\n", "        word = word.strip()\n", "        if (len(word) >= min_word_len and \n", "            word not in stopwords and \n", "            not word.isdigit() and \n", "            not re.match(r'^[\\W_]+$', word)):\n", "            filtered_words.append(word)\n", "    \n", "    # 重新组合文本\n", "    filtered_text = ' '.join(filtered_words)\n", "    \n", "    print(f\"✅ 文本预处理完成，过滤后共 {len(filtered_words)} 个有效词\")\n", "    \n", "    return filtered_words, filtered_text\n", "\n", "print(\"✅ 文本预处理模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 关键词提取模块"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 关键词提取模块已定义\n"]}], "source": ["def extract_keywords_textrank4zh(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TextRank4zh提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    if not TEXTRANK4ZH_AVAILABLE:\n", "        return []\n", "    \n", "    try:\n", "        tr4w = TextRank4Keyword()\n", "        tr4w.analyze(text=text, lower=True, window=2)\n", "        keywords = tr4w.get_keywords(num=num_keywords, word_min_len=2)\n", "        return [(item.word, item.weight) for item in keywords]\n", "    except Exception as e:\n", "        print(f\"TextRank4zh提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_jieba(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用jieba的TextRank提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)\n", "        return list(keywords)\n", "    except Exception as e:\n", "        print(f\"jieba TextRank提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_tfidf(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TF-IDF提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        # 分词\n", "        words = jieba.lcut(text)\n", "        text_processed = ' '.join(words)\n", "        \n", "        # TF-IDF\n", "        vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))\n", "        tfidf_matrix = vectorizer.fit_transform([text_processed])\n", "        \n", "        # 获取特征名和权重\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        tfidf_scores = tfidf_matrix.toarray()[0]\n", "        \n", "        # 排序并返回前num_keywords个\n", "        word_scores = list(zip(feature_names, tfidf_scores))\n", "        word_scores.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        return word_scores[:num_keywords]\n", "    except Exception as e:\n", "        print(f\"TF-IDF提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    综合多种方法提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    print(\"🔍 开始提取关键词...\")\n", "    \n", "    all_keywords = defaultdict(float)\n", "    \n", "    # 方法1: TextRank4zh\n", "    if TEXTRANK4ZH_AVAILABLE:\n", "        keywords_tr4zh = extract_keywords_textrank4zh(text, num_keywords)\n", "        if keywords_tr4zh:\n", "            print(f\"✅ TextRank4zh提取到 {len(keywords_tr4zh)} 个关键词\")\n", "            for word, weight in keywords_tr4zh:\n", "                all_keywords[word] += weight * 0.4\n", "    \n", "    # 方法2: <PERSON><PERSON><PERSON> TextRank\n", "    keywords_jieba = extract_keywords_jieba(text, num_keywords)\n", "    if keywords_jieba:\n", "        print(f\"✅ jieba TextRank提取到 {len(keywords_jieba)} 个关键词\")\n", "        for word, weight in keywords_jieba:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 方法3: TF-IDF\n", "    keywords_tfidf = extract_keywords_tfidf(text, num_keywords)\n", "    if keywords_tfidf:\n", "        print(f\"✅ TF-IDF提取到 {len(keywords_tfidf)} 个关键词\")\n", "        for word, weight in keywords_tfidf:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 合并并排序\n", "    if not all_keywords:\n", "        print(\"❌ 所有方法都未能提取到关键词\")\n", "        return []\n", "    \n", "    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)\n", "    result = sorted_keywords[:num_keywords]\n", "    \n", "    print(f\"✅ 关键词提取完成，共 {len(result)} 个关键词\")\n", "    \n", "    return result\n", "\n", "print(\"✅ 关键词提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 情感分析模块（修复版）"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 修复版情感词典加载函数已定义\n"]}], "source": ["def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:\n", "    \"\"\"\n", "    加载情感词典（支持多种编码）\n", "    \n", "    参数:\n", "        positive_path: 正面词典文件路径\n", "        negative_path: 负面词典文件路径\n", "    \n", "    返回:\n", "        情感词典，正面词为正值，负面词为负值\n", "    \"\"\"\n", "    sentiment_dict = {}\n", "    \n", "    # 默认情感词\n", "    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']\n", "    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']\n", "    \n", "    # 添加默认词\n", "    for word in default_positive:\n", "        sentiment_dict[word] = 1.0\n", "    for word in default_negative:\n", "        sentiment_dict[word] = -1.0\n", "    \n", "    # 加载正面词典\n", "    if os.path.exists(positive_path):\n", "        loaded = False\n", "        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "            try:\n", "                if positive_path.endswith('.csv'):\n", "                    df = pd.read_csv(positive_path, encoding=encoding)\n", "                    if not df.empty:\n", "                        words = df.iloc[:, 0].tolist()\n", "                        for word in words:\n", "                            if isinstance(word, str) and word.strip():\n", "                                sentiment_dict[word.strip()] = 1.0\n", "                else:\n", "                    with open(positive_path, 'r', encoding=encoding) as f:\n", "                        for line in f:\n", "                            word = line.strip()\n", "                            if word:\n", "                                sentiment_dict[word] = 1.0\n", "                print(f\"✅ 使用 {encoding} 编码加载正面词典: {positive_path}\")\n", "                loaded = True\n", "                break\n", "            except (UnicodeDecodeError, pd.errors.EmptyDataError):\n", "                continue\n", "        \n", "        if not loaded:\n", "            print(f\"⚠️ 无法加载正面词典: {positive_path}\")\n", "    \n", "    # 加载负面词典\n", "    if os.path.exists(negative_path):\n", "        loaded = False\n", "        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\n", "            try:\n", "                if negative_path.endswith('.csv'):\n", "                    df = pd.read_csv(negative_path, encoding=encoding)\n", "                    if not df.empty:\n", "                        words = df.iloc[:, 0].tolist()\n", "                        for word in words:\n", "                            if isinstance(word, str) and word.strip():\n", "                                sentiment_dict[word.strip()] = -1.0\n", "                else:\n", "                    with open(negative_path, 'r', encoding=encoding) as f:\n", "                        for line in f:\n", "                            word = line.strip()\n", "                            if word:\n", "                                sentiment_dict[word] = -1.0\n", "                print(f\"✅ 使用 {encoding} 编码加载负面词典: {negative_path}\")\n", "                loaded = True\n", "                break\n", "            except (UnicodeDecodeError, pd.errors.EmptyDataError):\n", "                continue\n", "        \n", "        if not loaded:\n", "            print(f\"⚠️ 无法加载负面词典: {negative_path}\")\n", "    \n", "    print(f\"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词\")\n", "    return sentiment_dict\n", "\n", "print(\"✅ 修复版情感词典加载函数已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 完整的情感分析和可视化模块"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 完整的情感分析和可视化模块已定义\n"]}], "source": ["def sentiment_analysis_by_dict(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]], List[Tuple[str, float]]]:\n", "    \"\"\"\n", "    基于情感词典的情感分析\n", "    \"\"\"\n", "    print(\"📊 开始基于词典的情感分析...\")\n", "    \n", "    words = jieba.lcut(text)\n", "    total_score = 0\n", "    matched_words = []\n", "    \n", "    for word in words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            total_score += score\n", "            matched_words.append((word, score))\n", "    \n", "    overall_score = total_score / len(words) if words else 0\n", "    \n", "    # 计算关键词情感得分\n", "    keyword_scores = []\n", "    for keyword, weight in keywords:\n", "        score = sentiment_dict.get(keyword, 0)\n", "        keyword_scores.append((keyword, score, weight))\n", "    \n", "    print(f\"✅ 词典情感分析完成，整体得分: {overall_score:.4f}，匹配 {len(matched_words)} 个情感词\")\n", "    return overall_score, keyword_scores, matched_words\n", "\n", "def sentiment_analysis_by_snownlp(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    使用SnowNLP进行情感分析\n", "    \"\"\"\n", "    try:\n", "        from snownlp import SnowNLP\n", "        s = SnowNLP(text)\n", "        overall_score = 2 * s.sentiments - 1  # 转换为[-1,1]范围\n", "        \n", "        keyword_scores = []\n", "        for keyword, weight in keywords:\n", "            s = SnowNLP(keyword)\n", "            score = 2 * s.sentiments - 1\n", "            keyword_scores.append((keyword, score, weight))\n", "        \n", "        print(f\"✅ SnowNLP情感分析完成，整体得分: {overall_score:.4f}\")\n", "        return overall_score, keyword_scores\n", "    except ImportError:\n", "        print(\"⚠️ SnowNLP不可用，使用简单规则\")\n", "        return sentiment_analysis_by_rules(text, keywords)\n", "\n", "def sentiment_analysis_by_rules(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    使用简单规则进行情感分析\n", "    \"\"\"\n", "    print(\"📊 开始基于规则的情感分析...\")\n", "    \n", "    positive_words = ['好', '优秀', '增长', '上涨', '盈利', '成功', '发展', '提升', '改善', '积极', '正面', '乐观']\n", "    negative_words = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '下降', '减少', '恶化', '消极', '负面']\n", "    \n", "    words = jieba.lcut(text)\n", "    pos_count = sum(1 for word in words if any(pw in word for pw in positive_words))\n", "    neg_count = sum(1 for word in words if any(nw in word for nw in negative_words))\n", "    \n", "    overall_score = (pos_count - neg_count) / len(words) if words else 0\n", "    \n", "    keyword_scores = []\n", "    for keyword, weight in keywords:\n", "        pos_in_keyword = sum(1 for pw in positive_words if pw in keyword)\n", "        neg_in_keyword = sum(1 for nw in negative_words if nw in keyword)\n", "        \n", "        if pos_in_keyword > 0 or neg_in_keyword > 0:\n", "            score = (pos_in_keyword - neg_in_keyword) / max(1, pos_in_keyword + neg_in_keyword)\n", "        else:\n", "            score = 0\n", "        \n", "        keyword_scores.append((keyword, score, weight))\n", "    \n", "    print(f\"✅ 规则情感分析完成，整体得分: {overall_score:.4f}\")\n", "    return overall_score, keyword_scores\n", "\n", "# 定义颜色方案\n", "COLORS = {\n", "    'positive': '#2E8B57',  # 海绿色\n", "    'negative': '#DC143C',  # 深红色\n", "    'neutral': '#708090',   # 石板灰\n", "    'background': '#F8F9FA',\n", "    'highlight': '#4169E1',  # 皇家蓝\n", "    'secondary': '#FFD700'   # 金色\n", "}\n", "\n", "def create_sentiment_comparison_chart(dict_score: float, snownlp_score: float, rules_score: float) -> None:\n", "    \"\"\"\n", "    创建多种情感分析方法对比图表（修复版）\n", "    \"\"\"\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    \n", "    # 智能选择标签语言\n", "    if CHINESE_FONT_AVAILABLE:\n", "        methods = ['词典分析', 'SnowNLP分析', '规则分析']\n", "        title = '多种情感分析方法对比'\n", "        ylabel = '情感得分\\n(负面 < 0 < 正面)'\n", "    else:\n", "        methods = ['Dictionary Analysis', 'SnowNLP Analysis', 'Rules Analysis']\n", "        title = 'Multiple Sentiment Analysis Methods Comparison'\n", "        ylabel = 'Sentiment Score\\n(Negative < 0 < Positive)'\n", "    \n", "    scores = [dict_score, snownlp_score, rules_score]\n", "    colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral'] for s in scores]\n", "    \n", "    bars = ax.bar(methods, scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1)\n", "    \n", "    # 添加数值标签\n", "    for bar, score in zip(bars, scores):\n", "        height = bar.get_height()\n", "        ax.text(bar.get_x() + bar.get_width()/2., height + (0.02 if height >= 0 else -0.05),\n", "                f'{score:.3f}', ha='center', va='bottom' if height >= 0 else 'top',\n", "                fontweight='bold', fontsize=12)\n", "    \n", "    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)\n", "    ax.set_title(title, fontsize=16, fontweight='bold', pad=20)\n", "    ax.set_ylabel(ylabel, fontsize=12, fontweight='bold')\n", "    \n", "    y_max = max(abs(min(scores)), abs(max(scores)), 0.5)\n", "    ax.set_ylim(-y_max*1.2, y_max*1.2)\n", "    ax.grid(axis='y', alpha=0.3, linestyle='--')\n", "    \n", "    plt.xticks(rotation=45, ha='right')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "print(\"✅ 完整的情感分析和可视化模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 主要执行流程"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 主要执行流程已定义\n"]}], "source": ["def analyze_research_report(pdf_path: str, stopwords_path: str = 'data/stopwords.txt', \n", "                          positive_dict_path: str = 'data/正面词典.csv', \n", "                          negative_dict_path: str = 'data/负面词典.csv') -> Dict:\n", "    \"\"\"\n", "    完整的研报情感分析流程（修复版）\n", "    \"\"\"\n", "    print(\"🚀 开始研报情感分析...\")\n", "    print(\"=\" * 60)\n", "    \n", "    results = {}\n", "    \n", "    # 1. PDF文本提取\n", "    print(\"\\n📄 步骤1: PDF文本提取\")\n", "    text, tables = extract_text_and_tables_from_pdf(pdf_path)\n", "    \n", "    if not text or len(text.strip()) < 100:\n", "        print(\"❌ PDF文本提取失败或文本过短\")\n", "        return {}\n", "    \n", "    results['original_text'] = text\n", "    results['tables'] = tables\n", "    \n", "    # 2. 文本预处理\n", "    print(\"\\n🔄 步骤2: 文本预处理\")\n", "    filtered_words, filtered_text = preprocess_text(text, stopwords_path)\n", "    \n", "    if not filtered_words:\n", "        print(\"❌ 文本预处理失败\")\n", "        return {}\n", "    \n", "    results['filtered_text'] = filtered_text\n", "    results['filtered_words'] = filtered_words\n", "    \n", "    # 3. 关键词提取\n", "    print(\"\\n🔍 步骤3: 关键词提取\")\n", "    keywords = extract_keywords(filtered_text, num_keywords=20)\n", "    \n", "    if not keywords:\n", "        print(\"❌ 关键词提取失败\")\n", "        return {}\n", "    \n", "    results['keywords'] = keywords\n", "    \n", "    # 4. 加载情感词典\n", "    print(\"\\n📚 步骤4: 加载情感词典\")\n", "    sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)\n", "    results['sentiment_dict'] = sentiment_dict\n", "    \n", "    # 5. 多种情感分析方法\n", "    print(\"\\n📊 步骤5: 多种情感分析方法对比\")\n", "    \n", "    # 方法1: 词典法\n", "    dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(text, keywords, sentiment_dict)\n", "    results['dict_analysis'] = {\n", "        'overall_score': dict_score,\n", "        'keyword_scores': dict_keywords,\n", "        'matched_words': matched_words\n", "    }\n", "    \n", "    # 方法2: SnowNL<PERSON>法\n", "    snownlp_score, snownlp_keywords = sentiment_analysis_by_snownlp(text, keywords)\n", "    results['snownlp_analysis'] = {\n", "        'overall_score': snownlp_score,\n", "        'keyword_scores': snownlp_keywords\n", "    }\n", "    \n", "    # 方法3: 规则法\n", "    rules_score, rules_keywords = sentiment_analysis_by_rules(text, keywords)\n", "    results['rules_analysis'] = {\n", "        'overall_score': rules_score,\n", "        'keyword_scores': rules_keywords\n", "    }\n", "    \n", "    # 6. 结果汇总\n", "    print(\"\\n📈 步骤6: 结果汇总\")\n", "    print(\"=\" * 40)\n", "    print(f\"词典法情感得分: {dict_score:.4f}\")\n", "    print(f\"SnowNLP情感得分: {snownlp_score:.4f}\")\n", "    print(f\"规则法情感得分: {rules_score:.4f}\")\n", "    \n", "    # 计算平均得分\n", "    avg_score = (dict_score + snownlp_score + rules_score) / 3\n", "    results['average_score'] = avg_score\n", "    print(f\"平均情感得分: {avg_score:.4f}\")\n", "    \n", "    # 情感倾向判断\n", "    if avg_score > 0.1:\n", "        sentiment_label = \"正面\"\n", "    elif avg_score < -0.1:\n", "        sentiment_label = \"负面\"\n", "    else:\n", "        sentiment_label = \"中性\"\n", "    \n", "    results['sentiment_label'] = sentiment_label\n", "    print(f\"整体情感倾向: {sentiment_label}\")\n", "    print(\"=\" * 40)\n", "    \n", "    # 7. 可视化\n", "    print(\"\\n🎨 步骤7: 生成可视化图表\")\n", "    create_sentiment_comparison_chart(dict_score, snownlp_score, rules_score)\n", "    \n", "    print(\"\\n✅ 研报情感分析完成！\")\n", "    \n", "    return results\n", "\n", "print(\"✅ 主要执行流程已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 执行分析和可视化（修复版）"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 找到PDF文件: data\\2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n", "🚀 开始研报情感分析...\n", "============================================================\n", "\n", "📄 步骤1: PDF文本提取\n", "📄 开始提取PDF文件: 2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "Building prefix dict from the default dictionary ...\n", "Loading model from cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\jieba.cache\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ 使用PyMuPDF提取的文本 (长度: 39084 字符)\n", "📊 提取到 101 个表格\n", "\n", "🔄 步骤2: 文本预处理\n", "🔄 开始文本预处理...\n", "📝 文本清洗完成，长度: 37024 字符\n", "✅ 从文件加载了 1317 个停用词\n", "✂️ 开始分词...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading model cost 3.425 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📊 分词完成，共 17998 个词\n", "✅ 文本预处理完成，过滤后共 8715 个有效词\n", "\n", "🔍 步骤3: 关键词提取\n", "🔍 开始提取关键词...\n", "TextRank4zh提取失败: module 'networkx' has no attribute 'from_numpy_matrix'\n", "✅ jieba TextRank提取到 20 个关键词\n", "✅ TF-IDF提取到 20 个关键词\n", "✅ 关键词提取完成，共 20 个关键词\n", "\n", "📚 步骤4: 加载情感词典\n", "✅ 使用 gbk 编码加载正面词典: data/正面词典.csv\n", "✅ 使用 gbk 编码加载负面词典: data/负面词典.csv\n", "📚 情感词典加载完成，共 2612 个词\n", "\n", "📊 步骤5: 多种情感分析方法对比\n", "📊 开始基于词典的情感分析...\n", "✅ 词典情感分析完成，整体得分: 0.0245，匹配 651 个情感词\n", "✅ SnowNLP情感分析完成，整体得分: 1.0000\n", "📊 开始基于规则的情感分析...\n", "✅ 规则情感分析完成，整体得分: 0.0008\n", "\n", "📈 步骤6: 结果汇总\n", "========================================\n", "词典法情感得分: 0.0245\n", "SnowNLP情感得分: 1.0000\n", "规则法情感得分: 0.0008\n", "平均情感得分: 0.3417\n", "整体情感倾向: 正面\n", "========================================\n", "\n", "🎨 步骤7: 生成可视化图表\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "✅ 研报情感分析完成！\n", "\n", "📊 分析结果详情:\n", "原始文本长度: 39084 字符\n", "关键词数量: 20\n", "情感词典大小: 2612\n", "\n", "🔍 前10个关键词:\n", "  1. 证券 (权重: 0.3959)\n", "  2. 服务 (权重: 0.3534)\n", "  3. 财富 (权重: 0.2977)\n", "  4. 金融 (权重: 0.2840)\n", "  5. 图表 (权重: 0.2409)\n", "  6. 业务 (权重: 0.2405)\n", "  7. 用户 (权重: 0.2218)\n", "  8. 产品 (权重: 0.2052)\n", "  9. 国金 (权重: 0.1938)\n", "  10. 指南针 (权重: 0.1440)\n", "\n", "💭 匹配到的情感词 (前20个):\n", "  1. 昌盛 (正面: 1.0)\n", "  2. 核心 (正面: 1.0)\n", "  3. 发展 (正面: 1.0)\n", "  4. 优势 (正面: 1.0)\n", "  5. 财富 (正面: 1.0)\n", "  6. 优势 (正面: 1.0)\n", "  7. 核心 (正面: 1.0)\n", "  8. 财富 (正面: 1.0)\n", "  9. 专注 (正面: 1.0)\n", "  10. 特色 (正面: 1.0)\n", "  11. 优势 (正面: 1.0)\n", "  12. 实现 (正面: 1.0)\n", "  13. 完整 (正面: 1.0)\n", "  14. 核心 (正面: 1.0)\n", "  15. 分化 (负面: -1.0)\n", "  16. 实现 (正面: 1.0)\n", "  17. 维护 (正面: 1.0)\n", "  18. 财富 (正面: 1.0)\n", "  19. 财富 (正面: 1.0)\n", "  20. 精准 (正面: 1.0)\n", "\n", "🎯 最终结论:\n", "  • 整体情感倾向: 正面\n", "  • 平均情感得分: 0.3417\n", "  • 词典法得分: 0.0245\n", "  • SnowNLP得分: 1.0000\n", "  • 规则法得分: 0.0008\n", "\n", "🎉 修复版情感分析系统运行完成！\n", "\n", "📋 修复内容总结:\n", "  ✅ 解决了情感词典编码问题（支持GBK、UTF-8等多种编码）\n", "  ✅ 修复了中文字体显示问题\n", "  ✅ 优化了容错机制和错误处理\n", "  ✅ 增强了可视化效果\n", "\n", "💡 现在您可以正常使用所有功能了！\n"]}], "source": ["# 查找PDF文件\n", "pdf_files = []\n", "data_dir = 'data'\n", "\n", "if os.path.exists(data_dir):\n", "    for file in os.listdir(data_dir):\n", "        if file.endswith('.pdf'):\n", "            pdf_files.append(os.path.join(data_dir, file))\n", "\n", "if pdf_files:\n", "    pdf_path = pdf_files[0]  # 使用第一个找到的PDF文件\n", "    print(f\"✅ 找到PDF文件: {pdf_path}\")\n", "    \n", "    # 执行完整的情感分析流程\n", "    results = analyze_research_report(pdf_path)\n", "    \n", "    if results:\n", "        print(\"\\n📊 分析结果详情:\")\n", "        print(f\"原始文本长度: {len(results.get('original_text', ''))} 字符\")\n", "        print(f\"关键词数量: {len(results.get('keywords', []))}\")\n", "        print(f\"情感词典大小: {len(results.get('sentiment_dict', {}))}\")\n", "        \n", "        # 显示前10个关键词\n", "        keywords = results.get('keywords', [])\n", "        if keywords:\n", "            print(\"\\n🔍 前10个关键词:\")\n", "            for i, (word, weight) in enumerate(keywords[:10], 1):\n", "                print(f\"  {i}. {word} (权重: {weight:.4f})\")\n", "        \n", "        # 显示匹配的情感词\n", "        matched_words = results.get('dict_analysis', {}).get('matched_words', [])\n", "        if matched_words:\n", "            print(f\"\\n💭 匹配到的情感词 (前20个):\")\n", "            for i, (word, score) in enumerate(matched_words[:20], 1):\n", "                emotion = \"正面\" if score > 0 else \"负面\"\n", "                print(f\"  {i}. {word} ({emotion}: {score:.1f})\")\n", "        \n", "        print(\"\\n🎯 最终结论:\")\n", "        print(f\"  • 整体情感倾向: {results.get('sentiment_label', '未知')}\")\n", "        print(f\"  • 平均情感得分: {results.get('average_score', 0):.4f}\")\n", "        \n", "        # 各方法得分对比\n", "        dict_score = results.get('dict_analysis', {}).get('overall_score', 0)\n", "        snownlp_score = results.get('snownlp_analysis', {}).get('overall_score', 0)\n", "        rules_score = results.get('rules_analysis', {}).get('overall_score', 0)\n", "        \n", "        print(f\"  • 词典法得分: {dict_score:.4f}\")\n", "        print(f\"  • SnowNLP得分: {snownlp_score:.4f}\")\n", "        print(f\"  • 规则法得分: {rules_score:.4f}\")\n", "        \n", "    else:\n", "        print(\"❌ 分析失败，请检查PDF文件和数据文件\")\n", "        \n", "else:\n", "    print(\"⚠️ 未找到PDF文件，请将PDF文件放在data目录下\")\n", "    print(\"\\n📝 演示模式: 使用示例文本进行分析\")\n", "    \n", "    # 使用示例文本进行演示\n", "    demo_text = \"\"\"\n", "    该公司在本季度表现优秀，营业收入实现了显著增长，达到了历史新高。\n", "    公司的盈利能力持续提升，净利润同比增长25%，超出市场预期。\n", "    管理层对未来发展前景保持乐观态度，预计下一季度将继续保持强劲的增长势头。\n", "    然而，公司也面临一些挑战和风险，包括市场竞争加剧和原材料成本上升的压力。\n", "    尽管存在这些困难，公司凭借其创新能力和市场领先地位，有信心克服当前的挑战。\n", "    投资者对公司的长期发展前景充满信心，股价在近期表现稳定。\n", "    \"\"\"\n", "    \n", "    print(\"\\n🔄 开始演示分析...\")\n", "    \n", "    # 文本预处理\n", "    filtered_words, filtered_text = preprocess_text(demo_text, 'data/stopwords.txt')\n", "    \n", "    # 关键词提取\n", "    keywords = extract_keywords(filtered_text, num_keywords=10)\n", "    \n", "    # 加载情感词典\n", "    sentiment_dict = load_sentiment_dict('data/正面词典.csv', 'data/负面词典.csv')\n", "    \n", "    # 情感分析\n", "    dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(demo_text, keywords, sentiment_dict)\n", "    snownlp_score, snownlp_keywords = sentiment_analysis_by_snownlp(demo_text, keywords)\n", "    rules_score, rules_keywords = sentiment_analysis_by_rules(demo_text, keywords)\n", "    \n", "    # 结果展示\n", "    avg_score = (dict_score + snownlp_score + rules_score) / 3\n", "    sentiment_label = \"正面\" if avg_score > 0.1 else \"负面\" if avg_score < -0.1 else \"中性\"\n", "    \n", "    print(\"\\n📊 演示分析结果:\")\n", "    print(f\"词典法得分: {dict_score:.4f}\")\n", "    print(f\"SnowNLP得分: {snownlp_score:.4f}\")\n", "    print(f\"规则法得分: {rules_score:.4f}\")\n", "    print(f\"平均得分: {avg_score:.4f}\")\n", "    print(f\"整体情感倾向: {sentiment_label}\")\n", "    \n", "    # 生成可视化\n", "    create_sentiment_comparison_chart(dict_score, snownlp_score, rules_score)\n", "    \n", "    print(\"\\n✅ 演示分析完成！\")\n", "\n", "print(\"\\n🎉 修复版情感分析系统运行完成！\")\n", "print(\"\\n📋 修复内容总结:\")\n", "print(\"  ✅ 解决了情感词典编码问题（支持GBK、UTF-8等多种编码）\")\n", "print(\"  ✅ 修复了中文字体显示问题\")\n", "print(\"  ✅ 优化了容错机制和错误处理\")\n", "print(\"  ✅ 增强了可视化效果\")\n", "print(\"\\n💡 现在您可以正常使用所有功能了！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置文件路径\n", "PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'\n", "STOPWORDS_PATH = 'data/stopwords.txt'\n", "POSITIVE_DICT_PATH = 'data/正面词典.csv'\n", "NEGATIVE_DICT_PATH = 'data/负面词典.csv'\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists(PDF_PATH):\n", "    print(f\"❌ PDF文件不存在: {PDF_PATH}\")\n", "    print(\"请确保PDF文件在正确的位置\")\n", "else:\n", "    print(f\"✅ 找到PDF文件: {PDF_PATH}\")\n", "    \n", "    # 执行完整分析\n", "    analysis_results = analyze_research_report(\n", "        pdf_path=PDF_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH\n", "    )\n", "    \n", "    if analysis_results:\n", "        print(\"\\n🎨 开始生成可视化图表...\")\n", "        \n", "        # 1. 情感分析方法对比图\n", "        create_sentiment_comparison_chart(\n", "            analysis_results['dict_analysis']['overall_score'],\n", "            analysis_results['snownlp_analysis']['overall_score'],\n", "            analysis_results['rules_analysis']['overall_score']\n", "        )\n", "        \n", "        # 2. 关键词情感分布图（词典法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['dict_analysis']['keyword_scores'],\n", "            \"Dictionary Method\"\n", "        )\n", "        \n", "        # 3. 关键词情感分布图（SnowNLP法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['snownlp_analysis']['keyword_scores'],\n", "            \"SnowNLP Method\"\n", "        )\n", "        \n", "        # 4. 关键词云图\n", "        create_wordcloud(analysis_results['keywords'])\n", "        \n", "        # 5. 显示详细结果\n", "        print(\"\\n📋 详细分析结果:\")\n", "        print(\"=\" * 50)\n", "        \n", "        print(f\"\\n📄 文本统计:\")\n", "        print(f\"  • 原始文本长度: {len(analysis_results['original_text'])} 字符\")\n", "        print(f\"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个\")\n", "        print(f\"  • 提取表格数: {len(analysis_results['tables'])} 个\")\n", "        \n", "        print(f\"\\n🔍 关键词 (前10个):\")\n", "        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "            print(f\"  {i:2d}. {word} (权重: {weight:.4f})\")\n", "        \n", "        print(f\"\\n📊 情感分析结果:\")\n", "        print(f\"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 平均得分: {analysis_results['average_score']:.4f}\")\n", "        print(f\"  • 情感倾向: {analysis_results['sentiment_label']}\")\n", "        \n", "        print(f\"\\n💡 匹配的情感词 (前10个):\")\n", "        matched_words = analysis_results['dict_analysis']['matched_words'][:10]\n", "        for word, score in matched_words:\n", "            sentiment = \"正面\" if score > 0 else \"负面\"\n", "            print(f\"  • {word} ({sentiment}, {score:+.1f})\")\n", "        \n", "        print(\"\\n✅ 所有分析和可视化完成！\")\n", "    else:\n", "        print(\"❌ 分析失败，请检查输入文件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置文件路径\n", "PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'\n", "STOPWORDS_PATH = 'data/stopwords.txt'\n", "POSITIVE_DICT_PATH = 'data/正面词典.csv'\n", "NEGATIVE_DICT_PATH = 'data/负面词典.csv'\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists(PDF_PATH):\n", "    print(f\"❌ PDF文件不存在: {PDF_PATH}\")\n", "    print(\"请确保PDF文件在正确的位置\")\n", "else:\n", "    print(f\"✅ 找到PDF文件: {PDF_PATH}\")\n", "    \n", "    # 执行完整分析\n", "    analysis_results = analyze_research_report(\n", "        pdf_path=PDF_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH\n", "    )\n", "    \n", "    if analysis_results:\n", "        print(\"\\n🎨 开始生成可视化图表...\")\n", "        \n", "        # 1. 情感分析方法对比图\n", "        create_sentiment_comparison_chart(\n", "            analysis_results['dict_analysis']['overall_score'],\n", "            analysis_results['snownlp_analysis']['overall_score'],\n", "            analysis_results['rules_analysis']['overall_score']\n", "        )\n", "        \n", "        # 2. 关键词情感分布图（词典法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['dict_analysis']['keyword_scores'],\n", "            \"Dictionary Method\"\n", "        )\n", "        \n", "        # 3. 关键词情感分布图（SnowNLP法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['snownlp_analysis']['keyword_scores'],\n", "            \"SnowNLP Method\"\n", "        )\n", "        \n", "        # 4. 关键词云图\n", "        create_wordcloud(analysis_results['keywords'])\n", "        \n", "        # 5. 显示详细结果\n", "        print(\"\\n📋 详细分析结果:\")\n", "        print(\"=\" * 50)\n", "        \n", "        print(f\"\\n📄 文本统计:\")\n", "        print(f\"  • 原始文本长度: {len(analysis_results['original_text'])} 字符\")\n", "        print(f\"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个\")\n", "        print(f\"  • 提取表格数: {len(analysis_results['tables'])} 个\")\n", "        \n", "        print(f\"\\n🔍 关键词 (前10个):\")\n", "        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "            print(f\"  {i:2d}. {word} (权重: {weight:.4f})\")\n", "        \n", "        print(f\"\\n📊 情感分析结果:\")\n", "        print(f\"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 平均得分: {analysis_results['average_score']:.4f}\")\n", "        print(f\"  • 情感倾向: {analysis_results['sentiment_label']}\")\n", "        \n", "        print(f\"\\n💡 匹配的情感词 (前10个):\")\n", "        matched_words = analysis_results['dict_analysis']['matched_words'][:10]\n", "        for word, score in matched_words:\n", "            sentiment = \"正面\" if score > 0 else \"负面\"\n", "            print(f\"  • {word} ({sentiment}, {score:+.1f})\")\n", "        \n", "        print(\"\\n✅ 所有分析和可视化完成！\")\n", "    else:\n", "        print(\"❌ 分析失败，请检查输入文件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置文件路径\n", "PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'\n", "STOPWORDS_PATH = 'data/stopwords.txt'\n", "POSITIVE_DICT_PATH = 'data/正面词典.csv'\n", "NEGATIVE_DICT_PATH = 'data/负面词典.csv'\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists(PDF_PATH):\n", "    print(f\"❌ PDF文件不存在: {PDF_PATH}\")\n", "    print(\"请确保PDF文件在正确的位置\")\n", "else:\n", "    print(f\"✅ 找到PDF文件: {PDF_PATH}\")\n", "    \n", "    # 执行完整分析\n", "    analysis_results = analyze_research_report(\n", "        pdf_path=PDF_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH\n", "    )\n", "    \n", "    if analysis_results:\n", "        print(\"\\n🎨 开始生成可视化图表...\")\n", "        \n", "        # 1. 情感分析方法对比图\n", "        create_sentiment_comparison_chart(\n", "            analysis_results['dict_analysis']['overall_score'],\n", "            analysis_results['snownlp_analysis']['overall_score'],\n", "            analysis_results['rules_analysis']['overall_score']\n", "        )\n", "        \n", "        # 2. 关键词情感分布图（词典法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['dict_analysis']['keyword_scores'],\n", "            \"Dictionary Method\"\n", "        )\n", "        \n", "        # 3. 关键词情感分布图（SnowNLP法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['snownlp_analysis']['keyword_scores'],\n", "            \"SnowNLP Method\"\n", "        )\n", "        \n", "        # 4. 关键词云图\n", "        create_wordcloud(analysis_results['keywords'])\n", "        \n", "        # 5. 显示详细结果\n", "        print(\"\\n📋 详细分析结果:\")\n", "        print(\"=\" * 50)\n", "        \n", "        print(f\"\\n📄 文本统计:\")\n", "        print(f\"  • 原始文本长度: {len(analysis_results['original_text'])} 字符\")\n", "        print(f\"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个\")\n", "        print(f\"  • 提取表格数: {len(analysis_results['tables'])} 个\")\n", "        \n", "        print(f\"\\n🔍 关键词 (前10个):\")\n", "        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "            print(f\"  {i:2d}. {word} (权重: {weight:.4f})\")\n", "        \n", "        print(f\"\\n📊 情感分析结果:\")\n", "        print(f\"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 平均得分: {analysis_results['average_score']:.4f}\")\n", "        print(f\"  • 情感倾向: {analysis_results['sentiment_label']}\")\n", "        \n", "        print(f\"\\n💡 匹配的情感词 (前10个):\")\n", "        matched_words = analysis_results['dict_analysis']['matched_words'][:10]\n", "        for word, score in matched_words:\n", "            sentiment = \"正面\" if score > 0 else \"负面\"\n", "            print(f\"  • {word} ({sentiment}, {score:+.1f})\")\n", "        \n", "        print(\"\\n✅ 所有分析和可视化完成！\")\n", "    else:\n", "        print(\"❌ 分析失败，请检查输入文件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置文件路径\n", "PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'\n", "STOPWORDS_PATH = 'data/stopwords.txt'\n", "POSITIVE_DICT_PATH = 'data/正面词典.csv'\n", "NEGATIVE_DICT_PATH = 'data/负面词典.csv'\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists(PDF_PATH):\n", "    print(f\"❌ PDF文件不存在: {PDF_PATH}\")\n", "    print(\"请确保PDF文件在正确的位置\")\n", "else:\n", "    print(f\"✅ 找到PDF文件: {PDF_PATH}\")\n", "    \n", "    # 执行完整分析\n", "    analysis_results = analyze_research_report(\n", "        pdf_path=PDF_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH\n", "    )\n", "    \n", "    if analysis_results:\n", "        print(\"\\n🎨 开始生成可视化图表...\")\n", "        \n", "        # 1. 情感分析方法对比图\n", "        create_sentiment_comparison_chart(\n", "            analysis_results['dict_analysis']['overall_score'],\n", "            analysis_results['snownlp_analysis']['overall_score'],\n", "            analysis_results['rules_analysis']['overall_score']\n", "        )\n", "        \n", "        # 2. 关键词情感分布图（词典法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['dict_analysis']['keyword_scores'],\n", "            \"Dictionary Method\"\n", "        )\n", "        \n", "        # 3. 关键词情感分布图（SnowNLP法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['snownlp_analysis']['keyword_scores'],\n", "            \"SnowNLP Method\"\n", "        )\n", "        \n", "        # 4. 关键词云图\n", "        create_wordcloud(analysis_results['keywords'])\n", "        \n", "        # 5. 显示详细结果\n", "        print(\"\\n📋 详细分析结果:\")\n", "        print(\"=\" * 50)\n", "        \n", "        print(f\"\\n📄 文本统计:\")\n", "        print(f\"  • 原始文本长度: {len(analysis_results['original_text'])} 字符\")\n", "        print(f\"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个\")\n", "        print(f\"  • 提取表格数: {len(analysis_results['tables'])} 个\")\n", "        \n", "        print(f\"\\n🔍 关键词 (前10个):\")\n", "        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "            print(f\"  {i:2d}. {word} (权重: {weight:.4f})\")\n", "        \n", "        print(f\"\\n📊 情感分析结果:\")\n", "        print(f\"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 平均得分: {analysis_results['average_score']:.4f}\")\n", "        print(f\"  • 情感倾向: {analysis_results['sentiment_label']}\")\n", "        \n", "        print(f\"\\n💡 匹配的情感词 (前10个):\")\n", "        matched_words = analysis_results['dict_analysis']['matched_words'][:10]\n", "        for word, score in matched_words:\n", "            sentiment = \"正面\" if score > 0 else \"负面\"\n", "            print(f\"  • {word} ({sentiment}, {score:+.1f})\")\n", "        \n", "        print(\"\\n✅ 所有分析和可视化完成！\")\n", "    else:\n", "        print(\"❌ 分析失败，请检查输入文件\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 设置文件路径\n", "PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'\n", "STOPWORDS_PATH = 'data/stopwords.txt'\n", "POSITIVE_DICT_PATH = 'data/正面词典.csv'\n", "NEGATIVE_DICT_PATH = 'data/负面词典.csv'\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists(PDF_PATH):\n", "    print(f\"❌ PDF文件不存在: {PDF_PATH}\")\n", "    print(\"请确保PDF文件在正确的位置\")\n", "else:\n", "    print(f\"✅ 找到PDF文件: {PDF_PATH}\")\n", "    \n", "    # 执行完整分析\n", "    analysis_results = analyze_research_report(\n", "        pdf_path=PDF_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH\n", "    )\n", "    \n", "    if analysis_results:\n", "        print(\"\\n🎨 开始生成可视化图表...\")\n", "        \n", "        # 1. 情感分析方法对比图\n", "        create_sentiment_comparison_chart(\n", "            analysis_results['dict_analysis']['overall_score'],\n", "            analysis_results['snownlp_analysis']['overall_score'],\n", "            analysis_results['rules_analysis']['overall_score']\n", "        )\n", "        \n", "        # 2. 关键词情感分布图（词典法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['dict_analysis']['keyword_scores'],\n", "            \"Dictionary Method\"\n", "        )\n", "        \n", "        # 3. 关键词情感分布图（SnowNLP法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['snownlp_analysis']['keyword_scores'],\n", "            \"SnowNLP Method\"\n", "        )\n", "        \n", "        # 4. 关键词云图\n", "        create_wordcloud(analysis_results['keywords'])\n", "        \n", "        # 5. 显示详细结果\n", "        print(\"\\n📋 详细分析结果:\")\n", "        print(\"=\" * 50)\n", "        \n", "        print(f\"\\n📄 文本统计:\")\n", "        print(f\"  • 原始文本长度: {len(analysis_results['original_text'])} 字符\")\n", "        print(f\"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个\")\n", "        print(f\"  • 提取表格数: {len(analysis_results['tables'])} 个\")\n", "        \n", "        print(f\"\\n🔍 关键词 (前10个):\")\n", "        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "            print(f\"  {i:2d}. {word} (权重: {weight:.4f})\")\n", "        \n", "        print(f\"\\n📊 情感分析结果:\")\n", "        print(f\"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 平均得分: {analysis_results['average_score']:.4f}\")\n", "        print(f\"  • 情感倾向: {analysis_results['sentiment_label']}\")\n", "        \n", "        print(f\"\\n💡 匹配的情感词 (前10个):\")\n", "        matched_words = analysis_results['dict_analysis']['matched_words'][:10]\n", "        for word, score in matched_words:\n", "            sentiment = \"正面\" if score > 0 else \"负面\"\n", "            print(f\"  • {word} ({sentiment}, {score:+.1f})\")\n", "        \n", "        print(\"\\n✅ 所有分析和可视化完成！\")\n", "    else:\n", "        print(\"❌ 分析失败，请检查输入文件\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}