#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试情感关键词提取功能
"""

import os
import sys
import warnings
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import jieba
import jieba.analyse
from collections import Counter, defaultdict
from datetime import datetime

# 忽略警告
warnings.filterwarnings('ignore')

# 设置matplotlib字体
plt.rcParams['font.family'] = ['sans-serif']
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

print("🎯 测试情感关键词提取功能")
print("=" * 60)

# 模拟情感词典
def create_test_sentiment_dict():
    """创建测试用的情感词典"""
    sentiment_dict = {}
    
    # 正面词
    positive_words = [
        '好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', 
        '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新',
        '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣'
    ]
    
    # 负面词
    negative_words = [
        '差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少',
        '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退',
        '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境'
    ]
    
    for word in positive_words:
        sentiment_dict[word] = 1.0
    for word in negative_words:
        sentiment_dict[word] = -1.0
    
    return sentiment_dict

# 情感关键词提取函数
def extract_sentiment_keywords(text: str, sentiment_dict: dict, num_keywords: int = 20):
    """提取有情感意义的关键词"""
    print("🎯 开始提取有情感意义的关键词...")
    
    # 分词并统计词频
    words = jieba.lcut(text)
    word_freq = Counter(words)
    
    # 计算总词数用于归一化
    total_words = len(words)
    
    positive_keywords = []
    negative_keywords = []
    
    # 遍历所有词，找出有情感意义的词
    for word, freq in word_freq.items():
        if len(word) >= 2 and word in sentiment_dict:
            sentiment_score = sentiment_dict[word]
            # 计算词频权重（归一化）
            freq_weight = freq / total_words
            
            if sentiment_score > 0:
                positive_keywords.append((word, sentiment_score, freq_weight))
            elif sentiment_score < 0:
                negative_keywords.append((word, abs(sentiment_score), freq_weight))
    
    # 按情感强度和词频的综合得分排序
    positive_keywords.sort(key=lambda x: x[1] * x[2], reverse=True)
    negative_keywords.sort(key=lambda x: x[1] * x[2], reverse=True)
    
    # 取前num_keywords个
    positive_keywords = positive_keywords[:num_keywords]
    negative_keywords = negative_keywords[:num_keywords]
    
    print(f"✅ 情感关键词提取完成")
    print(f"  • 正面情感关键词: {len(positive_keywords)} 个")
    print(f"  • 负面情感关键词: {len(negative_keywords)} 个")
    
    return positive_keywords, negative_keywords

# 上下文情感关键词提取函数
def extract_contextual_sentiment_keywords(text: str, keywords: list, window_size: int = 10):
    """基于上下文提取情感关键词"""
    print("🔍 开始基于上下文提取情感关键词...")
    
    # 预定义情感词
    positive_words = {
        '好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', 
        '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新'
    }
    
    negative_words = {
        '差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少',
        '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退'
    }
    
    # 分词
    words = jieba.lcut(text)
    
    contextual_sentiment_keywords = []
    
    for keyword, weight in keywords:
        # 找到关键词在文本中的所有位置
        keyword_positions = []
        for i, word in enumerate(words):
            if keyword in word or word in keyword:
                keyword_positions.append(i)
        
        if not keyword_positions:
            continue
        
        # 分析每个关键词位置的上下文
        context_sentiment_scores = []
        context_sentiment_words = []
        
        for pos in keyword_positions:
            # 定义上下文窗口
            start = max(0, pos - window_size)
            end = min(len(words), pos + window_size + 1)
            context_words = words[start:end]
            
            # 计算上下文情感得分
            pos_count = sum(1 for w in context_words if w in positive_words)
            neg_count = sum(1 for w in context_words if w in negative_words)
            
            if len(context_words) > 0:
                context_score = (pos_count - neg_count) / len(context_words)
                context_sentiment_scores.append(context_score)
                
                # 收集上下文中的情感词
                sentiment_words_in_context = [w for w in context_words if w in positive_words or w in negative_words]
                context_sentiment_words.extend(sentiment_words_in_context)
        
        # 计算平均上下文情感得分
        if context_sentiment_scores:
            avg_context_sentiment = np.mean(context_sentiment_scores)
            unique_sentiment_words = list(set(context_sentiment_words))
            
            contextual_sentiment_keywords.append((keyword, avg_context_sentiment, unique_sentiment_words))
    
    # 按上下文情感得分的绝对值排序（情感强度）
    contextual_sentiment_keywords.sort(key=lambda x: abs(x[1]), reverse=True)
    
    print(f"✅ 上下文情感关键词提取完成，共 {len(contextual_sentiment_keywords)} 个")
    
    return contextual_sentiment_keywords

# 测试文本
test_text = """
金融科技行业发展迅速，带来了巨大的机遇和挑战。许多公司在这个领域取得了优秀的成绩，
盈利能力不断提升，业务增长强劲。然而，市场竞争激烈，也存在一些风险和困难。
投资者对这个行业保持乐观态度，相信未来会有更好的发展前景。
同时，监管政策的变化也带来了不确定性，需要企业谨慎应对各种挑战。
总体而言，金融科技行业仍然充满希望，创新能力是成功的关键因素。
"""

print(f"\n📄 测试文本:")
print(f"文本长度: {len(test_text)} 字符")
print(f"文本内容: {test_text[:100]}...")

# 1. 创建情感词典
print(f"\n📚 1. 创建测试情感词典")
sentiment_dict = create_test_sentiment_dict()
print(f"✅ 情感词典创建完成，共 {len(sentiment_dict)} 个词")

# 2. 提取基础关键词
print(f"\n🔍 2. 提取基础关键词")
keywords = jieba.analyse.textrank(test_text, topK=10, withWeight=True)
print(f"✅ 基础关键词提取完成，共 {len(keywords)} 个")
print("前5个关键词:")
for i, (word, weight) in enumerate(keywords[:5], 1):
    print(f"  {i}. {word}: {weight:.4f}")

# 3. 提取情感关键词
print(f"\n🎯 3. 提取情感关键词")
positive_keywords, negative_keywords = extract_sentiment_keywords(test_text, sentiment_dict, 10)

print(f"\n正面情感关键词:")
for i, (word, sentiment_score, freq_weight) in enumerate(positive_keywords, 1):
    combined_score = sentiment_score * freq_weight
    print(f"  {i}. {word:<8} 情感强度: {sentiment_score:.3f}, 词频权重: {freq_weight:.6f}, 综合得分: {combined_score:.6f}")

print(f"\n负面情感关键词:")
for i, (word, sentiment_score, freq_weight) in enumerate(negative_keywords, 1):
    combined_score = sentiment_score * freq_weight
    print(f"  {i}. {word:<8} 情感强度: {sentiment_score:.3f}, 词频权重: {freq_weight:.6f}, 综合得分: {combined_score:.6f}")

# 4. 上下文情感关键词分析
print(f"\n🔍 4. 上下文情感关键词分析")
contextual_keywords = extract_contextual_sentiment_keywords(test_text, keywords, 5)

print(f"\n上下文情感关键词分析结果:")
for i, (keyword, context_score, sentiment_words) in enumerate(contextual_keywords[:8], 1):
    sentiment_tendency = '正面' if context_score > 0.02 else '负面' if context_score < -0.02 else '中性'
    sentiment_words_str = ', '.join(sentiment_words[:3]) if sentiment_words else '无'
    
    print(f"  {i}. {keyword:<8} 上下文得分: {context_score:+.4f} ({sentiment_tendency})")
    print(f"     相关情感词: {sentiment_words_str}")

# 5. 创建简单的可视化
print(f"\n📊 5. 创建可视化图表")
try:
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # 图1: 情感关键词数量对比
    counts = [len(positive_keywords), len(negative_keywords)]
    labels = ['Positive Keywords', 'Negative Keywords']
    colors = ['green', 'red']
    
    ax1.bar(labels, counts, color=colors, alpha=0.7)
    ax1.set_title('Sentiment Keywords Count', fontweight='bold')
    ax1.set_ylabel('Number of Keywords')
    
    for i, count in enumerate(counts):
        ax1.text(i, count + 0.1, str(count), ha='center', va='bottom', fontweight='bold')
    
    # 图2: 正面情感关键词
    if positive_keywords:
        pos_words = [item[0] for item in positive_keywords[:5]]
        pos_scores = [item[1] * item[2] for item in positive_keywords[:5]]
        
        ax2.barh(pos_words, pos_scores, color='green', alpha=0.7)
        ax2.set_title('Top Positive Sentiment Keywords', fontweight='bold')
        ax2.set_xlabel('Combined Score')
    
    # 图3: 负面情感关键词
    if negative_keywords:
        neg_words = [item[0] for item in negative_keywords[:5]]
        neg_scores = [item[1] * item[2] for item in negative_keywords[:5]]
        
        ax3.barh(neg_words, neg_scores, color='red', alpha=0.7)
        ax3.set_title('Top Negative Sentiment Keywords', fontweight='bold')
        ax3.set_xlabel('Combined Score')
    
    # 图4: 上下文情感分布
    if contextual_keywords:
        context_words = [item[0] for item in contextual_keywords[:6]]
        context_scores = [item[1] for item in contextual_keywords[:6]]
        colors = ['green' if s > 0 else 'red' if s < 0 else 'gray' for s in context_scores]
        
        ax4.barh(context_words, context_scores, color=colors, alpha=0.7)
        ax4.set_title('Contextual Sentiment Analysis', fontweight='bold')
        ax4.set_xlabel('Context Sentiment Score')
        ax4.axvline(x=0, color='black', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('sentiment_keywords_test.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 可视化图表已生成: sentiment_keywords_test.png")
    
except Exception as e:
    print(f"⚠️ 可视化生成失败: {e}")

# 6. 统计总结
print(f"\n📈 6. 统计总结")
print(f"=" * 50)

total_sentiment_keywords = len(positive_keywords) + len(negative_keywords)
if total_sentiment_keywords > 0:
    positive_ratio = len(positive_keywords) / total_sentiment_keywords
    print(f"情感关键词总数: {total_sentiment_keywords}")
    print(f"正面关键词比例: {positive_ratio:.1%}")
    print(f"负面关键词比例: {1-positive_ratio:.1%}")
    print(f"情感倾向: {'偏正面' if positive_ratio > 0.6 else '偏负面' if positive_ratio < 0.4 else '平衡'}")

if contextual_keywords:
    positive_context = len([kw for kw, score, _ in contextual_keywords if score > 0.02])
    negative_context = len([kw for kw, score, _ in contextual_keywords if score < -0.02])
    neutral_context = len(contextual_keywords) - positive_context - negative_context
    
    print(f"\n上下文情感分析:")
    print(f"正面倾向关键词: {positive_context} 个")
    print(f"负面倾向关键词: {negative_context} 个")
    print(f"中性倾向关键词: {neutral_context} 个")

print(f"\n🎉 情感关键词提取功能测试完成!")
print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
print(f"=" * 60)
