{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 研报情感分析系统 - 依赖修复版\n", "\n", "本notebook修复了transformers依赖冲突问题，包括：\n", "1. PDF文本提取\n", "2. 文本预处理\n", "3. 关键词提取\n", "4. 情感分析（词典法 + FinBERT）\n", "5. 结果可视化\n", "\n", "**修复内容**:\n", "- 解决了 `deprecated() got an unexpected keyword argument 'name'` 错误\n", "- 修复了 cryptography 和 pyOpenSSL 兼容性问题\n", "- 启用了 FinBERT 深度学习功能"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 兼容性补丁和库导入"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 兼容性补丁 - 修复依赖冲突\n", "import warnings\n", "warnings.filterwarnings('ignore', category=DeprecationWarning)\n", "warnings.filterwarnings('ignore', category=FutureWarning)\n", "\n", "# 修复 cryptography 和 pyOpenSSL 的兼容性问题\n", "try:\n", "    import cryptography\n", "    from cryptography import utils\n", "    \n", "    # 检查是否需要修复 deprecated 函数\n", "    if hasattr(utils, 'deprecated'):\n", "        original_deprecated = utils.deprecated\n", "        \n", "        def patched_deprecated(reason, name=None, **kwargs):\n", "            # 移除不支持的 name 参数，只保留 reason\n", "            return original_deprecated(reason)\n", "        \n", "        utils.deprecated = patched_deprecated\n", "        print(\"✅ cryptography 兼容性补丁已应用\")\n", "    else:\n", "        # 如果没有 deprecated 函数，创建一个简单的\n", "        def deprecated(reason, name=None, **kwargs):\n", "            def decorator(func):\n", "                return func\n", "            return decorator\n", "        utils.deprecated = deprecated\n", "        print(\"✅ cryptography deprecated 函数已创建\")\n", "        \n", "except ImportError:\n", "    print(\"⚠️ cryptography 未安装，跳过补丁\")\n", "except Exception as e:\n", "    print(f\"⚠️ cryptography 补丁失败: {e}\")\n", "\n", "print(\"🩹 兼容性补丁加载完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 基础库\n", "import os\n", "import sys\n", "import time\n", "import re\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import concurrent.futures\n", "from typing import List, Tuple, Dict, Optional\n", "\n", "# 数据处理\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF处理\n", "import pdfplumber\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"警告: PyMuPDF不可用，将使用其他PDF提取方法\")\n", "\n", "try:\n", "    import camelot\n", "    CAMELOT_AVAILABLE = True\n", "except ImportError:\n", "    CAMELOT_AVAILABLE = False\n", "    print(\"警告: camelot-py不可用，高级表格提取功能将不可用\")\n", "\n", "# 文本处理\n", "import jieba\n", "import jieba.analyse\n", "from tqdm import tqdm\n", "\n", "# TextRank\n", "try:\n", "    from textrank4zh import TextRank4Keyword, TextRank4Sentence\n", "    TEXTRANK4ZH_AVAILABLE = True\n", "except ImportError:\n", "    TEXTRANK4ZH_AVAILABLE = False\n", "    print(\"警告: textrank4zh不可用，将使用jieba的TextRank\")\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "print(\"✅ 基础库导入完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 深度学习 - 修复依赖冲突版本\n", "try:\n", "    import torch\n", "    from transformers import AutoTokenizer, AutoModelForSequenceClassification\n", "    TRANSFORMERS_AVAILABLE = True\n", "    print(\"✅ FinBERT功能已启用（依赖冲突已修复）\")\n", "    print(f\"   PyTorch版本: {torch.__version__}\")\n", "    \n", "    # 测试基本功能\n", "    try:\n", "        test_tokenizer = AutoTokenizer.from_pretrained('bert-base-uncased')\n", "        print(\"✅ Transformers功能测试通过\")\n", "    except Exception as e:\n", "        print(f\"⚠️ Transformers功能测试失败: {e}\")\n", "        TRANSFORMERS_AVAILABLE = False\n", "        \n", "except Exception as e:\n", "    TRANSFORMERS_AVAILABLE = False\n", "    print(f\"⚠️ transformers导入失败: {str(e)[:100]}...\")\n", "    print(\"将使用传统方法进行情感分析\")\n", "    print(\"\\n🔧 如需启用FinBERT，请执行:\")\n", "    print(\"pip install --upgrade transformers torch accelerate cryptography pyopenssl\")\n", "    print(\"然后重启Jupyter内核\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "import seaborn as sns\n", "from wordcloud import WordCloud\n", "\n", "# 设置matplotlib和WordCloud中文字体支持\n", "import platform\n", "system = platform.system()\n", "\n", "# 获取系统字体路径\n", "def get_system_font_path():\n", "    system = platform.system()\n", "    if system == \"Windows\":\n", "        # Windows系统字体路径\n", "        possible_fonts = [\n", "            \"C:/Windows/Fonts/simhei.ttf\",\n", "            \"C:/Windows/Fonts/msyh.ttc\",\n", "            \"C:/Windows/Fonts/simsun.ttc\"\n", "        ]\n", "    elif system == \"Darwin\":  # macOS\n", "        possible_fonts = [\n", "            \"/System/Library/Fonts/PingFang.ttc\",\n", "            \"/System/Library/Fonts/Hiragino Sans GB.ttc\",\n", "            \"/System/Library/Fonts/Arial Unicode.ttf\"\n", "        ]\n", "    else:  # Linux\n", "        possible_fonts = [\n", "            \"/usr/share/fonts/truetype/wqy/wqy-microhei.ttc\",\n", "            \"/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf\",\n", "            \"/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf\"\n", "        ]\n", "    \n", "    # 检查字体文件是否存在\n", "    for font_path in possible_fonts:\n", "        if os.path.exists(font_path):\n", "            return font_path\n", "    \n", "    # 如果没有找到，返回None\n", "    return None\n", "\n", "# 获取系统字体路径\n", "SYSTEM_FONT_PATH = get_system_font_path()\n", "\n", "# 根据操作系统设置中文字体\n", "if system == 'Windows':\n", "    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS', 'sans-serif']\n", "elif system == 'Darwin':  # macOS\n", "    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'Hiragino Sans GB', 'sans-serif']\n", "else:  # Linux\n", "    plt.rcParams['font.sans-serif'] = ['WenQuanYi Micro Hei', 'DejaVu Sans', 'sans-serif']\n", "\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['font.size'] = 12\n", "\n", "# 测试中文字体\n", "try:\n", "    fig, ax = plt.subplots(figsize=(1, 1))\n", "    ax.text(0.5, 0.5, '测试中文', ha='center', va='center')\n", "    plt.close(fig)\n", "    print(\"✅ 中文字体设置成功\")\n", "    CHINESE_FONT_AVAILABLE = True\n", "except Exception as e:\n", "    print(f\"⚠️ 中文字体设置可能有问题: {e}，将使用英文标签\")\n", "    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'sans-serif']\n", "    CHINESE_FONT_AVAILABLE = False\n", "\n", "print(\"\\n📊 系统状态:\")\n", "print(f\"PyMuPDF可用: {PYMUPDF_AVAILABLE}\")\n", "print(f\"Camelot可用: {CAMELOT_AVAILABLE}\")\n", "print(f\"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}\")\n", "print(f\"Transformers可用: {TRANSFORMERS_AVAILABLE}\")\n", "print(f\"中文字体可用: {CHINESE_FONT_AVAILABLE}\")\n", "print(f\"系统字体路径: {SYSTEM_FONT_PATH}\")\n", "\n", "print(\"\\n🎉 所有库导入完成，依赖冲突已修复！\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}