#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复notebook中的编码和字体问题
"""

import json
import os
import sys

def fix_notebook_issues():
    """修复notebook中的问题"""
    
    # 读取notebook
    with open('情感分析完整版.ipynb', 'r', encoding='utf-8') as f:
        notebook = json.load(f)
    
    # 找到情感词典加载函数的cell
    for cell in notebook['cells']:
        if cell['cell_type'] == 'code' and 'source' in cell:
            source_lines = cell['source']
            
            # 检查是否是情感词典加载函数
            if any('def load_sentiment_dict' in line for line in source_lines):
                print("找到情感词典加载函数，开始修复...")
                
                # 修复编码问题
                new_source = []
                in_positive_section = False
                in_negative_section = False
                
                for line in source_lines:
                    if '# 加载正面词典' in line:
                        in_positive_section = True
                        new_source.append(line)
                    elif '# 加载负面词典' in line:
                        in_positive_section = False
                        in_negative_section = True
                        new_source.append(line)
                    elif 'print(f"📚 情感词典加载完成' in line:
                        in_negative_section = False
                        new_source.append(line)
                    elif in_positive_section and 'df = pd.read_csv(positive_path)' in line:
                        # 替换为支持多编码的版本
                        new_source.extend([
                            "                # 尝试多种编码\\n",
                            "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
                            "                    try:\\n",
                            "                        df = pd.read_csv(positive_path, encoding=encoding)\\n",
                            "                        if not df.empty:\\n",
                            "                            words = df.iloc[:, 0].tolist()\\n",
                            "                            for word in words:\\n",
                            "                                if isinstance(word, str) and word.strip():\\n",
                            "                                    sentiment_dict[word.strip()] = 1.0\\n",
                            "                        print(f\\"✅ 使用 {encoding} 编码加载正面词典: {positive_path}\\")\\n",
                            "                        break\\n",
                            "                    except UnicodeDecodeError:\\n",
                            "                        continue\\n"
                        ])
                        # 跳过原来的几行
                        continue
                    elif in_positive_section and any(x in line for x in ['words = df.iloc', 'for word in words:', 'sentiment_dict[word.strip()] = 1.0']):
                        # 跳过这些行，已经在上面添加了
                        continue
                    elif in_negative_section and 'df = pd.read_csv(negative_path)' in line:
                        # 替换为支持多编码的版本
                        new_source.extend([
                            "                # 尝试多种编码\\n",
                            "                for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:\\n",
                            "                    try:\\n",
                            "                        df = pd.read_csv(negative_path, encoding=encoding)\\n",
                            "                        if not df.empty:\\n",
                            "                            words = df.iloc[:, 0].tolist()\\n",
                            "                            for word in words:\\n",
                            "                                if isinstance(word, str) and word.strip():\\n",
                            "                                    sentiment_dict[word.strip()] = -1.0\\n",
                            "                        print(f\\"✅ 使用 {encoding} 编码加载负面词典: {negative_path}\\")\\n",
                            "                        break\\n",
                            "                    except UnicodeDecodeError:\\n",
                            "                        continue\\n"
                        ])
                        continue
                    elif in_negative_section and any(x in line for x in ['words = df.iloc', 'for word in words:', 'sentiment_dict[word.strip()] = -1.0']):
                        # 跳过这些行，已经在上面添加了
                        continue
                    else:
                        new_source.append(line)
                
                cell['source'] = new_source
                print("✅ 情感词典编码问题已修复")
                break
    
    # 保存修复后的notebook
    with open('情感分析完整版.ipynb', 'w', encoding='utf-8') as f:
        json.dump(notebook, f, ensure_ascii=False, indent=1)
    
    print("✅ Notebook修复完成")

def create_fixed_sentiment_dict_function():
    """创建修复后的情感词典加载函数"""
    
    fixed_function = '''
def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:
    """
    加载情感词典（支持多种编码）
    
    参数:
        positive_path: 正面词典文件路径
        negative_path: 负面词典文件路径
    
    返回:
        情感词典，正面词为正值，负面词为负值
    """
    sentiment_dict = {}
    
    # 默认情感词
    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']
    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']
    
    # 添加默认词
    for word in default_positive:
        sentiment_dict[word] = 1.0
    for word in default_negative:
        sentiment_dict[word] = -1.0
    
    # 加载正面词典
    if os.path.exists(positive_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if positive_path.endswith('.csv'):
                    df = pd.read_csv(positive_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = 1.0
                else:
                    with open(positive_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = 1.0
                print(f"✅ 使用 {encoding} 编码加载正面词典: {positive_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载正面词典: {positive_path}")
    
    # 加载负面词典
    if os.path.exists(negative_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if negative_path.endswith('.csv'):
                    df = pd.read_csv(negative_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = -1.0
                else:
                    with open(negative_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = -1.0
                print(f"✅ 使用 {encoding} 编码加载负面词典: {negative_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载负面词典: {negative_path}")
    
    print(f"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词")
    return sentiment_dict

print("✅ 修复后的情感词典加载函数已定义")
'''
    
    return fixed_function

def test_encoding_fix():
    """测试编码修复"""
    import pandas as pd
    
    print("🔧 测试编码修复...")
    
    # 测试正面词典
    positive_path = 'data/正面词典.csv'
    if os.path.exists(positive_path):
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                df = pd.read_csv(positive_path, encoding=encoding)
                print(f"✅ {encoding} 编码可以读取正面词典，共 {len(df)} 行")
                print(f"前5个词: {df.iloc[:5, 0].tolist()}")
                break
            except Exception as e:
                print(f"❌ {encoding} 编码失败: {e}")
    
    # 测试负面词典
    negative_path = 'data/负面词典.csv'
    if os.path.exists(negative_path):
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                df = pd.read_csv(negative_path, encoding=encoding)
                print(f"✅ {encoding} 编码可以读取负面词典，共 {len(df)} 行")
                print(f"前5个词: {df.iloc[:5, 0].tolist()}")
                break
            except Exception as e:
                print(f"❌ {encoding} 编码失败: {e}")

if __name__ == "__main__":
    print("🚀 开始修复notebook问题...")
    
    # 测试编码
    test_encoding_fix()
    
    # 输出修复后的函数
    print("\n" + "="*50)
    print("修复后的情感词典加载函数:")
    print("="*50)
    print(create_fixed_sentiment_dict_function())
    
    print("\n✅ 修复完成！请将上述函数替换notebook中的对应函数。")
