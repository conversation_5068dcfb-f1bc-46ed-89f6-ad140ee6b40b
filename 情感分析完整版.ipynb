{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 研报情感分析系统 - 完整版\n", "\n", "本notebook整合了研报情感分析的完整流程，包括：\n", "1. PDF文本提取\n", "2. 文本预处理\n", "3. 关键词提取\n", "4. 多种情感分析方法对比\n", "5. 结果可视化\n", "\n", "所有结果都在notebook内展示，无需外部文件。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 导入必要的库和模块"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 所有必要的库已导入完成\n", "PyMuPDF可用: True\n", "TextRank4zh可用: True\n", "SnowNLP可用: True\n", "📊 将使用多种传统方法进行情感分析对比\n"]}], "source": ["# 基础库\n", "import os\n", "import sys\n", "import time\n", "import re\n", "import warnings\n", "from datetime import datetime\n", "from collections import Counter, defaultdict\n", "import concurrent.futures\n", "from typing import List, Tuple, Dict, Optional\n", "\n", "# 数据处理\n", "import pandas as pd\n", "import numpy as np\n", "\n", "# PDF处理\n", "import pdfplumber\n", "try:\n", "    import fitz  # PyMuPDF\n", "    PYMUPDF_AVAILABLE = True\n", "except ImportError:\n", "    PYMUPDF_AVAILABLE = False\n", "    print(\"警告: PyMuPDF不可用，将使用其他PDF提取方法\")\n", "\n", "# 文本处理\n", "import jieba\n", "import jieba.analyse\n", "from tqdm import tqdm\n", "\n", "# TextRank\n", "try:\n", "    from textrank4zh import TextRank4Keyword, TextRank4Sentence\n", "    TEXTRANK4ZH_AVAILABLE = True\n", "except ImportError:\n", "    TEXTRANK4ZH_AVAILABLE = False\n", "    print(\"警告: textrank4zh不可用，将使用jieba的TextRank\")\n", "\n", "# 机器学习\n", "from sklearn.feature_extraction.text import TfidfVectorizer\n", "\n", "# 情感分析库\n", "try:\n", "    from snownlp import SnowNLP\n", "    SNOWNLP_AVAILABLE = True\n", "except ImportError:\n", "    SNOWNLP_AVAILABLE = False\n", "    print(\"警告: SnowNLP不可用，将使用其他情感分析方法\")\n", "\n", "# 可视化\n", "import matplotlib.pyplot as plt\n", "import matplotlib.font_manager as fm\n", "import seaborn as sns\n", "from wordcloud import WordCloud\n", "\n", "# 设置matplotlib使用系统默认字体，避免中文显示问题\n", "plt.rcParams['font.family'] = ['sans-serif']\n", "plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Bitstream Vera Sans', 'sans-serif']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.rcParams['font.size'] = 12\n", "\n", "# 忽略警告\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"✅ 所有必要的库已导入完成\")\n", "print(f\"PyMuPDF可用: {PYMUPDF_AVAILABLE}\")\n", "print(f\"TextRank4zh可用: {TEXTRANK4ZH_AVAILABLE}\")\n", "print(f\"SnowNLP可用: {SNOWNLP_AVAILABLE}\")\n", "print(\"📊 将使用多种传统方法进行情感分析对比\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. PDF文本提取模块"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ PDF文本提取模块已定义\n"]}], "source": ["def extract_text_with_pymupdf(pdf_path: str) -> Tuple[str, List[str]]:\n", "    \"\"\"\n", "    使用PyMuPDF提取PDF文本\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 按页分割的文本列表)\n", "    \"\"\"\n", "    if not PYMUPDF_AVAILABLE:\n", "        return None, []\n", "    \n", "    try:\n", "        doc = fitz.open(pdf_path)\n", "        all_text = \"\"\n", "        page_texts = []\n", "        \n", "        for page_num in range(len(doc)):\n", "            page = doc[page_num]\n", "            text = page.get_text()\n", "            \n", "            if text.strip():\n", "                all_text += text + \"\\n\"\n", "                page_texts.append(text)\n", "        \n", "        doc.close()\n", "        return all_text, page_texts\n", "        \n", "    except Exception as e:\n", "        print(f\"PyMuPDF提取失败: {e}\")\n", "        return None, []\n", "\n", "def extract_text_with_pdfplumber(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    使用pdfplumber提取PDF文本和表格\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (完整文本, 表格列表)\n", "    \"\"\"\n", "    try:\n", "        all_text = \"\"\n", "        all_tables = []\n", "        \n", "        with pdfplumber.open(pdf_path) as pdf:\n", "            for page_num, page in enumerate(pdf.pages):\n", "                # 提取文本\n", "                text = page.extract_text()\n", "                if text:\n", "                    all_text += text + \"\\n\"\n", "                \n", "                # 提取表格\n", "                tables = page.extract_tables()\n", "                for table in tables:\n", "                    if table and len(table) > 1:\n", "                        try:\n", "                            df = pd.DataFrame(table[1:], columns=table[0])\n", "                            all_tables.append(df)\n", "                        except Exception as e:\n", "                            print(f\"表格处理失败: {e}\")\n", "        \n", "        return all_text, all_tables\n", "        \n", "    except Exception as e:\n", "        print(f\"pdfplumber提取失败: {e}\")\n", "        return \"\", []\n", "\n", "def extract_text_and_tables_from_pdf(pdf_path: str) -> Tuple[str, List[pd.DataFrame]]:\n", "    \"\"\"\n", "    从PDF文件中提取文本和表格，使用多种方法确保提取完整性\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "    \n", "    返回:\n", "        (提取的文本, 表格列表)\n", "    \"\"\"\n", "    print(f\"📄 开始提取PDF文件: {os.path.basename(pdf_path)}\")\n", "    \n", "    # 方法1: PyMuPDF提取文本\n", "    pymupdf_text, _ = extract_text_with_pymupdf(pdf_path)\n", "    \n", "    # 方法2: pdfplumber提取文本和表格\n", "    pdfplumber_text, tables = extract_text_with_pdfplumber(pdf_path)\n", "    \n", "    # 选择最佳文本提取结果\n", "    if pymupdf_text and len(pymupdf_text.strip()) > len(pdfplumber_text.strip()):\n", "        best_text = pymupdf_text\n", "        print(f\"✅ 使用PyMuPDF提取的文本 (长度: {len(best_text)} 字符)\")\n", "    else:\n", "        best_text = pdfplumber_text\n", "        print(f\"✅ 使用pdfplumber提取的文本 (长度: {len(best_text)} 字符)\")\n", "    \n", "    print(f\"📊 提取到 {len(tables)} 个表格\")\n", "    \n", "    return best_text, tables\n", "\n", "print(\"✅ PDF文本提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 文本预处理模块"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 文本预处理模块已定义\n"]}], "source": ["def load_stopwords(stopwords_path: str) -> set:\n", "    \"\"\"\n", "    加载停用词\n", "    \n", "    参数:\n", "        stopwords_path: 停用词文件路径\n", "    \n", "    返回:\n", "        停用词集合\n", "    \"\"\"\n", "    stopwords = set()\n", "    \n", "    # 默认停用词\n", "    default_stopwords = {\n", "        '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这',\n", "        '年', '月', '日', '元', '万', '亿', '千', '百', '个', '家', '次', '位', '名', '项', '条', '件', '只', '支', '本', '部', '些', '每', '各', '该', '此', '其', '及', '以', '为', '由', '从', '向', '对', '与', '等'\n", "    }\n", "    stopwords.update(default_stopwords)\n", "    \n", "    # 从文件加载停用词\n", "    if os.path.exists(stopwords_path):\n", "        try:\n", "            with open(stopwords_path, 'r', encoding='utf-8') as f:\n", "                for line in f:\n", "                    word = line.strip()\n", "                    if word:\n", "                        stopwords.add(word)\n", "            print(f\"✅ 从文件加载了 {len(stopwords)} 个停用词\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载停用词文件失败: {e}，使用默认停用词\")\n", "    else:\n", "        print(f\"⚠️ 停用词文件不存在: {stopwords_path}，使用默认停用词\")\n", "    \n", "    return stopwords\n", "\n", "def clean_text(text: str) -> str:\n", "    \"\"\"\n", "    清洗文本，去除特殊字符等\n", "    \n", "    参数:\n", "        text: 待清洗的文本\n", "    \n", "    返回:\n", "        清洗后的文本\n", "    \"\"\"\n", "    # 去除URL\n", "    text = re.sub(r'https?://\\S+|www\\.\\S+', '', text)\n", "    \n", "    # 去除HTML标签\n", "    text = re.sub(r'<.*?>', '', text)\n", "    \n", "    # 去除邮箱\n", "    text = re.sub(r'\\S*@\\S*\\s?', '', text)\n", "    \n", "    # 保留中文、英文、数字和基本标点\n", "    text = re.sub(r'[^\\u4e00-\\u9fa5a-zA-Z0-9.,，。、；：''\\\"\\\"（）()？?!！\\s]+', ' ', text)\n", "    \n", "    # 去除多余的空白字符\n", "    text = re.sub(r'\\s+', ' ', text).strip()\n", "    \n", "    return text\n", "\n", "def preprocess_text(text: str, stopwords_path: str, min_word_len: int = 2) -> Tuple[List[str], str]:\n", "    \"\"\"\n", "    文本预处理：分词、去停用词、过滤\n", "    \n", "    参数:\n", "        text: 待处理的文本\n", "        stopwords_path: 停用词文件路径\n", "        min_word_len: 最小词长度\n", "    \n", "    返回:\n", "        (过滤后的词列表, 过滤后的文本)\n", "    \"\"\"\n", "    print(\"🔄 开始文本预处理...\")\n", "    \n", "    if not text or len(text.strip()) == 0:\n", "        print(\"❌ 输入文本为空\")\n", "        return [], \"\"\n", "    \n", "    # 清洗文本\n", "    cleaned_text = clean_text(text)\n", "    print(f\"📝 文本清洗完成，长度: {len(cleaned_text)} 字符\")\n", "    \n", "    # 加载停用词\n", "    stopwords = load_stopwords(stopwords_path)\n", "    \n", "    # 分词\n", "    print(\"✂️ 开始分词...\")\n", "    words = jieba.lcut(cleaned_text)\n", "    print(f\"📊 分词完成，共 {len(words)} 个词\")\n", "    \n", "    # 过滤词语\n", "    filtered_words = []\n", "    for word in words:\n", "        word = word.strip()\n", "        if (len(word) >= min_word_len and \n", "            word not in stopwords and \n", "            not word.isdigit() and \n", "            not re.match(r'^[\\W_]+$', word)):\n", "            filtered_words.append(word)\n", "    \n", "    # 重新组合文本\n", "    filtered_text = ' '.join(filtered_words)\n", "    \n", "    print(f\"✅ 文本预处理完成，过滤后共 {len(filtered_words)} 个有效词\")\n", "    \n", "    return filtered_words, filtered_text\n", "\n", "print(\"✅ 文本预处理模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 关键词提取模块"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 关键词提取模块已定义\n"]}], "source": ["def extract_keywords_textrank4zh(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TextRank4zh提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    if not TEXTRANK4ZH_AVAILABLE:\n", "        return []\n", "    \n", "    try:\n", "        tr4w = TextRank4Keyword()\n", "        tr4w.analyze(text=text, lower=True, window=2)\n", "        keywords = tr4w.get_keywords(num=num_keywords, word_min_len=2)\n", "        return [(item.word, item.weight) for item in keywords]\n", "    except Exception as e:\n", "        print(f\"TextRank4zh提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_jieba(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用jieba的TextRank提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        keywords = jieba.analyse.textrank(text, topK=num_keywords, withWeight=True)\n", "        return list(keywords)\n", "    except Exception as e:\n", "        print(f\"jieba TextRank提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords_tfidf(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    使用TF-IDF提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    try:\n", "        # 分词\n", "        words = jieba.lcut(text)\n", "        text_processed = ' '.join(words)\n", "        \n", "        # TF-IDF\n", "        vectorizer = TfidfVectorizer(max_features=num_keywords*2, ngram_range=(1, 2))\n", "        tfidf_matrix = vectorizer.fit_transform([text_processed])\n", "        \n", "        # 获取特征名和权重\n", "        feature_names = vectorizer.get_feature_names_out()\n", "        tfidf_scores = tfidf_matrix.toarray()[0]\n", "        \n", "        # 排序并返回前num_keywords个\n", "        word_scores = list(zip(feature_names, tfidf_scores))\n", "        word_scores.sort(key=lambda x: x[1], reverse=True)\n", "        \n", "        return word_scores[:num_keywords]\n", "    except Exception as e:\n", "        print(f\"TF-IDF提取失败: {e}\")\n", "        return []\n", "\n", "def extract_keywords(text: str, num_keywords: int = 20) -> List[Tuple[str, float]]:\n", "    \"\"\"\n", "    综合多种方法提取关键词\n", "    \n", "    参数:\n", "        text: 待提取关键词的文本\n", "        num_keywords: 提取的关键词数量\n", "    \n", "    返回:\n", "        关键词列表，每个元素为(词, 权重)元组\n", "    \"\"\"\n", "    print(\"🔍 开始提取关键词...\")\n", "    \n", "    all_keywords = defaultdict(float)\n", "    \n", "    # 方法1: TextRank4zh\n", "    if TEXTRANK4ZH_AVAILABLE:\n", "        keywords_tr4zh = extract_keywords_textrank4zh(text, num_keywords)\n", "        if keywords_tr4zh:\n", "            print(f\"✅ TextRank4zh提取到 {len(keywords_tr4zh)} 个关键词\")\n", "            for word, weight in keywords_tr4zh:\n", "                all_keywords[word] += weight * 0.4\n", "    \n", "    # 方法2: <PERSON><PERSON><PERSON> TextRank\n", "    keywords_jieba = extract_keywords_jieba(text, num_keywords)\n", "    if keywords_jieba:\n", "        print(f\"✅ jieba TextRank提取到 {len(keywords_jieba)} 个关键词\")\n", "        for word, weight in keywords_jieba:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 方法3: TF-IDF\n", "    keywords_tfidf = extract_keywords_tfidf(text, num_keywords)\n", "    if keywords_tfidf:\n", "        print(f\"✅ TF-IDF提取到 {len(keywords_tfidf)} 个关键词\")\n", "        for word, weight in keywords_tfidf:\n", "            all_keywords[word] += weight * 0.3\n", "    \n", "    # 合并并排序\n", "    if not all_keywords:\n", "        print(\"❌ 所有方法都未能提取到关键词\")\n", "        return []\n", "    \n", "    sorted_keywords = sorted(all_keywords.items(), key=lambda x: x[1], reverse=True)\n", "    result = sorted_keywords[:num_keywords]\n", "    \n", "    print(f\"✅ 关键词提取完成，共 {len(result)} 个关键词\")\n", "    \n", "    return result\n", "\n", "print(\"✅ 关键词提取模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 情感分析模块"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 情感分析模块已定义\n"]}], "source": ["def load_sentiment_dict(positive_path: str, negative_path: str) -> Dict[str, float]:\n", "    \"\"\"\n", "    加载情感词典\n", "    \n", "    参数:\n", "        positive_path: 正面词典文件路径\n", "        negative_path: 负面词典文件路径\n", "    \n", "    返回:\n", "        情感词典，正面词为正值，负面词为负值\n", "    \"\"\"\n", "    sentiment_dict = {}\n", "    \n", "    # 默认情感词\n", "    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']\n", "    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']\n", "    \n", "    # 添加默认词\n", "    for word in default_positive:\n", "        sentiment_dict[word] = 1.0\n", "    for word in default_negative:\n", "        sentiment_dict[word] = -1.0\n", "    \n", "    # 加载正面词典\n", "    if os.path.exists(positive_path):\n", "        try:\n", "            if positive_path.endswith('.csv'):\n", "                df = pd.read_csv(positive_path)\n", "                if not df.empty:\n", "                    words = df.iloc[:, 0].tolist()\n", "                    for word in words:\n", "                        if isinstance(word, str) and word.strip():\n", "                            sentiment_dict[word.strip()] = 1.0\n", "            else:\n", "                with open(positive_path, 'r', encoding='utf-8') as f:\n", "                    for line in f:\n", "                        word = line.strip()\n", "                        if word:\n", "                            sentiment_dict[word] = 1.0\n", "            print(f\"✅ 加载正面词典: {positive_path}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载正面词典失败: {e}\")\n", "    \n", "    # 加载负面词典\n", "    if os.path.exists(negative_path):\n", "        try:\n", "            if negative_path.endswith('.csv'):\n", "                df = pd.read_csv(negative_path)\n", "                if not df.empty:\n", "                    words = df.iloc[:, 0].tolist()\n", "                    for word in words:\n", "                        if isinstance(word, str) and word.strip():\n", "                            sentiment_dict[word.strip()] = -1.0\n", "            else:\n", "                with open(negative_path, 'r', encoding='utf-8') as f:\n", "                    for line in f:\n", "                        word = line.strip()\n", "                        if word:\n", "                            sentiment_dict[word] = -1.0\n", "            print(f\"✅ 加载负面词典: {negative_path}\")\n", "        except Exception as e:\n", "            print(f\"⚠️ 加载负面词典失败: {e}\")\n", "    \n", "    print(f\"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词\")\n", "    return sentiment_dict\n", "\n", "def sentiment_analysis_by_dict(text: str, keywords: List[Tuple[str, float]], sentiment_dict: Dict[str, float]) -> Tuple[float, List[Tuple[str, float, float]], List[Tuple[str, float]]]:\n", "    \"\"\"\n", "    基于情感词典的情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "        sentiment_dict: 情感词典\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表, 匹配的情感词列表)\n", "    \"\"\"\n", "    print(\"📊 开始基于词典的情感分析...\")\n", "    \n", "    # 分词\n", "    words = jieba.lcut(text)\n", "    \n", "    # 计算整体情感得分\n", "    total_score = 0\n", "    matched_words = []\n", "    \n", "    for word in words:\n", "        if word in sentiment_dict:\n", "            score = sentiment_dict[word]\n", "            total_score += score\n", "            matched_words.append((word, score))\n", "    \n", "    # 归一化整体得分\n", "    if len(words) > 0:\n", "        overall_score = total_score / len(words)\n", "    else:\n", "        overall_score = 0\n", "    \n", "    # 计算关键词情感得分\n", "    keyword_scores = []\n", "    for keyword, weight in keywords:\n", "        if keyword in sentiment_dict:\n", "            score = sentiment_dict[keyword]\n", "        else:\n", "            # 如果关键词不在词典中，检查是否包含情感词\n", "            score = 0\n", "            for word in sentiment_dict:\n", "                if word in keyword:\n", "                    score += sentiment_dict[word] * 0.5\n", "        \n", "        keyword_scores.append((keyword, score, weight))\n", "    \n", "    print(f\"✅ 词典情感分析完成，整体得分: {overall_score:.4f}，匹配 {len(matched_words)} 个情感词\")\n", "    \n", "    return overall_score, keyword_scores, matched_words\n", "\n", "print(\"✅ 情感分析模块已定义\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 多种情感分析方法已定义\n"]}], "source": ["def sentiment_analysis_by_snownlp(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    使用SnowNLP进行情感分析\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表)\n", "    \"\"\"\n", "    if not SNOWNLP_AVAILABLE:\n", "        print(\"⚠️ SnowNLP不可用，使用简单规则\")\n", "        return sentiment_analysis_by_rules(text, keywords)\n", "    \n", "    try:\n", "        # 分析整体文本\n", "        s = SnowNLP(text)\n", "        # SnowNLP返回[0,1]，转换为[-1,1]\n", "        overall_score = 2 * s.sentiments - 1\n", "        \n", "        # 分析关键词\n", "        keyword_scores = []\n", "        for keyword, weight in keywords:\n", "            s = SnowNLP(keyword)\n", "            score = 2 * s.sentiments - 1\n", "            keyword_scores.append((keyword, score, weight))\n", "        \n", "        print(f\"✅ SnowNLP情感分析完成，整体得分: {overall_score:.4f}\")\n", "        return overall_score, keyword_scores\n", "        \n", "    except Exception as e:\n", "        print(f\"❌ SnowNLP分析失败: {e}，使用备选方法\")\n", "        return sentiment_analysis_by_rules(text, keywords)\n", "\n", "def sentiment_analysis_by_rules(text: str, keywords: List[Tuple[str, float]]) -> Tuple[float, List[Tuple[str, float, float]]]:\n", "    \"\"\"\n", "    使用简单规则进行情感分析（备选方案）\n", "    \n", "    参数:\n", "        text: 待分析的文本\n", "        keywords: 关键词列表\n", "    \n", "    返回:\n", "        (整体情感得分, 关键词情感得分列表)\n", "    \"\"\"\n", "    print(\"📊 开始基于规则的情感分析...\")\n", "    \n", "    # 简单的规则基础情感分析\n", "    positive_words = ['好', '优秀', '增长', '上涨', '盈利', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新']\n", "    negative_words = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机']\n", "    \n", "    words = jieba.lcut(text)\n", "    pos_count = sum(1 for word in words if any(pw in word for pw in positive_words))\n", "    neg_count = sum(1 for word in words if any(nw in word for nw in negative_words))\n", "    \n", "    if len(words) > 0:\n", "        overall_score = (pos_count - neg_count) / len(words)\n", "    else:\n", "        overall_score = 0\n", "    \n", "    # 分析关键词\n", "    keyword_scores = []\n", "    for keyword, weight in keywords:\n", "        pos_in_keyword = sum(1 for pw in positive_words if pw in keyword)\n", "        neg_in_keyword = sum(1 for nw in negative_words if nw in keyword)\n", "        \n", "        if pos_in_keyword > 0 or neg_in_keyword > 0:\n", "            score = (pos_in_keyword - neg_in_keyword) / max(1, pos_in_keyword + neg_in_keyword)\n", "        else:\n", "            score = 0\n", "        \n", "        keyword_scores.append((keyword, score, weight))\n", "    \n", "    print(f\"✅ 规则情感分析完成，整体得分: {overall_score:.4f}\")\n", "    return overall_score, keyword_scores\n", "\n", "print(\"✅ 多种情感分析方法已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 可视化模块"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 可视化模块已定义\n"]}], "source": ["# 定义颜色方案\n", "COLORS = {\n", "    'positive': '#2E8B57',  # 海绿色\n", "    'negative': '#DC143C',  # 深红色\n", "    'neutral': '#708090',   # 石板灰\n", "    'background': '#F8F9FA',\n", "    'highlight': '#4169E1',  # 皇家蓝\n", "    'secondary': '#FFD700'   # 金色\n", "}\n", "\n", "def create_sentiment_comparison_chart(dict_score: float, snownlp_score: float, rules_score: float) -> None:\n", "    \"\"\"\n", "    创建多种情感分析方法对比图表\n", "    \n", "    参数:\n", "        dict_score: 词典方法得分\n", "        snownlp_score: SnowNLP方法得分\n", "        rules_score: 规则方法得分\n", "    \"\"\"\n", "    fig, ax = plt.subplots(figsize=(12, 6))\n", "    \n", "    methods = ['Dictionary Analysis', 'SnowNLP Analysis', 'Rules Analysis']\n", "    scores = [dict_score, snownlp_score, rules_score]\n", "    \n", "    # 确定颜色\n", "    colors = [COLORS['positive'] if s > 0 else COLORS['negative'] if s < 0 else COLORS['neutral'] for s in scores]\n", "    \n", "    # 创建条形图\n", "    bars = ax.bar(methods, scores, color=colors, alpha=0.8, edgecolor='black', linewidth=1)\n", "    \n", "    # 添加数值标签\n", "    for bar, score in zip(bars, scores):\n", "        height = bar.get_height()\n", "        ax.text(bar.get_x() + bar.get_width()/2., height + (0.02 if height >= 0 else -0.05),\n", "                f'{score:.3f}', ha='center', va='bottom' if height >= 0 else 'top',\n", "                fontweight='bold', fontsize=12)\n", "    \n", "    # 添加零线\n", "    ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title('Multiple Sentiment Analysis Methods Comparison', fontsize=16, fontweight='bold', pad=20)\n", "    ax.set_ylabel('Sentiment Score\\n(Negative < 0 < Positive)', fontsize=12, fontweight='bold')\n", "    \n", "    # 设置y轴范围\n", "    y_max = max(abs(min(scores)), abs(max(scores)), 0.5)\n", "    ax.set_ylim(-y_max*1.2, y_max*1.2)\n", "    \n", "    # 添加网格\n", "    ax.grid(axis='y', alpha=0.3, linestyle='--')\n", "    \n", "    # 旋转x轴标签\n", "    plt.xticks(rotation=45, ha='right')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_keyword_sentiment_chart(keyword_scores: List[Tuple[str, float, float]], method_name: str, top_n: int = 15) -> None:\n", "    \"\"\"\n", "    创建关键词情感分布图表\n", "    \n", "    参数:\n", "        keyword_scores: 关键词情感得分列表\n", "        method_name: 方法名称\n", "        top_n: 显示的关键词数量\n", "    \"\"\"\n", "    if not keyword_scores:\n", "        print(f\"No keyword sentiment data available for {method_name}\")\n", "        return\n", "    \n", "    # 按权重排序并选择前top_n个\n", "    sorted_keywords = sorted(keyword_scores, key=lambda x: x[2], reverse=True)[:top_n]\n", "    \n", "    keywords = [item[0] for item in sorted_keywords]\n", "    scores = [item[1] for item in sorted_keywords]\n", "    weights = [item[2] for item in sorted_keywords]\n", "    \n", "    # 按情感得分排序\n", "    sorted_indices = sorted(range(len(scores)), key=lambda i: scores[i])\n", "    sorted_keywords = [keywords[i] for i in sorted_indices]\n", "    sorted_scores = [scores[i] for i in sorted_indices]\n", "    \n", "    # 创建图表\n", "    fig, ax = plt.subplots(figsize=(12, 8))\n", "    \n", "    # 确定颜色\n", "    colors = [COLORS['positive'] if s > 0.05 else COLORS['negative'] if s < -0.05 else COLORS['neutral'] for s in sorted_scores]\n", "    \n", "    # 创建水平条形图\n", "    bars = ax.barh(sorted_keywords, sorted_scores, color=colors, alpha=0.8, edgecolor='black', linewidth=0.5)\n", "    \n", "    # 添加数值标签\n", "    for bar in bars:\n", "        width = bar.get_width()\n", "        ax.text(width + (0.02 if width >= 0 else -0.02),\n", "                bar.get_y() + bar.get_height()/2,\n", "                f'{width:.3f}',\n", "                ha='left' if width >= 0 else 'right',\n", "                va='center', fontweight='bold', fontsize=9)\n", "    \n", "    # 添加零线\n", "    ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)\n", "    \n", "    # 设置标题和标签\n", "    ax.set_title(f'Keyword Sentiment Distribution ({method_name})', fontsize=14, fontweight='bold', pad=20)\n", "    ax.set_xlabel('Sentiment Score', fontsize=12, fontweight='bold')\n", "    \n", "    # 设置x轴范围\n", "    if sorted_scores:\n", "        x_max = max(abs(min(sorted_scores)), abs(max(sorted_scores)), 0.3)\n", "        ax.set_xlim(-x_max*1.2, x_max*1.2)\n", "    \n", "    # 添加网格\n", "    ax.grid(axis='x', alpha=0.3, linestyle='--')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "def create_wordcloud(keywords: List[Tuple[str, float]]) -> None:\n", "    \"\"\"\n", "    创建关键词云图\n", "    \n", "    参数:\n", "        keywords: 关键词列表\n", "    \"\"\"\n", "    if not keywords:\n", "        print(\"No keywords available for word cloud\")\n", "        return\n", "    \n", "    try:\n", "        # 准备词频字典\n", "        word_freq = {word: weight for word, weight in keywords}\n", "        \n", "        # 创建词云\n", "        wordcloud = WordCloud(\n", "            width=800, height=400,\n", "            background_color='white',\n", "            max_words=50,\n", "            colormap='viridis',\n", "            font_path=None  # 使用系统默认字体\n", "        ).generate_from_frequencies(word_freq)\n", "        \n", "        # 显示词云\n", "        plt.figure(figsize=(12, 6))\n", "        plt.imshow(wordcloud, interpolation='bilinear')\n", "        plt.axis('off')\n", "        plt.title('Keywords Word Cloud', fontsize=16, fontweight='bold', pad=20)\n", "        plt.tight_layout()\n", "        plt.show()\n", "        \n", "    except Exception as e:\n", "        print(f\"Word cloud generation failed: {e}\")\n", "        # 创建简单的条形图作为替代\n", "        top_words = keywords[:20]\n", "        words = [item[0] for item in top_words]\n", "        weights = [item[1] for item in top_words]\n", "        \n", "        plt.figure(figsize=(12, 8))\n", "        plt.barh(words, weights, color=COLORS['highlight'], alpha=0.7)\n", "        plt.xlabel('Weight')\n", "        plt.title('Top Keywords (Alternative to Word Cloud)')\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "print(\"✅ 可视化模块已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 主要执行流程"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 主要执行流程已定义\n"]}], "source": ["def analyze_research_report(pdf_path: str, stopwords_path: str = 'data/stopwords.txt', \n", "                          positive_dict_path: str = 'data/正面词典.csv', \n", "                          negative_dict_path: str = 'data/负面词典.csv') -> Dict:\n", "    \"\"\"\n", "    完整的研报情感分析流程\n", "    \n", "    参数:\n", "        pdf_path: PDF文件路径\n", "        stopwords_path: 停用词文件路径\n", "        positive_dict_path: 正面词典路径\n", "        negative_dict_path: 负面词典路径\n", "    \n", "    返回:\n", "        分析结果字典\n", "    \"\"\"\n", "    print(\"🚀 开始研报情感分析...\")\n", "    print(\"=\" * 60)\n", "    \n", "    results = {}\n", "    \n", "    # 1. PDF文本提取\n", "    print(\"\\n📄 步骤1: PDF文本提取\")\n", "    text, tables = extract_text_and_tables_from_pdf(pdf_path)\n", "    \n", "    if not text or len(text.strip()) < 100:\n", "        print(\"❌ PDF文本提取失败或文本过短\")\n", "        return {}\n", "    \n", "    results['original_text'] = text\n", "    results['tables'] = tables\n", "    \n", "    # 2. 文本预处理\n", "    print(\"\\n🔄 步骤2: 文本预处理\")\n", "    filtered_words, filtered_text = preprocess_text(text, stopwords_path)\n", "    \n", "    if not filtered_words:\n", "        print(\"❌ 文本预处理失败\")\n", "        return {}\n", "    \n", "    results['filtered_text'] = filtered_text\n", "    results['filtered_words'] = filtered_words\n", "    \n", "    # 3. 关键词提取\n", "    print(\"\\n🔍 步骤3: 关键词提取\")\n", "    keywords = extract_keywords(filtered_text, num_keywords=20)\n", "    \n", "    if not keywords:\n", "        print(\"❌ 关键词提取失败\")\n", "        return {}\n", "    \n", "    results['keywords'] = keywords\n", "    \n", "    # 4. 加载情感词典\n", "    print(\"\\n📚 步骤4: 加载情感词典\")\n", "    sentiment_dict = load_sentiment_dict(positive_dict_path, negative_dict_path)\n", "    results['sentiment_dict'] = sentiment_dict\n", "    \n", "    # 5. 多种情感分析方法\n", "    print(\"\\n📊 步骤5: 多种情感分析方法对比\")\n", "    \n", "    # 方法1: 词典法\n", "    dict_score, dict_keywords, matched_words = sentiment_analysis_by_dict(text, keywords, sentiment_dict)\n", "    results['dict_analysis'] = {\n", "        'overall_score': dict_score,\n", "        'keyword_scores': dict_keywords,\n", "        'matched_words': matched_words\n", "    }\n", "    \n", "    # 方法2: SnowNL<PERSON>法\n", "    snownlp_score, snownlp_keywords = sentiment_analysis_by_snownlp(text, keywords)\n", "    results['snownlp_analysis'] = {\n", "        'overall_score': snownlp_score,\n", "        'keyword_scores': snownlp_keywords\n", "    }\n", "    \n", "    # 方法3: 规则法\n", "    rules_score, rules_keywords = sentiment_analysis_by_rules(text, keywords)\n", "    results['rules_analysis'] = {\n", "        'overall_score': rules_score,\n", "        'keyword_scores': rules_keywords\n", "    }\n", "    \n", "    # 6. 结果汇总\n", "    print(\"\\n📈 步骤6: 结果汇总\")\n", "    print(\"=\" * 40)\n", "    print(f\"词典法情感得分: {dict_score:.4f}\")\n", "    print(f\"SnowNLP情感得分: {snownlp_score:.4f}\")\n", "    print(f\"规则法情感得分: {rules_score:.4f}\")\n", "    \n", "    # 计算平均得分\n", "    avg_score = (dict_score + snownlp_score + rules_score) / 3\n", "    results['average_score'] = avg_score\n", "    print(f\"平均情感得分: {avg_score:.4f}\")\n", "    \n", "    # 情感倾向判断\n", "    if avg_score > 0.1:\n", "        sentiment_label = \"正面\"\n", "    elif avg_score < -0.1:\n", "        sentiment_label = \"负面\"\n", "    else:\n", "        sentiment_label = \"中性\"\n", "    \n", "    results['sentiment_label'] = sentiment_label\n", "    print(f\"整体情感倾向: {sentiment_label}\")\n", "    print(\"=\" * 40)\n", "    \n", "    print(\"\\n✅ 研报情感分析完成！\")\n", "    \n", "    return results\n", "\n", "print(\"✅ 主要执行流程已定义\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 执行分析和可视化"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ 找到PDF文件: data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n", "🚀 开始研报情感分析...\n", "============================================================\n", "\n", "📄 步骤1: PDF文本提取\n", "📄 开始提取PDF文件: 2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf\n"]}, {"name": "stderr", "output_type": "stream", "text": ["CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "CropBox missing from /Page, defaulting to MediaBox\n", "Building prefix dict from the default dictionary ...\n", "Loading model from cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\jieba.cache\n"]}, {"name": "stdout", "output_type": "stream", "text": ["✅ 使用PyMuPDF提取的文本 (长度: 39084 字符)\n", "📊 提取到 101 个表格\n", "\n", "🔄 步骤2: 文本预处理\n", "🔄 开始文本预处理...\n", "📝 文本清洗完成，长度: 37024 字符\n", "✅ 从文件加载了 1317 个停用词\n", "✂️ 开始分词...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading model cost 4.882 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["📊 分词完成，共 17998 个词\n", "✅ 文本预处理完成，过滤后共 8715 个有效词\n", "\n", "🔍 步骤3: 关键词提取\n", "🔍 开始提取关键词...\n", "TextRank4zh提取失败: module 'networkx' has no attribute 'from_numpy_matrix'\n", "✅ jieba TextRank提取到 20 个关键词\n", "✅ TF-IDF提取到 20 个关键词\n", "✅ 关键词提取完成，共 20 个关键词\n", "\n", "📚 步骤4: 加载情感词典\n", "⚠️ 加载正面词典失败: 'utf-8' codec can't decode byte 0xd3 in position 9: invalid continuation byte\n", "⚠️ 加载负面词典失败: 'utf-8' codec can't decode byte 0xb1 in position 10: invalid start byte\n", "📚 情感词典加载完成，共 60 个词\n", "\n", "📊 步骤5: 多种情感分析方法对比\n", "📊 开始基于词典的情感分析...\n", "✅ 词典情感分析完成，整体得分: 0.0033，匹配 111 个情感词\n", "✅ SnowNLP情感分析完成，整体得分: 1.0000\n", "📊 开始基于规则的情感分析...\n", "✅ 规则情感分析完成，整体得分: 0.0023\n", "\n", "📈 步骤6: 结果汇总\n", "========================================\n", "词典法情感得分: 0.0033\n", "SnowNLP情感得分: 1.0000\n", "规则法情感得分: 0.0023\n", "平均情感得分: 0.3352\n", "整体情感倾向: 正面\n", "========================================\n", "\n", "✅ 研报情感分析完成！\n", "\n", "🎨 开始生成可视化图表...\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKAAAAJHCAYAAACnyC68AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8hTgPZAAAACXBIWXMAAA9hAAAPYQGoP6dpAACcK0lEQVR4nO3dB3RU1ff28U3vvXfpiIIURbDQFBRFUARRUBCwYEVUVBRFBUERGypir6jYsIEFFRAVKYooXQEF6dXQa9717N975z8JSUgbkky+n7VmZXLnzp17ppyZu+8+++SIjY2NNQAAAAAAACBCckZqwwAAAAAAAIAQgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAETMwYMHM3oXsp3s9JzHxsbaoUOHMno3kMVkp88IAACZCQEoAMiCXnvtNcuRI0ecy4ABA45YTwfnxYoVO2LdtPr777+T3N66deusd+/e9vbbb8dZfv/994fuc+WVV6Z5P7SNYHvadiR98MEHdtFFF1nlypUtf/78VqhQIatVq5ZdfvnlNmPGDMto27dvt4EDB9ojjzyS6HuldevWFi3mzp1rZ5xxhq1evTrF9/3nn38sZ86coeflkksusczgWL6f40vvz+bR2hdcJk6ceMR6s2fPPmK99Hjvqj8cO3asXXrppSnqz6L5PTBt2jTr1auX1ahRwwoWLGiFCxe2E044wW666SZbtmxZxB8/Ghx33HGh10zPJwAgcQSgACBKfPPNN0csmzNnjsXExBzT/Rg3bpzVqVPH3njjDTt8+LBldQcOHLDOnTtbt27d7OOPP7Y1a9bYvn37bPfu3bZ8+XIbP368tWzZ0u66664M20cdxOs5f/LJJ31/o13fvn2tWbNm9tNPP6Xq/q+88opnTwX0um7cuDEd9xBp6be+++67dH+cxYsXW+PGje2GG26wzZs3W3a3Z88eD8S1adPG3nzzTVu5cqUv27Vrly1atMieeeYZO/HEE23MmDEZvasAgCiSO6N3AACQPnTQoMyjChUqhJZ9++23x3w/3n33Xdu5c2eCtylj5c477/TrTZs2tazg4Ycftk8//dSvK2vm7LPPtnr16nkA6ssvv7R///3Xb1PmUfPmze3CCy885vv4ySef2KZNmxK8rUGDBqHnXFkO0eDVV19N9X0VFFVWWDgF7bTsjjvusOwqoz6bCQWgItFvzZo1y/744490325WpM+AgupTpkzx/5W507ZtWw84KRD72WefeR+uz4Uya5X12aVLl4ze7Uzruuuus23btvn1qlWrZvTuAECmRgAKALI4DZtQMCQ4mLviiiuOOJDTcDGd2c5oCt7oklWDHe+8806c4VrKGGjRooXNnz/f/3/ppZcyJACVFAUTskqw71j4+uuvbdWqVX69RIkSoQPHF1980QYNGpThQ7Cyy2cz6Lc0zEvDKKtUqeLLlV34448/Zqp+K9oouykIPhUpUsQD2MqECmzYsMHfCwsWLPD/ld2p4cfZ9bNxNEHgFgBwdAzBA4As7vTTT08wm2Dv3r2hIUrh66SkfkVK6sIEdVSmT58eWtanTx9fFmScJLa98DpFt99+u2cVqS5JmTJlvCaJ6r+kZliOsrE0PK5o0aJ+MHvSSSfZiBEjQgG75Fi7dm3ourYTrkCBAjZ06FA755xz/FKxYsUE769hP9WqVbN8+fJZ+fLl7eKLL/aMjPjCnwcd9G3ZssWuv/56367qTmn/FSgJp3Vff/310P8PPPBAnPoxidWACl+udfX6aUhOyZIlvZ2dOnWyFStW+LqqcaUMCb0Wek26d+/udZQSogPbc88914M7en6ULaYDtK1btx6xrvYn2IclS5b4e+ess87yg+LixYt7lkZ41kqwz+GqV6/uy7T/yfHyyy/HyW4LMgb/+usvmzp1aoL3CX/faoip2q7aX3ouFEhR5puG8SX2HtTBvZ7X3Llz+/tQWWl6nRTAPJrLLrss9Niq8RXfhx9+GLq9UaNGoeUKrOk9pKwWPWaePHmsXLly1qFDB5s0aVKSbYz/WdfwNdV0U1+h97Auuq5gtzIvU+O0005LsN9SnxU8L0n1W6LglfoJfT60T8rU0f/a3/jvM/VFAb3PkqorpQwhDT07/vjj/XNXs2ZNf70UHEvIwoULPQtGNeH0ni9VqpS1a9fOJkyYEGeoZzi1U58T1ejT502B66VLlybZ3i+++MLOP/98fx31eup1Vb0mZe4l9PlKrG2jR48O/T9y5Mg4wSfR9oM+u3bt2p4dl9D21T71e+rTgvfEVVdd5Z/lpGpb6XtGnzU9/2qD7n/zzTeH+mV9Rhs2bOjPvTKKFBiO/1kJ397nn3/uQUsFzdR36PlXX6Yh0mn9TIb3URrS3rNnT//Mq38Khl0n9R2qOojqO7VPeiztn4aCDh8+PNHPv4Kujz/+uA8zVj8a7N+9996b4OsQ/lwoK/f333/3vlP31eOpT/3+++8TfCwAOOZiAQBZzquvvqqjGr/07t07tlatWn69UqVKoXW++eab0DojRowIXY/f9VerVi20fOrUqXFuGzp0aJzHCaxcufKI7cVfFn7R/ia1vfD2XHzxxbFly5Y9Yhs5c+YMbSegbQS3a9vhrr322kT3p1GjRrFbtmxJ1nN90kknhe5XpEiR2Ntuuy125syZsQcPHjzqfefPn59gW4L2vPjii3HWD38eevXqFXvccccleN/Ro0eH7pNYG4PnI3ybrVq1SvQ5L1my5BHbqFixYuyzzz7r+5rQbdu3b4+z/yNHjkx0f/Q+W7FiRZz1tT/B7UOGDEnwcfScB/cL3+f4F73/jmbTpk2xefPm9fULFSoUGxMTE3vnnXeGttG9e/cE7xf+vr3pppsSfK50+eCDD+Lc7/HHH090f3Vp3bp17OHDh5N8P0+ZMiXOc37o0KE4j3HJJZeEbtfjyX///Rdbp06dRB83R44csS+//HKibQz/bM6dO9efq8S2VaxYsdhff/31qM99/PY99NBDoes9evQIraP3QUL9Vvh7N+jfEtuv/Pnzx06aNCnB91n4Jdhm/L7r8ssvT3D9yy677Ig2vfbaa7H58uVL9Pnp0qVL7L59++Lc5/3334/NnTt3gs/lKaeccsR7QJ577rkk30sNGzaM3bZt21Ffg99++y10H+2DPgNJfV4SsmfPntjOnTsnui/6jL311luJvvb9+vXz92D8+3Xo0CH2+uuvT3Cb559/fqLbu/rqqxN8PkuUKOHtTctnMvy9o+c4fN3gM5TYd2h435LQ5eyzzz7ivbF8+fLY448/PtH76Ds+fpvCnws9pt7/8e+XJ08e/94CgIxGAAoAoiAAddVVV4X+X7x4sa8zePDg0LKffvopzo/RSASgtm7d6j9+q1SpElp+3nnn+TIdxCa1vYQCC7rvNddc4z+4ww8s//7776MGoMK3pwOdCy+8MLZ///5xtpXQwWRC3n777QQPBBQY0T4+/fTTsRs3bjzifvv374+tXbt2aP3q1avHXnfddX6f8IOCP/74I9HnIVeuXB4c0r4XLlw4tLxChQqh++j5PfHEE0O3nX766b5MgYvkBqB0KVCgQOyVV14Ze9FFFyXYVr3HOnbsGGe5glOB7777Ls5BZbt27WJvuOGGOIGQFi1axHmO4gcGypcv7wegOhANX673suh9FP+gTs+Nlun9dzThB5/B+2/p0qVxDpwTOugOf98G70MFKfR86TVMqH3aThCY0PPStWvX2IEDBx7RtuCzkdj7WQfD4YHI8M/o7t27Q0EYHYCvX7/elz/yyCOh9fV51OfolltuiW3WrFloud5PClQl1Mbwz2b4/p588smxN954o79GVatWDS0/88wzj/rcx2+f3n9B4LxcuXKhdfQcapna9fXXXyf43t28eXNsqVKlQrc1aNDA32stW7YMLStevHjo+Xj++efjfO70nOg9o+WJBc/btGkTO2DAgCOCAeGBzlmzZsUJfNStW9ffj3rOwj8Ler4CCnwXLVo0dJva0bdv39hu3bodEUQJ3gPqS4L7aLsKxui9pCC1PpvB+vfee+9RX4NXXnkltH69evViU0NB2PD+Ve1Vu9X+YLna8uOPPyb42gfBVD0vp5566hHPvd7vep81btw4zvKFCxcmur2CBQv6Z1IX9WXB8vr164eCtqn5TMbvo3RCQvumPjcIwCf0Hbp27dpQQF39ih7r1ltv9SB3EATXJTwQrNe5adOmcT6j+p5SP1O6dOnQ8sqVK8fp7+I/Fwpkqr8OD06n5DsPACKJABQAREEASmebg/8VEJHgYFMHcgcOHIjzQzQSAaiEfrDHz1hKbgAqPLCh4I5+cAe33XPPPUcNQIUfNIb/wNeP9iAIpYODf/75J1nP96hRoxI8wx5cdFDz4IMPxslOmTBhQpwDvV27dsXZXng2QGLPw7vvvptoICyxA5D4mWDJDUDpYD8Qnt2g52nOnDmh28IP1nQgmtDy8APhvXv3xjmQnDFjRoLvFQUFwoM/55xzTui2Tp06xWlTYgGBoznhhBMSfK+fccYZoeWPPvroEfcLf9/q+VBANxCepaMgQUCBLQV9FMjQeyNc27ZtQ/d57733jvo63n///aHlyuwLz6QJlis4GFDAKaHXVcEsZYvogFbvwTVr1hz1sxkEEBVoC38P6/2n11yBn7FjxyYrIzB+ACo8cK5ArLJxgs9Z+/bt/TVK6L0bHmDT86v+LRCeQTNs2LCjfg4S6s8UYAgoSBceMPr0009DtynIGv78672e0OdV75lly5b5cvVt4YGC8D5o8uTJcfYjeA8omBEeYA6nz5MCUoMGDYr9+OOPj/oahPc9p512WmxKrVq1Kk6motoZUPsvuOCCBAOT4a+9MpOC4KCCLuHBFfUDQSaXMq20bnDbhx9+mOD29N4MzwpS4Cs8ABgE41PzmQzvo/TdEf4ZSOo7NPykT3iGn2j7Oily9913e+A+MH78+Dh9SXAyKXgPhAeiw/vY8OdCQau//vorwb5AGVwAkNGoAQUAUSC8lonqqfz333/2yy+/hOqoqPZEVqF6IP379w/9rzo7N954Y+j/oEBxYtavXx+qAZM3b16vXRNQTYygSLhqoSS3rpRqkKiuiepTqR5MfKoNc99993mNjoRm8grqhgTC69EkNAuYqK6Nai0FzjvvvDi3JzbTYGrosVSzJqC6TYEmTZrYySefHPpftUji78OhQ4fi1P665pprQtdVG6ZHjx5Hba/qCZUuXTr0v2oVxX+ctPj555+9Vk9QN6pVq1ah2/r27Ru6Hr/GVnyq96TC8wm9LuH7WadOHXviiSf8Pab3hd5vKur87LPPxqmflZw6UHq/aAbGoObTwYMH/fp7770XWif8fa7XLKB6Y3r+VSBf7+EXXnjBC+vrPZ1QzbL4gm1pRrS6dev6Z1GPq/2ePHmyF7RW/aNcuXJZWvstvYeCtiVWnyn+Z0uvXXj/lpzP1tGo7lpA9Zn0WgZiYmL8r2rxhG//ySef9Pd6eO0u1U4SvfaaWS54H4bX7gmfNU3vedV5i081mSpVqhTq//SaDBkyxL766itfXzWQRo0a5XV/jiZ4foP9Sim9/4L7qX1qZ0Dt13s+8MMPP9jmzZuP2IbaqTaJalmF96nq81RfSVQDSjWojtYPqE8Pf95UX0y1qeJ/Z6T1M6nnN7wfT4rqh6keWFAH6swzz/S6T6oR1bFjR5s4caI99NBDcepvhX+eVRMrvB9WrTq95oHEas6pdl/485lY/wQAGYUAFABEAR2cqACu6AeuDtAUFJD4BWZTIjUHKGmldgQH2+E/5sMDTEkJZjiT/fv3+8FpUKBVFx1sBOIXK06KftQ/+uijXqxaBys6iO/atasfQAVU3FfTmItm9groYCd8HxRUC2hbCRVFD2YFC6iYbLjg9U0PKt4cLvxAWsGacMFBVfj7Q8XSw9ugfQ9vr4IdR3vOk2pverQ1vPi4AoY6CFYRaF2CgstBYev4hYSTu5/xPy8qJqzAhIoQK5Ch4J0COOHFkZPzGVOQIpihTgf0Cnzo+VYAKAisXnDBBaH1+/Xr5weismPHDp+98eqrr7b69et70GnAgAFxPidJUWAjCMBocgB9fhQkUJ+jAuePPPJIqmeqix+ACg8IJ9VvhX+2FLgMf6+dcsopqfp8h1NR6XDhQYcggKMC/UGBcb22CQWmwwOBwWuuGeYC4YGtQHjQIaD+UO/R4L02b948D14Exf71PCrIkVjB83Aqhh3Q5zal1P8l1L6Anodgsgbtz8qVK9O9v4lPgdH4EvvOSMtnMv77IikKoinYq5MgQTBO3wN6X+s1UzF5FZVPyXOb0PvpWPejAJBWWeeUOAAgScro0A9YZT/poDGQVCZBfPF/eCuAc6xp9r74gh/xcrSpwMN/ZOvALTzYE9/RDtj0I18zH61bt87P2AeZPAoIKHtBF2WanXrqqf64er7mzp3rZ53D90MHOuEHUvHpzHT8M+s6+x8uflAuPcXft/DnOKn9TuzAJshuSEhi7Yhke3XQqRm7wmcmDJ/dMD4dOCb2uUnufm7atMmzD//880//X7OVaZvKnlKmw6effpqiNiio9PXXX4dm8VJgKQj8aMav8IN4BV0/+eQTD0RrXc2MpeCR6L2sGd40c+Ls2bMTDILEP6DVrFrvv/++ffTRRx4kUh8jyijTTGB6bjWrW/zn5mgUxFLAQp8zZT8FwQrNtqisOx20H+39poP58P4hXPhzkl6fh6DPCA88J9YnhfcvwTrh64ZnIwWUaZYQBSD1PL355pv+3pk5c6b3N0H2oS6arfK5555Lsm2aXS6gIJoyuuLP7inatrKI9LhdunQJBXTC250cCT03ae1vUvudkdbPZELPU1I0U6Yyn9544w3PUtN3hV4vBcAVPNZFM3FqltDkPLcJvZ/iO5bfGwCQGvRKABAlwg+YZ82aFedALinhQ2fiZ+IkNHwi0hREi39AoayUQDAUJTHht+sAVIEGnQEPLmvWrAldDw/UJUSZMBpicuutt9rAgQMTPGBs2rRpnLPMwb6H78fgwYPj7EP8fSpbtqxlVkcL+ImGzoUf8P3222+JPufKxjnWFCBRwCa5FGhJTXZIOGXDBQe61157rQ/10XA1DcdMTWBEw4yC7BUN31EgIqHhd+GU3aFgmjKGFGzQgXAwhFJBpMceeyxZj63Xtn379v68aOiZXl9lkQRDpZSRo31KS7+lIOyiRYuSNWw4/LM1duzYRD9b4cOq0luQ5Rc8l3p+49PzEgiGkoXv+9KlS4+4j4ZJJqZYsWIe9Fa/tH37dvv+++/tjjvuCN0+btw4DzAmRd8HQX+jYMj48eMTXE8BTD2Ohn0pcy7IuKlWrVqC7QtovWCYooIfNWrUsPTub+L7448/jliW0HdGWj+TiQU6k6LnWsPp9J28bds2DyKHD/l98MEHQwHVoz23Cb2fACCrIQAFAFEioYyN5NR/UgZBQNk7AR2wq8ZISoUHtBI7m58UHVhpWE9AB3fPP/986H+dUU6KspOCYRyq5RF+X2WMKONCFw1ZCmoCJUZDXIL2aGjdbbfddkSWmLavfQ4EB/jhNYZeeeWV0EGZ6KBHZ9NVT+iBBx6wtErrc55WOnOvuiuB8DowOrjSbTpg1/MZXisqtcLP6ienveHD75T98/8nYTniEgyxUYaCMoTSYv78+aHrJUuWDF1XgGTq1KkpHuaqg18dKIveS0FNIQ0/UgZeQO3Qe1vBKmWiKXNJ9JnQcLXwmj3hQ9kSomF6CrAqkK3n5u+///bnXvV2NIyvWbNmyd5WSvqto2Vthn+2FIAKz9RUnTY93+onwrOB0vszoscIajyJgtTh+6GgZ5DBpT44GBLZsmXL0DrKTgsy00SvVRCEC6fMHA3NK1SokGck6f2pTCG1cdiwYXEC4Ed7HbQv6sfCg+PKXosfvAmvu6fXIxhiGLRDlHEVnlmo9odvW/cLf+9HioI64cNmFZAKH94WfGek9TOZkuCYvhf0nOmzo8+tPpd6nVRrT7Wgwk/6BMHu8OdW/VR4EE0BVQ27DCgrDQCyIobgAUCUUF2NYDhLSobfNWrUKFSwXIEfHaDoTLvOpocfHCVX+DAF1UzSthV4CIp/J8f999/vZ/c1PEgHEkEmgw7ANBTpaG655RY/QA4KCusATtuaMmVKaFvKZjra8COdOb/qqqtCQSwdFOhgRweeCrrojHR4UWEdQARnprt162b33HOPZ//orLuGsCgwoMCeDjR1EKyz4uFFk1Mr/DlXbSoFxBQkCC8GHmnKEAsOApVZpoNa1SzR86OhXqL9Ut2g9GhvEPRTwXoV6NVBXUI1WpRREhxg6wAyqQM3DWXTkJigGLmCCqmlfQr/HCiYo+ExKuIcHrBMThHygN77eg+Gi5/9pDYqKKUhP6Kgk4YXKTCrfQiWJzeYq+0FmZHKnlEhZh1I62A+/KD/aNtKzwCU6lnpOVVAWUEQvad0YK9AQlCcWcGf8KLN4Z8RvR/1vtGw18cff9xSS9tX36bggjKG9JlTjR/1MeEBEAVzgqC4+gUNW1RAQdlk+oyolpyGhyWWRaZAtV479Vnqc/Q4CkTpcTUkMsjuU0Zacj5fel9r+JeCwQrw67VThpuCXMF7JAim6T0bHlBWRpMCKkHmlAKayqxT9o6COEEGlwKmwWcp0vQ8aLjgRRdd5IG5Dz74IJSJqu+3IOgXic9kYjSsT0NKtW+TJk3yz46+N7Rf4e8NfVaDjDTVVtN3sAJPel0U/NV3irKz9JroPSJ6roPvNwDIcjJ6Gj4AQMqFTykePmV6v3794kzjPXPmzNBt4cvD/f7777H58uWLc7sumg49fArn8MeJP215uNGjRx+xrfvuuy/Jqd7D21OvXr3Y6tWrH7ENTf2taarDJTZtvaaEv/TSS4/YRvj057NmzUrWc717926fbj6xbQWXJk2a+NT04fT8FylSJNH79OzZM/bQoUPJmio+/muo1yDwwQcfHLHtXr16JbnNpB4rsdfpaLdpKvjE2po3b944U9jHn+Jc+xMuqf3TlPfxtx8+nXm422+/PdnTzs+fPz/ONqdPn37UNif2Wfj1118T/FwF77/g+m233XbU93O4k08+Oc5nYtWqVUeso+nrzzrrrCTfr3ou9N4OJNbG5cuXx1auXDnJbQ0cODDJ5zWh9oW/3jVr1owzjfyBAwd8uaa0T+w9MHHixNg8efIkuk+DBw+Os/66deuOWL9q1apJvobJeZ+OGTMmyf1QP7R///4499F7tUCBAkesW7BgwTh9ePh74PPPP/fPUGKPo/5a/UBy7dixI7ZTp05Jvq7qu/S4Cd23Xbt2id4vf/78se+9916c+yT13k7q+U3stvDttWnTxh8z/n6UKlUqdsGCBWn6TCa1b4Fq1aqF1tF7NvD888/H5siRI9HnqVChQrE//vhjnG0tXLgwtkaNGoneR+/Z8DYd7bkN/wxpPwEgozEEDwCiSHjmQHLqPwVDxnQm/KyzzvL76Cy6MnWUNaIzyimls/033XSTF//WmVtlZYXXtjgaDRtSgV0VcNXwQGUpqF060x8UAT8aDbfRrFBvvfWWz3ak4RbaF82wp9ofyiIIHz6UFJ1RV7aAzqori0tDybQtnTlXhpRmM9IU98qqCB/OGGQuaJjfDTfc4M+D7qN6SRoaqawBXdKjSKyyepQ1pv1RZpb2MaGZoSJNmU8626/nRK+/siCUlaTXTcXcw2dqSwsNu1JmgDLidFEWid678SnLTM9xQJkmRyvQrMLEAdVPSq3GjRv7+1gZMnrNlYGjbAy9TsG08KLsvOTMXhaeWRHQezv+rFei95myLNR2fXaUHan3hTIbdX9ltChbJTkFn5XxoiLkGv6jjAy1RVmSytrQTILK2klLFlH8fis5w4ZFn0VlYSkDTJlaeq+VL1/eM4P0nI4YMSLO+rpNQ970+uq5UDvUnrRSX6ehy+pX9BlX36B+QPuhTEfVPItfXFpZUsoM1OdB2WR6XZRZps9IYn22PlOqWXTdddd5G/R+Cj5fynLTfS+++OJk77c+L8raUt+mrKyg79ByZVgpS0s1qvS4Cd1Xw7OVBaXMI/XZ2he9DsoY1ftF2zxWlOGkTDg9r/q+UH+vzCw9J+Gf50h9JhOjDFRlAOv1UX+sfdP7Q1myysDT8xQ+dFlUb0vva/WlmtFR38f6nKodmkVPt4W3CQCymhyKQmX0TgAAsjdNMR4MRVN9l/ChPQD+R7ViNJQzGIqjAJMOboHsRsXYgzptQ4cO9SASACDzowYUAABAJqU6R8p8UFaQatUEwSdlIB3LLBMAAIC0IgAFAACQSWn4jWZNDJ+5TYXBNexNQ+0AAACyCmpAAQAAZFKqEaZaYqofo3owqhmjTCjNRAYAAJCVUAMKAAAAAAAAEUUGFAAAAAAAACKKABQAAAAAAAAiigAUAAAAAAAAIooAFAAAAAAAACKKABQAAAAAAAAiigAUAAAAAAAAIooAFAAAAAAAACKKABQAAAAAAAAiigAUAAAAAAAAIooAFAAAAAAAACKKABQAAAAAAAAiigAUAAAAAAAAIooAFAAAAAAAACKKABQAAAAAAAAiigAUAAAAAAAAIooAFAAAAAAAACKKABQAAAAAAAAiigAUAAAAAAAAIooAFAAAAAAAACKKABQAAAAAAAAiigAUAAAAAAAAIip3ZDefPR0+fNjWrl1rRYoUsRw5cmT07gAAAAAAAKS72NhY27Fjh1WsWNFy5kw6x4kAVAQo+FSlSpWM3g0AAAAAAICIW716tVWuXDnJdQhARYAyn4IXoGjRohm9OwAAAAAAAOkuJibGE3CCOEhSCEBFQDDsTsEnAlAAAAAAACCaJaf8EEXIAQAAAAAAEFEEoAAAAAAAABBRBKAAAAAAAAAQUQSgAAAAAAAAEFEEoAAAAAAAABBRBKAAAAAAAAAQUQSgAAAAAAAAEFEEoAAAAAAAABBRBKAAAAAAAAAQUQSgAAAAAAAAEFEEoAAAAAAAABBRBKAAAAAAAAAQUQSgAAAAAAAAEFEEoAAAADKJ66+/3nLkyGH9+/c/6roTJ060hg0bWr58+ey4446zkSNHWmxsbJx1FixYYGeffbYVLFjQSpUqZf369bP//vsvzjrbtm2zPn36WMmSJa1QoULWvn17W7x4cbq3DQAAZG+5M3oHAAAAYPbuu+/aCy+8kKx1v/vuO+vatasdPnzYihUrZv/884/dfffdHoDSX9m8ebO1adPG/yqwFBMTY6+88oqtXr3avv7669C2unTpYtOmTbM8efL4ZcqUKda6dWtbsmSJlShRImLtBQAA2QsZUAAAABlo48aNdtNNN1mPHj3s0KFDybrPsGHDPPjUu3dvz2AaM2aMLx81apTt37/frz/77LMefDr++OP9MebOnWs5c+b0ANOsWbN8nenTp4eCT8qWWrdunWdTaf3kBsMAAACSgwAUAABABrr22mvtmWeeserVq1uNGjWOuv7evXttxowZfr1Xr14+ZK9v377+V8PrZs+e7bcFWU7KlNIQvJNOOsmaNm3qyxSECl+nefPmVqdOHStatKh169YtzjoAAADpgQAUAABABtLwuJtvvtl+/fVXq1KlylHX/+uvv0KZUlWrVg1tQzWeZNmyZf536dKlcdaRatWqpXgdAACA9EANKAAAgAz0+uuvW65cuZK9vmo5BZTZFChQoID/DYqMB+uldR0AAID0QAYUAABABkpJ8Ck+Dbs7VusAAACkBQEoAACALER1mgJ79uwJXd+9e7f/1ax44euldR0AAID0QAAKAAAgC1Gx8iBjadWqVaGg0datW/26iolLzZo146wj//77b4rXAQAASA8EoAAAALIQFRw/9dRT/forr7ziBclfe+01i42NteLFi1uzZs38trZt2/rfCRMm2I4dO+yPP/6wuXPn+rJzzjknzjo//fSTLVq0yGtCffjhh3HWAQAASA8EoAAAADKxxx9/3CpXrmytW7cOLbvvvvs8C+rNN9/0oNMNN9zgy++66y7LmzevX9fMeiVLlrQlS5ZY2bJlrUmTJh6sOvfcc+2UU07xddq3b2+nnXaaHThwwBo0aGAVKlTwWfbKly9vV199dQa1GAAARCMCUAAAAJmYspLWrFlj69evDy3r0KGDffTRR9awYUPbv3+/Va1a1UaMGGF33HFHaB0Fk6ZPn27t2rXzYFWRIkWsb9++nhEV0PLPP//cl6vmk7KotP53333ngS0AAID0kiNWvzSygdWrV/uZvY8//jjOGcSEvPXWWzZy5EhbsWKF/6AbNGiQXXXVVSn6oagfcZq+OLxQKAAAAAAAQLRISfwjW2RA/fPPP342T0/I0bz//vvWq1cvT0lXsEq1EZSCPn78+GOyrwAAAAAAANEmqjOgDh8+bK+//rrdfvvt/r9mh5k6dWqSGVB169a1k046yd57773Qsu7du9svv/ziNRGSgwwoAAAAAAAQ7WLIgPqf33//3a677jrr3bu3F+k8mr///tuWLVtmXbp0ibO8a9eutnz5cr8NAAAAAAAAKZPbopjqNylrSTPHTJs27ajrL1682P/WqVMnzvJatWr5XwWg4t8m+/bt80t4BDDIwNIlKPKpixLOwpPOjrY8uH9ql+fMmfOIbad0eWr3nTbRJtpEm2hT2tq0YcMG2759e2jb8WXl5ZlpX2gTbcrqy9OyDRWbL1euXKbp96KxL6dNtIk20aZobtPheOtk2wCUph7WJbn0I1/ip41p1pjwwFJ8Klj+wAMPHLF806ZNtnfvXr9eoEABT0vTNvbs2RNap1ChQr79bdu2+Sw2Ae1DwYIFfdjgwYMHQ8tLlChh+fLl822Hv0FKlSpluXLlso0bN8bZB027rCmXt2zZElqmN4x+aOjx9LiB3LlzW+nSpX3/wtuq6Zz1PO7cudN27doVWk6baBNtok20KXJt0rYeGP6g7dq725YsW2p5cuexmjVqxPmy13I9drUqVUPLdUJk+coVVrxYcatYoUJoudrxz+pVVqZ0GStTunSc776169dZxfIV4sx6tmnzZtu0eZNvW48RWLtunW3/b7vVrF7D2xDQtvUY9erU9R9EgeUrVtiBgwd8eTjaRJtoU+ZoU6H8BW3okPu8j8zofi8a+3LaRJtoE22K9jbt2LHDkiuqa0CFUwZUmzZtkqwBpULjl19+uWdN1axZM7T8zz//9Mynd955xy699NJkZUBVqVLFX8QgmEUUlTbRJtpEm2hTSpYvXbrUOl1ykVU6p5EVKlPsf7dbjjjrx1psCpYHSxNe/r8lORLYRmLL07Iv6bWcNtEm2pSW5bs2/WdrvvrNPn1votWrVy/D+71o7MtpE22iTbQp2tsUExPjgank1ICK6gyolArOVMXPdFL0UBQxTIgigOFnrcLfEOFnrcJfsPgSWx7//qlZntLHjPRy2kSbaBNtSmo5bfrf8uDLXcGnohVLJbgeAKSV+pnwvou+nDaldDltok2pWU6bLGralNg6Cd4v2WtmA5oBT+LPdhf8X79+/QzZLwAAAAAAgKyMAFS8YuM1atSwDz74IM5y/a8heNWqVcuwfQMAAAAAAMiqsvUQPA21W7Rokdd7KlOmjC+79957rU+fPl4YrFOnTvbpp5/ae++9ZxMmTMjo3QUAAAAAAMiSsnUG1K+//motWrSwSZMmhZZdeeWVNm7cOJsyZYpdeOGFXrz8jTfesEsuuSRD9xUAAAAAACCryjYZUJr5Ln7V+ISWybXXXusXAAAAAAAApF22zoACAAAAAABA5BGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQETltgwWExNja9assf/++89Klixp1apVs3z58mX0bgEAAAAAACArB6A2btxo48aNs08//dR+++03i42NDd2WJ08ea9y4sV1yySXWu3dvD0oBAAAAAAAg6zqmQ/B2795tt956qx133HH2wAMP2K+//mqHDx/2AFRw2b9/v82aNctuv/12q1Spkt155522a9euY7mbAAAAAAAAyKoZUPXr17dVq1ZZhQoVrHPnzta2bVtr2LChlStXzgoXLuzD8davX2/z58+3mTNn2meffWaPPvqovf3227Z69epjuasAAAAAAADIigGounXr2jPPPGPnn3++5ciR44jbS5Qo4Zfjjz/eLr30Unvqqafs+++/tyeeeOJY7iYAAAAAAACyagDqq6++SvF9WrZs6RcAAAAAAABkTce0BlR8O3fu9CF27du3txNOOMGXKdtJw/TSy5dffmknn3yyFSxY0GfYGzlyZJyi5/EtWbLEs7PiX+rVq5du+wQAAAAAAJCdZMgseKJaT2eeeaatWLHCA0LBkLwHH3zQHn74YZs+fXqagz4//fSTderUybp3727Dhw+3H374we655x4vfK6/CdGsfDJ16lTLnz9/aHmBAgXStC8AAAAAAADZVYYFoO666y5bvny5tWvXzme927Fjh+3Zs8dKly7tywcPHmwTJ05M02Nopr1GjRrZm2++6f+fe+65duDAAQ9waTa+hIJKCkBplr7WrVun6bEBAAAAAACQwUPwvvjiC88w+vjjj61IkSK+TAGhOXPm+PIZM2akafv79u2zadOmWZcuXeIs79q1qw/9S2z7CkApaAUAAAAAAIAsngEVExPjdZniZyEVKlTIh+Pt3bs3TdvX0L79+/dbnTp14iyvVauW/122bJnXnkooAFW/fn1r0aKFzZs3z4oXL25XXnmlDRs2zPLkyZNosEuX8LaJhvrpIkEtKQ03DK9BdbTlwf1TuzxnzpxHbDuly1O777SJNtEm2kSbUt8mbUf7lsP+b9bY+PPHxqbj8khuO6OWZ6Z9Sa/lmWlf0mt5ZtqX9FqemfYl6eU54vSBGd3vRWNfTptoE22iTdHepsPx1smUASgVHVeA59lnn7VDhw75ssWLF9tDDz3kQ/GaNWuWpu1v377d/xYtWjTO8iDbKggShduwYYNf9EI+8sgjVrVqVfv222/9+urVq238+PEJPpYKm2u4X3ybNm0KBdIUaCtWrJg/rtoXHnDTPm3bts0DZgHttwJ0W7dutYMHD4aWlyhRwvLly+fbDn+DlCpVynLlymUbN26Msw9ly5b153fLli2hZXrDlCtXzh9PjxvInTu3D4HU/oU/P3nz5rWSJUt65tiuXbtCy2kTbaJNtIk2Ra5Netx6depa5cJl7D87ZLksp5XN/X/fabEWa+sO/mf5cuS2UrkKh5YfjD1kGw/tsII58lrxXAVDy/fFHrQth3ZakZz5/RLYfXi/bT+824rlLGgFc+YNLd9xeK9fSuYq7I8R2H5ot+2O3W9lchWx3DlyhZZr23qM8rmLxQmabTwYY4fssFXIXTzO67Tu4HbaRJtoU0a3qXAZy1Gnrvc36r8yut+Lxr6cNtEm2kSbor1NO3bssOTKERs/jHaMfP7559a5c+dEb3/vvffs4osvTvX2f/zxRzvjjDPsm2++sbPOOiu0XE+eMpkUNFIdqnB6IXS/unXrWpUqVULLFRQbMmSILVq0yI4//vhkZUDp/noRgwAYUVTaRJtoE22iTSlZvnTpUut0yUVW9/LWVqRiyf/dHmft7JqxkfzlmWlf0mt5ZtqX9FqemfYlvZZnpn1JavmOtVtt6VvT7NP3JvrkPxnd70VjX06baBNtok3R3qaYmBgPTP33339HJABlmgyojh072oQJE+y2227z7KKACoBrxrq0BJ9EQ+cSynQKonOK/sWnqODZZ599xPLzzz/fA1Dz589PMAClCKAu8ekNoUtCL1h8iS2Pf//ULE/pY0Z6OW2iTbSJNiW1nDb9b3nw5a4shkDcnx2Wrssjue2MWp6Z9iW9lmemfUmv5ZlpX9JreWbal6SXx3o/E9530ZfTppQup020KTXLaZNFTZsSWydTBaCCguC6/Pnnn7Z582arUKGCVatWLcGGp1TNmjU9re2vv/6Kszz4X3We4tPZ5qlTp1qPHj3iRO6CFDWluAEAAAAAACCLzIJXu3ZtGzFihK1du9avq+i3sp/SI/gkmkmvZcuW9tFHH8VJJfvggw88OyqhGlNr1qyx6667ztcJp0wtjYts2rRpuuwbAAAAAABAdpJhGVDLly+3e++914YOHWrt2rWzvn37ek2oxGaaSw0Nm9OQuksuucS3/9NPP9mjjz7qRcU13E7D81TXSdlSZcqUsVatWlnr1q3t1ltv/V/x13r1bNKkSTZmzBi/n8Y1AgAAAAAAIItkQD388MPWoEEDr/L+5ZdfWvfu3X0I3oABA+y3335Ll8do27atffjhhz607sILL/RZ7BRIGjRokN/+66+/euaVgkyiIXsff/yx9enTxx5//HG74IILbMqUKfb88897rSoAAAAAAACkXIbNghdYuHChvfnmm/bOO+94MfJgCF6jRo3sl19+saxImVUqcp6cKvAAACRkyZIldv7FnaxerzZWtGKpjN4dAFEoZu0WW/LGVJv04aee+Q8AQCTjHxmWARU44YQTPBvq559/tn79+vkyxcTSKwsKAAAAAAAAGStDZ8FTnSUNkdPQuO++++5/003HxlqhQoW8bhMAAAAAAACyvgwLQF122WX22Wef2Z49e0Kz1DVv3tyzoC699FIPQgEAAAAAACDry7AA1IQJE/yvZp+74oorPPB0/PHHZ9TuAAAAAAAAINoCUOeee65dddVV1qlTJ8udO0NHAgIAAAAAACCCMizyM3ny5Ix6aAAAAAAAAERrAKp+/fpWoUIF+/bbb/16UnLkyGELFy48ZvsGAAAAAACAKAhALVmyxHbu3Bm6frQAFAAAAAAAALK+YxqAGjp0qBUtWtSv33fffQSZAAAAAAAAsoFjHoAK3H///Umue/jw4WOwRwAAAAAAAIi0nJZBatSoYWecccYRyw8dOmSVKlWypk2bZsh+AQAAAAAAIItmQMXGxtrbb7/tASb5+++/LSYmxt54440466lG1ObNm23btm3HatcAAAAAAACQ2QJQ//77r40cOdKmTJlia9assV27dtltt91mPXr0SDRzSfWeZsyYYS+++GLofwWZ+vTpk2Cw6rjjjkvNrgEAAAAAACCrD8FbtmyZNW7c2MaNG2d//fWX7d2715e/8MIL1rp1a5s1a1ai9x0+fLiVKlXK8ubNG1qm68ElX758VqhQIWvYsKGNGTMmtW0CAAAAAABAVg5ADR482LZu3Wq9e/e2EiVK+DIFoRo1auSZUEOGDEn0vqVLl7aNGzfanj17PMtJtZ50Pbjs3r3bh+X99ttvds4556StZQAAAAAAAMiaAaipU6d6lpIyngoWLOjL8ufP78PxtHzu3LnJ2o5muVu1alXK9xgAAAAAAADRXQNK2U4aLpc7d9y7Hjx40A4cOGA5cyYe07r++uutZMmSPhRP15OiGlHPPvtsSncPAAAAAAAAWT0A1axZMy8mfuedd9q+fft82RdffGGPPfaYB6BOO+20RO+rulFVqlTxAJSuK8iUEA3PIwAFAAAAAACQTQNQI0aMsLZt29ro0aNDyzp27OhBI2VF3XfffYnet2XLllauXLnQ9cQCUAAAAAAAAMjGAShlOKkO1B133GE///yzHTp0yHLlyuXLhw0b5oGlxEybNi3B6wAAAAAAAIheKQ5ATZw40Vq0aOHD8FQPatu2bVa2bFkPQqXV+vXrfYa9+vXrp3lbAAAAAAAAyKKz4F1zzTV23HHH2ebNm332uwoVKqQ6+PTggw/aww8/7Ndfe+01q1q1qjVo0MDOOOMM2759e6q2CQAAAAAAgCwegCpatKgVKFDAZ7NLi0ceecQeeOABmz59uhcvv/32230mPdWSmjlzpteaAgAAAAAAQDYMQCkwtGfPHrvgggvsnXfe8VpOs2bNstmzZ4cuyaGMJ7nooot8OJ+G3un6Tz/95Ms//vjjlO4aAAAAAAAAoqEGVI8ePfzvl19+6Zf4NLOdMpmOZvXq1VaiRAkf0nf//ff7/RTUat68uZUqVcrWrFmT0l0DAAAAAABANASgNEQuLbcHNIxv3759vv63337ryzSDXkxMjP33339WunTplO4aAAAAAAAAoiEAtXLlynR5YM1098MPP3jBcdV8qlu3rpUvX95at27tGVSnnnpqujwOAAAAAAAAslgAqlq1aqHr+/fv99pNGjKXJ0+eFG1nyJAh1rFjRw8+yeDBgz0rasGCBVawYEG77777UrprAAAAAAAAiIYi5PL333/bhRde6DPiVapUyf9269bN/v3332Rvo127djZnzhx79NFHbcqUKdarVy+vA3X99df7zHiNGjVKza4BAAAAAAAgq2dAqXh4ixYtbOPGjaF6T6rl9NFHH9nPP//sM+JVrFgxWdtq2LChX5RJtX79es+kGj16dMpbAQAAAAAAgOjJgNLQuQ0bNliNGjXs5Zdftu+++85eeukl/3/t2rV27733HtNMKgAAAAAAAERZBtRXX31lOXPmtG+++SZUD0qFw9u0aWO1atWySZMmJTuTqnnz5rZp06Y0Z1IBAAAAAAAgijKgYmJirHDhwnGKkUv16tV9uW5PbiaVhvGlRyYVAAAAAAAAoigDqmbNmrZo0SJ7++23rUePHqHlb731lu3YscMaNGhwTDOpAAAAAAAAEGUZUFdeeaUPmbviiis8YNS3b1//27t3b5/FTrPZHctMKgAAAAAAAERZAGrgwIFeOFxBqO+//95ef/11/6v/zz33XL89uZlUyphSJlW4IJOqdu3aKd01AAAAAAAARMMQPA2bU6HwTz75xCZOnGjr16+38uXL2/nnn29du3b1LKjkZlINGjTIM6leeOEFr/20YsUKmzFjRooyqQAAAAAAABBlAahA586d/SKHDx/2oFFyg0+iTKkff/zRPv74Y8+gUuApmA2vQ4cOyc6kAgAAAAAAQJQNwZPXXnvNTj/99ND/c+fOtSpVqvhsdinNpFIWlbKd2rVr538nTJhgn3/+ud8OAAAAAACAbJgBpZpNKjyubKctW7ZYqVKlbOHChbZ27Vq75pprrFChQnbppZemKpMKAAAAAAAA0SfFaUaPPfaYB59uvPFGy58/vy/r1KmT3XbbbT6E7oknnkjy/pMnT/Zi5fXr17eOHTv68DsAAAAAAABErxQHoJYtW2ZFixa1p556yrOdRFlQjz76qBUrVsyWLFmS6H1V70nBqilTpvh6CkadffbZ9sMPP6StFQAAAAAAAIieAFTevHlt165dtnnz5jjLNQRvx44dSdZueuihh7xguQJW3bp1s5IlS9rBgwdt1KhRqdt7AAAAAAAARF8NqFatWnkmU+vWre2qq66y0qVL25o1a+yll17yIXi6PansKQ3fmzZtmg/Bmz17tjVv3txmzZqV1nYAAAAAAAAgWgJQI0aMsKlTp9qiRYu87lNAwScNzRs5cmSi9929e7cVL17cg0/SrFkzH7b333//pXb/AQAAAAAAEG1D8OrVq2dz5syxyy67zMqWLWu5cuWyMmXK+P/KaDr++OMTve+hQ4esYMGCcZapjtSBAwdSt/cAAAAAAACIvgwoqVWrlo0fPz5VD6hMqU2bNvnf4H8JXxZQgAsAAAAAAADZMAAVbsaMGbZhwwY76aSTrHbt2kddf926dVa+fPkjlsdfplpRKlAOAAAAAACAbDIEb+LEiXbCCSfYoEGDQhlLTZs29WLk3bt396F3N91001G3oyyn5F4AAAAAAACQTTKgvvvuO+vatatfP+ecc/yvCpDPmzcvtI4CRmPHjrXGjRtb3759E9yOipcDAAAAAAAge0lWAGr06NEeYNKsdZdccolt27bNJkyY4MPkevfubU8//bTPfqcZ8l599dVEA1CtWrVK7/0HAAAAAABANAzB06x3uXPntsmTJ1vz5s3tm2++Cc1cd/vtt/tMdkOHDrU8efLYwoULI73PAAAAAAAAiLYA1H///WclSpSwkiVL+v/ffvut/y1XrpzVr1/fryv4VKRIEdu5c2ck9xcAAAAAAADRGIBS4EnD7mJiYuzQoUP22Wef+fC7tm3bhtb5888/bevWrVaqVKlI7i8AAAAAAACiMQDVokULDzyp/pNmvFu3bp0v1/+qDTVlyhTr0qWLB6VOP/30SO8zAAAAAAAAoq0I+V133eX1nxRoCpx66qnWqVMn27Rpk8+Mp+CT6kTdcccdSW5r3759ftG6BQsWjHPbnj17vLZUvnz5/AIAAAAAAIBskgGlYNPXX3/tQ+7q1q1r1113nQekpHTp0pYzZ06rVauWfffddz5TXlI++OADrydVtWpV279/f2i5glJVqlTx27QOAAAAAAAAslEASlq1auUZUIsWLbJnn33Wihcv7suV+fTXX3/Z0qVLkzX8TkP4VLxcNaU+/PDD0HIFnVRDqkKFCnbppZemtj0AUmnixInWsGFDzz487rjjbOTIkT7ENq33WbBggZ199tme8agacf369fOJDcL9/PPP1r59e5/IQEFtZVXOmzcvIu0EAAAAAGTiAFRSdOCZXBp6d8MNN/hB6ksvvRRa/vzzz3swS7flypUrPXYLQDIpe7Fr1672xx9/WIECBeyff/6xu+++2wNKabnP5s2brU2bNj5zpjIlNZHBK6+8Yt26dQuts3jxYl9HAW71C7t27fKMy9NOO80WLlwY8bYDAAAge8vIE7H6vavf1ErEKFasmP8GDkYbAdEmXQJQKdW/f3/Lnz+/TZ8+3VasWOFZVT/88IMfxOo2AMfWsGHD7PDhw9a7d2/PThwzZowvHzVqVJyhsim9j7IlFYQ6/vjjbePGjTZ37lwPRCnYNGvWrFD248GDBz0DasuWLbZhwwarVq2a7d27195+++1j9hwAAAAg+8nIE7GaSb558+Y+MkijgTTx18yZM+3888+38ePHR7ztQLYIQCn627NnT48Qv/zyy/biiy/68iuuuMJrQAE4dhTomTFjhl/v1auXZyL27dvX/+oMzezZs1N9H2Uyib6gdebnpJNOsqZNm/qyYFKDe++91ycg+Oijj/wMkiY2UBaUVK5c+Rg9CwAAAMiOMvJE7FNPPWU7d+60E0880X8DKwil4JMkFQADsqoMCUDJwIEDPQD1+uuv2xtvvOEfxltuuSWjdgfItlTDTWdbRJMDSKFChTxQLMuWLUv1fVQbLnwdUXZT/O1qaK7uf9lll1nt2rX9y1opyldffXWEWg0AAIDsLqNPxFaqVMnOPfdc/81btGhRy5s3r5133nl+2+rVq4/RswBk4gDUqlWrbO3atUcsVzDp999/t99++y1Z26lfv76Ph123bp1t377diw5rhj0Ax5bSgQP6cgwonVjij1NPyX2C9ZK7XY2TV1+igLTOAIU/DgAAABBNJ2IHDx5sX3zxhd18882hdb7//nv/q1nmAcvuASgVWGvRosURyxXxPfPMM61Lly7J3pYynnSwqQvZT0DG0+c4EvdJ7nZ1NkgB7lNOOcULOyqtGQAAAIj2E7Gi0UETJkzw64wEQDTKfbQVFBzScLnwD5oyE5RmGE5jV3fs2OHFhJNL6YUrV66MEw0GcGwp3TegWkyB3bt3+1/NxpHa+2g9FRZP7nbLly/vfwcNGuTpypoBZN++fV4bCgAAAIjWE7EqUB4EnVS8/Jprrknx/gBZPgClD4wKAd9xxx1+XRcdQCo6Gy6YcrJJkyYp2gECT0DGql69un+u9RnWENsaNWr4Z1yBZqlTp06q71OzZk0PQGmdwL///htnnXHjxtmPP/5oF1xwgV1yySVxHkfFHQ8cOEAACgAAAFF7Inbs2LF24403+m9rHU9rVjyVpACiTbLe1QMGDPBsBE2Trg+FDgZ1PbioflPHjh197KoKigPIOjRm/dRTTw2dedGY9tdee80/68WLF7dmzZql+j5t27b1v0olVoakpqrVDCCifkMWLVpkb731lj3wwAP+Ja0v5meeecZvO/nkk61w4cLH6JkAAABAdhKcVJXghGlyT8Qe7T46ERu+TkInYkWJHUHwSaVuvv32W2aGR/YOQOXJk8fee+89L5DWsmVLDzbpenDRMJlPP/3UnnzySf9AAsha7rvvPv8iffPNNz2AdMMNN/jyu+66y2fjePzxxz0TsnXr1sm+jygoXbJkSVuyZImVLVvWz+goWKXZPlTnSe68804rU6aMB6IqVqzoBRynTZvm23jiiScy5PkAAABA9MvoE7FapqF2um/jxo195jxtA4hWKc7r04GhglEAokeHDh3so48+soYNG9r+/ft9to4RI0b40FtRDbg1a9bY+vXrk30fqVChgk2fPt3atWvnwaoiRYp4/biguGIw/ezMmTM9y1Jne7SevrA1A8gZZ5xxjJ8JAAAAZCcZeSL2wQcf9N/RotrI9erV88fShVnwEI1yxAbFm5JJY1iHDx/uWU+K5KpGS5wN5shhy5cvt+xMB+sa16vZDcLHCAMAkFz6wXr+xZ2sXq82VrTi/6Z2BoD0FLN2iy15Y6pN+vBTP/AFsquPP/7Yhg4d6t+9mhSnf//+HkzSse3999/vpSLq1q3rtyfnPoEFCxbYrbfeaj/88IPlz5/fLrroIs/w1zGiglE6Zty1a1eC+6SyN3v37j0m7QeOVfwjxQGofv36hVIME9xgjhz+YUrKnDlzfLr1ZcuWhaag1A5rLKzSEZs2bWrp5csvv7QhQ4b48B4N80moY4hP9WhGjhxpK1as8KwOzch11VVXJfsxCUABANKKABSASCMABQBIq5TEP446C158qsgvChJpCE7BggWTPbWkdkyzXCn4FAgCWcE27r33Xt/uu+++m+biwz/99JN16tTJunfv7llbijzfc889nrWlvwl5//33rVevXl54XemRimxrOswCBQpYz54907Q/AAAAAAAA2VGKM6A0jlXjVDdv3uxphCmhLCIVatOUkgpgVatWzQM7wcwBmhVAhdkUIFKm1QsvvGBpoWyqbdu22ezZs0PLVPBY01xu3Lgx9NjhlFp50kknxalzpQDWL7/8Yn/99VeyHpcMKABAWpEBBSDSyIACAGTqDCgVCn7jjTd8rGpKA1DKJtKMehqCp8LFCdFMAApOqbhxWgJQ+/bt84LpGq8bf/9HjRplM2bMsPbt28e57e+///ZhgQndRwEp3ZbQVJyJUaAuKCoHAEBK6PtD54gOHzxkhw4mPbQdAFJD/Yv6GX6zAgBSKyXfHykOQPXp08e+/fZbnwVAs1mVK1fOcueOuxkNs0usgLmKqSnLKDHVq1f37aW14JrqN+mJiB8wCmYTUDApfgBq8eLF/jep+yQUgFKwS5fwCKCMHj3a2xvQMMOEEs4iuTyt21Bmmi4Zse+RalNmXJ6Z9oU20aZjvVxDuXWJpjalx3JlGm/fvM0WT/rZ8hT833dJDos75D3WYlOwPFia8PL/LcmRwDYSW56WfUmv5bSJNtGmtCw/sHufxWzeZs8//7yVLl06w/u9jH7M+MuD38HR1Kb0Xp6Z9oU20aasurzg//8tnBn2JTXLw2Mh6R6ACp8W/fbbb09wZxILQLVo0cKmTp3qGU7KKlKB7+CJVnBq1apVPj27GqBp29Ni+/bt/jd+CpimgQ8PEqX1PqKC5fGzpkRZYgcPHvTrCqopY0xtC5aJMsKCGQ7Ci7drmW7T8xI+06C2oW3Fny1Bwwk1tDH+8kKFCvn9tZ1wqq+lxwsP9On+ej20f8GbSMMOFZjTjId6jPBhi1pHj6fHCA+06bF00fOmNoQ/H7qP0vNy5coVWq5tHzhwwEqUKOHvhYBS+LSPGvYZbuvWrX5/bSegD4CGW+rxgtdLdH9tR/un/Qzo8WgTbaJNmaNNCvLrkln6PdFzou3rRIbaFzjWfXnFcuVt/7YDtvmv9ZYrZ05/XcNfp81btljevHmsWNH/e/30+Fu3bfPHKBJWS1Ht2P7ff1YoLOAn2ucdO3f6uuGZzXt277Zdu3db8WLF4rz3du7c6fcpWaJEnPdeTMx/tn//AStdqlSc9972bdvs0OHDvjzcli1baBNtok2ZoE3qZ4J9zwz9Xmbqy/U7WJdo+s4N0CbaRJsyT5tq/f/fwpmh30tNXx7RAFRCEbDk3v7UU0955pRmpBs2bFii99cL8fjjj1taBE9S+AsdTi9Ycu8TtCmh+8jgwYN9es2AAlVVqlTxgupBMEvbDCKF4c/R0ZaHv9ipWa59jr/tlCzXDxqdhc8s0dWMekzaRJsy275EU5t01r3U/z/Iygz9Xvi202t5atu0YcMGPzmSGV6n9F6emfaFNtGmrL48LdsoXry4j2jILP1eZurLg9/BaX2OU7NuVlmemfaFNtGmrLq89P//LZwZ+r3U9NmKfzzzzDMWkQBU/B1IiRNOOMGLqmrnNBPe0qVLQ1lFCtTUrFnT2rZtazfffLOVL/+/szGppS/ThLKWFIWU8Ejp0e6js1OJ3SeIDoZHRsOjgymtk5XZVKpUyS8AgGNPk3XoAgA49vgdDAAZXAMq3Nq1a33mumbNmnm0TJGwo1Fkb+jQoX6JJAWzlH4Wf+a64P/69esfcZ+gNpXWady4cbLuAwAAAAAAgKQlPKbsKD7//HPPZtIws9NOO82XtWrVysaOHWuZhTKPWrZs6bPphaeSffDBB57ppKBZfBp3WaNGDV8nnP5X8XHOQgMAAAAAAKRcijOgvvrqK7vwwgvjDMVTEauZM2fajz/+6EPpLr/8cssMhgwZYmeffbYXRdeMfT/99JM9+uij9sgjj3hhLg21Uz0qZUuVKVPG76O6TZrpT5lanTp1sk8//dTee+89L44OAAAAAACAY5AB9eCDD3pG0YsvvhinTtMtt9ziy9NaPDw9qZ7Uhx9+6LWmFDQbP368B6AGDRrkt//6668+M9+kSZNC97nyyitt3LhxXqNK95k2bZq98cYbic7sBwAAAAAAgKTliE2oJHsSNDWgsoc0I4SG4KkOVDBdn2avC6YszM6UWaWC5ZpeMZgFDwAAAAAAILvGP1KcAaXZ3hRg2r17d5zly5cv9wdUgAoAAAAAAABIdQDq3HPP9Wn2zj//fI90yZ133ukFv0U1l5Ly7rvvWpMmTXw78XXo0MFvo94SAAAAAABANh6Ct2bNGjv99NNt1apV/9tAjhz+V5tRTSgV+j7uuOMSvf+2bdt86N6ePXvsm2++sTZt2vjyqVOn2llnneUZVKtXr/aZ6rIqhuABAAAAAIBoFxPJIXiVKlWyefPm2T333OMFvGvVqmVnnnmmzx63YMGCJINPUqJECbviiis8YPXyyy+Hlj///PMezOrVq1eWDj4BAAAAAAAgjRlQ6UGz0tWvX9/rSa1bt84LlysrSsXMFy9ebLVr17asjAwoAAAAAAAQ7WJSEP/InZoHUAbU999/bzt27LDDhw8fcft9992X5P3r1q1r7du3t6+//trefPNNL2p+4MABryuV1YNPAAAAAAAASGMG1KuvvmpXX321D6FLjDKZjkbBJxUiP+mkk2znzp22YsUKX6Y6UFkdGVAAAAAAACDaxUQyA2r48OGe9ZQ7d26vB5U/f/5U7aQyoDQM7/fff/f/TzzxxKgIPgEAAAAAACCNASjVbMqbN68tW7bMqlatamlx8803W//+/b34+C233JKmbQEAAAAAACBKAlCa+W727NlWtmzZND+4ZrxTIXK57LLL0rw9AAAAAAAAREENqCVLlljLli19yNw111xj5cqVs1y5csVZR7dnZ9SAAgAAAAAA0S4mkjWgtm7d6n+nT5/ul/g0nO7gwYMp3SwAAAAAAACiVIoDUNddd51t2bIl0VnwUphQBQAAAAAAgCiX4gDUn3/+6X/feecdO+2007wgOQAAAAAAAJBuAahGjRrZ4sWLrXv37im9KwAAAAAAALKhnCm9w9NPP22HDx+2AQMG2MKFC23dunW2cePGOJeUmDNnjo0YMcJuuOEG///HH39kGB8AAAAAAEB2ngWvYsWKXuV8z549CW8wmUXIDxw4YD179rQPP/wwtOzQoUNWqVIlq1atmn3xxRdeST0rYhY8AAAAAAAQ7WJSEP9IcQbU+vXrbffu3Z6llNglOYYNG2YffPCBlStXLlRHSttVBtWsWbPs3nvvTemuAQAAAAAAIBoyoKZPn37UdVq1anXUdWrUqGH//vuvrVixwlq0aGFr1671DKjffvvNTjnlFCtfvrytXr3asiIyoAAAAAAAQLSLSUH8I8VFyJMTXEqONWvWWPHixa1y5cpHFDkvXLiwbd68OV0eBwAAAAAAABkrWQGo66+/3kqWLGnDhw/360lRDahnn332qNusUqWKrVy50ubNmxdn+dixYz1yVqdOneTsGgAAAAAAAKJhCF7OnDk9YPTPP//4dQWZEqJN6TYNpTuaRx991O68806v/6RZ9XSfMmXK2KZNm/x2BbsGDx5sWRFD8AAAAAAAQLSLSe8heC1btvRi4cH1xAJQKTFo0CCv8aRsqSAGpgLkCnD17dvX7rjjjjQ/BgAAAAAAALJgEfL0tnz5cvvuu++85lOFChXsjDPOsFq1allWRgYUAAAAAACIdjGRLEKu7KRSpUr5ELpwGkJ32WWXWYkSJez5558/6nauuuoq39Zpp51mNWvWTOluAAAAAAAAIFoyoHSzhsoFjjvuOM9U+vnnn0ND52Tnzp126qmn+vA8RcCOJqglVbt2bQ9E9erVy8qXL2/RgAwoAAAAAAAQ7WJSEP9I1hC8s846y6ZNmxan0HhiypYta+vWrTvqTp533nn2zTff2MGDB317uXLlsnPOOceDURdccIHlzp3i5KxMgwAUAAAAAACIdjEpiH/kTM4Gn3zySQ8ShQefdD3+RbfdcsstydrJyZMn29q1a+2pp56yU045xQNRkyZNsq5du1rFihXttttuS9Z2AAAAAAAAECVFyBcsWODD7FSzSVlOn3zyyf9tJEcOz1iqVq2a14dKjRUrVtiIESPs1VdfDQWzVFcqKyIDCgAAAAAARLuYSBQhP/HEE/3v1KlTLV++fF7vKT38888/9vbbb/tl0aJFobpSNWrUSJftAwAAAAAAIGOluNBSq1atbNu2bZ4BtWPHDjt8+PAR66ig+NFoprzx48fbTz/9FBrCV6BAAevSpYv169fPWrdundJdAwAAAAAAQFYeghf48ssv7ZJLLrFdu3YlvMEcObyeU3JmwQs0adLEg049evTw1K2sjiF4AAAAAAAg2sVEYghe4I477vBaUIG8efMmOSteYooXL26XX365B55OOumkFN8fAAAAAAAAWUOKA1B//vmnB5zeffddHy6XK1euVD3wunXrvJYUAAAAAAAAoluKA1B169b1wuHdunVL8YNdf/31VrJkSRs+fLgNHDgwyXUV5Hr22WdT/BgAAAAAAADI4jWgJk+ebJ07d7ann37a+vfvn6IHU92nKlWqeABL1xMbuqdd0m2HDh2yrIgaUAAAAAAAINrFRLIG1GuvvWaVKlWyG264wQYPHmxlypSx3Ln/bzMKHC1cuDDB+7Zs2dLKlSsXup6a2lEAAAAAAACI8gyo8NnrEtxgFs5cSi9kQAEAAAAAgGgXE8kMqKFDh1p66Nu3r5UqVcoeffTROMsVvLrsssusRIkS9vzzz6fLYwEAAAAAACALZUCllh5m9erVof+PO+44q1Chgv38889+W2Dnzp126qmneiaVImlZERlQAAAAAAAg2sVEMgNKDh8+bB9++KFNmTLF1qxZY5MmTbL33nvPOnToYEWKFEnwPgoo9enTx6ZNmxZatn79eg9EJaRs2bKp2TUAAAAAAABkMikOQO3YscPOPffcUOZSUEj8xhtvtOLFi3uAqWLFigne98knn7TGjRt7ACu4X0IJWKozdcstt6S8NQAAAAAAAMj6Aah77rnHZs6caQ0aNLDly5fbnj17bPfu3Xbw4EH/XzPjvf766wneV/f57bfffJjdaaed5llOn3zySeh2BaU0o161atW8PhQAAAAAAACyYQ2oypUr2+bNm23dunXWsGFDW7t2rRcOV32nWrVqWcmSJf22o5k+fbrly5fPmjdvbtGGGlAAAAAAACDaxUSyBpSCT6rzpFnq4gemFFDSgyZHq1atvH6UMqA0rE/D8kR/t23bZt98843XlgIAAAAAAEDWluIAVM2aNW3JkiX2+eefh5ZpCN6DDz7oQ+uUFZUckydPti5dutiBAwdSugsAAAAAAACI5gDUoEGDrG/fvta5c+fQsmDmO9Vwuu6665K1neHDh9v+/futYMGCPoQvT548VrhwYZ8ZT9u59tprU7prAAAAAAAAyIRypvQOV155pY0ePdoDRyofFVwUPBo2bFiyA0cLFy60vHnz2sqVK61Pnz528sknez2pCRMmJDgzHgAAAAAAALJJEfKA6jb9/PPPXhOqQoUK1qRJkxQV3C5QoIAXqlLG09tvv+2BK9WPypkzpxcyV1bVP//8Y1kRRcgBAAAAAEC0i4lkEfKAAkTt2rXz2e82bNjgw+ZSokqVKrZ8+XL7+OOPrUWLFrZr1y57+eWXrWLFir7jGp4HAAAAAACAbDQEb/bs2Xb++efbiBEj/H8FjC688EI77rjj7NRTT/UsqMceeyzZD9yjRw8fanffffdZ9erVrW7duta/f3/r1KmT337iiSempj0AAAAAAADIZJKVATVv3jxr1aqVZyUpUCR33323ffrpp3FmwrvjjjusRo0adtFFFx11mwo8aQY8pWvJmDFjPKC1Z88eH4L3xBNPpL5VAAAAAAAAyFoZUMp62rdvn2c7nX322aHhchp217FjR1uwYIEXJ1dG09ixY5P3wDlz2kMPPWRPP/20/6/hfGvWrPFMK9V+0rA8AAAAAAAAZJMi5Bpet2nTJp+xTrWbPv/8cx8qpwDUrFmzfAY7BaWKFy/uF62bnVGEHAAAAAAARLuYFMQ/kpUBtWXLFitVqpQHn+Sbb77xvwo2KfgkhQoV8v/1oMmRK1euJC8qct6gQQN7/PHHk7U9AAAAAAAAZE7JCkApiqXAkobhiTKglP3UunXr0Drr1q2zrVu3euQrOZR4ldRFGVULFy60QYMG2VNPPZXa9gEAAAAAACArBKCaNm3qBcM1S93AgQNtxYoVvrxLly7+d9myZdazZ0+/fsoppyTrgefPn2+VK1f2bX/44Yc2Z84c/9u4cWMrW7asFzhX7SkFo1RvCgAAAAAAAFFcA+rLL7+08847z7OeRHepU6eOFx/X8LyKFSuG1p08ebKdc845R31gFS//+uuvbcOGDVaiRInQ8o0bN1qlSpWsa9eu9s477/iwPgW/lBGVVVADCgAAAAAARLuY9K4Bde6559pbb71lNWrUsHz58lmHDh08eJQ7d24rU6aMr1O4cGF7++23kxV8kqlTp/q24g/ZK126tC+fNGmS/58nTx47ePBgsrYJAAAAAACAzCdZASjp0aOH/fnnn7Z7924PDlWtWvV/G8iZ04NJmvmue/fuyX5gFTXXtq677jrPggrqSGmYn5arCPnEiRM9w0pD8gAAAAAAABDlAaiktGzZ0vLmzZui+1x99dU+lO+ll17yIXzKdFJNKNV70lC/q666yn799Vdft1WrVumxmwAAAAAAAMiqAajUGDJkiN16660+jE+BqEOHDvnfAgUK2F133WVDhw717KcmTZrYqFGjMmo3AQAAAAAAcCyKkEeSgkyzZ8+27du3W/ny5T3gFNSF2rdvn9eDymooQg4AAAAAAKJdTHoXIY8k1XrSzubKlcvatGnjNaUCWTH4BAAAAAAAgEwUgBo7dqxVqFDBzjzzTC9yLs2bN7c777wzI3cLAAAAAAAA6Si3ZZDx48fbjTfeGGfZ3r17bcmSJX7RcLyBAwdm1O4BAAAAAAAgq2dAjR492me7mzJlilWqVCk05G7MmDFejPyFF17IqF0DAAAAAABANBQh12x3qv+0ceNGq1Kliq1du9ZnwpNSpUrZ7t27bc+ePZYVUYQcAAAAAABEu5isUIRcO6Yd1Cx44ebOnWvbtm2zkiVLZtSuAQAAAAAAIB1lWACqS5cuduDAAS9AroCTdO/e3Vq3bu1D8y644IKM2jUAAAAAAABEwxA8ZT+dddZZ9uuvvx5xW/369W3atGlWunRpy4oYggcAAAAAAKJdlhiCpx2cOXOmvfjii3bZZZdZu3btrFevXvbyyy/bvHnz0iX4tGPHDuvfv7/PqFeoUCF/jEWLFh31fpdeeqlnYcW/vPvuu2neJwAAAAAAgOwmwzKgjoWOHTva7NmzbdSoUR6Je+CBB2zDhg0ehEqqxlS9evXslFNOsRtuuCHO8tq1a3uB9KMhAwoAAAAAAES7mBTEP3Ifs70yszfeeCNF6ysjKrWUXTVp0iS/nHfeeb5M9aaqV69uY8eOtSFDhiR4P82+9+eff9rgwYOtefPmqX58AAAAAAAAZEAA6sorr/ShbMmh9dISgPrqq6982F379u1Dy8qUKWOtWrWyyZMnJxqA+v333+3w4cPWqFGjVD82AAAAAAAAMigAVbVq1UQDUAr6rF692m9Pj1GBixcvtho1alju3HGbWKtWLRs/fnyi9/vtt9/877hx42zixIm2detWO/XUU2306NH+NyH79u3zS3gKWtAmXSSoI6W2hbfvaMuD+6d2ec6cOY/YdkqXp3bfaRNtok20iTbRJtpEm2gTbaJNtIk20SbaFL1tir9OpglA/f333wkunzt3rl133XX277//eqPLlStnjz32WJoea/v27QmOPyxSpEgoQJRUAGrPnj1edHzLli328MMPW5s2beznn3+2hg0bHnGfkSNHen2p+DZt2mR79+716wUKFPBxkXpsbTugLC3t07Zt22z//v2h5dr3ggULegDs4MGDoeUlSpSwfPny+bbD3yCqTZUrVy7buHFjnH0oW7asHTp0yNsR0BtGz7EeT48bULBOxd+1f+HPUd68eb1m1s6dO23Xrl2h5bSJNtEm2kSbaBNtok20iTbRJtpEm2gTbcq+bdqxY4dliSLkavzdd99tzz//vEfN9ORec801HtDRk5Nc4ZlGgQ4dOnjwZ8aMGXGW33PPPR7cCgJD8S1dutQDYWeddVacYJYKkLdt29YmTJiQrAyoKlWq+IsYBMGIotIm2kSbaBNtok20iTbRJtpEm2gTbaJNtCma2qT4hwJTySlCnmEBqLfeessGDRrkUT/tgmouadhbs2bNUryt+++//4gMpK5du9qyZcts/vz5cZYPGDDAM5s0G15KXHjhhbZkyRK/HA2z4AEAAAAAgGgXk1lnwQtqM11//fX2/fffe+CpcOHC9uCDD9rNN9/sEbzUUNZUx44d4yz7+OOPvRC5InPh2/3rr7+sfv36iW5LwSmlxLVr1y7OcqWpKcUNAAAAAAAAKXNMA1CDBw+2J554wg4cOODBJ2U76f9KlSr5sLeEipYnR8WKFf0Sbvfu3fbQQw95EErD8UTjFKdPn+7D8BIzduxYW7t2rS1atMjHT8qaNWvsxx9/tFtuuSWFLQYAAAAAAMAxHYKnTCSNF0wOrRde7Co1VDhcQ/BGjRrlWU0aqqeCXn/88YePURQFmlS/qXHjxv7/N998Y+ecc44HrW688UYvuqX7KQNK9ytevPhRH5cheAAAAAAAINrFpCD+ccwDUCmRkun8EqIi4LfeeqsPx9O2Tj/9dM+4qlu3bmid1q1b++x84TP0TZkyxYcF/v77777PCkgpiJXcjCwCUAAAAAAAINrFZNYA1D///JOi9atVq2ZZEQEoAAAAAAAQ7WIyaxHyrBpQAgAAAAAAQOqlbtq5VNLQt2eeecYjZMmh2kxvvPGGNWnSJOL7BgAAAAAAgMg4phlQSse6+eab7fbbb7ezzjrL2rZtayeddJKVK1fOChUq5AXC169fb0uXLvVi4D/88IMX/27UqNGx3E0AAAAAAACko2NaA0qFwMeMGWMjR460TZs2JTkjnnarfPnyNnjwYLvhhhtSXMA8I1EDCgAAAAAARLuYzFqEPLB3715799137bPPPrNp06b5bHWBsmXLWqtWraxz587WrVs3y5Mnj2U1BKAAAAAAAEC0i8nsAaiEaj1t3brVSpQoYfnz57esjgAUAAAAAACIdjGZdRa8xOTLl88qVKiQ0bsBAAAAAACACMg6hZUAAAAAAACQJRGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEEYACAAAAAABARBGAAgAAAAAAQEQRgAIAAAAAAEBEZZsA1K233mqtW7dO1rrr1q2zyy67zEqXLm1Fixa1rl272po1ayK+jwAAAAAAANEoWwSgHnnkEXviiSeSte7BgwetQ4cONmfOHHvuueds3LhxNnv2bGvfvr0dOHAg4vsKAAAAAAAQbXJbFFu5cqVnPn322WdWrFixZN3n/ffft/nz59uCBQvshBNO8GWNGjWyE0880SZMmGCXX355hPcaAAAAAAAgukR1BtTAgQPtr7/+su+++86DSMnx1VdfWd26dUPBJ6lfv74df/zxNnny5AjuLQAAAAAAQHSK6gyo4cOHeyApR44cyb7P4sWLrU6dOkcsr1Wrli1btizB++zbt88vgZiYGP97+PBhv4j2QZfY2Fi/BI62PLh/apfnzJnziG2ndHlq95020SbaRJtoE22iTbSJNtEm2kSbaBNtok3R26b462TbAJSGzaXU9u3brXbt2kcsL1KkSCiwFN/IkSPtgQceOGL5pk2bbO/evX69QIECPgxQ29izZ09onUKFCvm2t23bZvv37w8tV/HzggUL2tatW70uVaBEiRKWL18+33b4G6RUqVKWK1cu27hxY5x9KFu2rB06dMi2bNkSWqY3TLly5fzx9LiB3Llze+F17V94W/PmzWslS5a0nTt32q5du0LLaRNtok20iTbRJtpEm2gTbaJNtIk20SbalH3btGPHDkuuHLHxw2hZUHimUfgLEi6YAW/atGlJbkvBp+bNm9ubb74ZZ3nPnj3tl19+sSVLliQrA6pKlSr+IuqFE6KotIk20SbaRJtoE22iTbSJNtEm2kSbaBNtiqY2Kf6hwNR///0Xin9EdQbUgw8+eEQGUvwXJ7mKFy+eYKaTIoiJFTJXBFCX+PSG0CWhFyy+xJbHv39qlqf0MSO9nDbRJtpEm5JaTptoE22iTUktp020iTbRpqSW0ybaRJvsmLYpsXWiNgB1zTXXWMeOHdNlWypAPm/evCOWq5h5s2bN0uUxAAAAAAAAspOoCEBVrFjRL+mhffv29vbbb9uiRYt89jvRdRUnHzJkSLo8BgAAAAAAQHaS/FypKKXgUnjGU/fu3X0WvA4dOtg777zjF11v0KCBdevWLUP3FQAAAAAAICuKigyotLj++uvt77//9ouoltOUKVNswIABPrQvT548nhX1xBNPHFHYHAAAAAAAANlkFrzMRkXMVbA8OVXgAQAAAAAAoj3+ke2H4AEAAAAAACCyCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCiCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCiCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCiCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCiCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCiCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCiCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCiCEABAAAAAAAgoghAAQAAAAAAIKIIQAEAAAAAACCickd289lTbGys/42JicnoXQEAAAAAAIiIIO4RxEGSQgAqAnbs2OF/q1SpktG7AgAAAAAAEPE4SLFixZJcJ0dscsJUSJHDhw/b2rVrrUiRIpYjR46M3h3gmEa/FXhdvXq1FS1aNKN3BwCyFfpgAMg49MHIrmJjYz34VLFiRcuZM+kqT2RARYCe9MqVK2f0bgAZRl+6fPECQMagDwaAjEMfjOyo2FEynwIUIQcAAAAAAEBEEYACAAAAAABARBGAApBu8uXLZ0OHDvW/AIBjiz4YADIOfTBwdBQhBwAAAAAAQESRAQUAAAAAAICIIgAFAAAAAACAiCIABQAAAAAAgIgiAAUAAAAAAICIIgAFAAAAAACAiCIABQAAAAAAgIgiAAUgUYcPH07WMgBAZMTGxmb0LgBAtkUfDKSv3Om8PQBR4uDBg5Y7d27bt2+f/fLLL/bff/9Z06ZNrWzZshm9awCQrfrhQ4cO2ZYtWyxXrlxWsmRJy5EjR0bvGgBkqz5Yv4Nz5sxpxYsXz+jdArI0MqAAHEFftPrC3bFjh5199tl2ySWXWK9evWz8+PF24MCBjN49AMhW/fCll15qrVu3tnPPPddPCHBGHgCOXR/cvXt3O/PMM/0yf/58RgMAaUAGFIA4dGCjs+x79uyx008/3cqUKWOvvfaaZ0LpAChPnjy2d+9ey58/f0bvKgBEdT+8a9cua9asmZ9x79mzpx8QlS9fngwoAIhAv6u+Vf2s+t/wPliZp127drWCBQv6dWVCAUgdAlAA7Ndff7XNmzdb+/btQwc2H330keXLl8/efPNNq1ixoi/77LPP7IUXXrBNmzbZwIED/YxQ8IUNAEi9VatWWdWqVf26+lT1rUOGDPGDnc8//9xKlCjht82ZM8deffVVPwPfu3dvO+644zJ4zwEg6/vnn3+8P1XgKaA+uFixYkf0wa+88ooHozp06GAnnnhiBu41kPUQvgWyMR3g7N6924fZaVhHOI11/+uvvzzjafLkydatWzfr3LmzrVy50u/Tp08fT0Mm+AQAaaN+tWbNmt7XBtS3auiHglI6APr555/txhtvtObNm9tjjz1mjz76qPXr18+2bduWofsOAFndihUrrEaNGvbJJ5/EWa7+tX79+t4Hz5o1y2666SY79dRT7YknnrChQ4favffe6/X5ACQfGVBANqYDHJ3BWbhwoVWoUMGH1ikQpaF3jRs39oLj1atX9/UKFy5sb7zxhp/t2bp1q38BK3PqpJNOyuhmAECWpiHNH3zwgZ133nleZ0+Bf1GW07Rp0+ycc86xpUuX+sHQyJEjrUuXLr6+AlHKXg3OzAMAUk61nt577z0/0RreB+tErbKfOnXq5Cdd1Qc/9NBDPiRav4l1IkAnbEuVKpXRTQCyDAJQQDanL1cFn/RXQ/D+/PNPe/vtt61Nmzb25JNP2k8//WTVqlWzli1bWp06dUJnilSTRPcDAKSN+tLgwEfBffW36n9ffPFFnwRCw54vuOACz3hq0qSJ36d27dpWunRpslABII2UaaqL+uCTTz7ZM02ff/55e/nll30SCA3PUxCqb9++PiO01KtXLzRsGkDy5YhlKhUg24pfv0kZTe3atfOaT2PGjPEgVGDBggVWoEABW79+vd11111+3+nTp8cZKw8ASL2NGzfafffd5wc9d9xxh59pj99Px8TEeD+smUmVpaohIwShACDt1q1bZ4MHD/bspttvv91GjRrly/fv32958+b16ypDsXbtWs+CKleuHH0wkEIEoIBsKpjlIzi4Ua2RIkWK2B9//OHTzCrr6eGHH/Yhd3PnzvWz8CqSqzolmhlv6tSpnqIcbAcAkDIJ9Z/Kdho9erQP7bjnnnts2LBhvlzDQxSc0nA7zVKqISMzZ870flhD9ZiVCQDS3gfrt+7jjz/uJ2Jvu+0274vl3Xff9eCUsp40FE8BKfpgIOUYggdkQwcPHvSDl507d9rdd99ty5Yts8qVK9uDDz5oDRo0sB9++MHrQOmLVjPhtW3b1p577jn7+++/PfikoSL6wg62AwBImaD/VDBpwoQJPvy5Y8eO1qJFC7v11lt9HWVAqa+9//777ZRTTvHafKoXpZmaFJzS/emHASBtffCHH35oixYt8pOtjRo18uwnUZ09BZYeeeQR74M1NE+BJ40Q0Ax59MFAyvFpAbIZZTzpi1IZT82aNfNaTkWLFvXAks7g6HZNKTtjxgyvQ3LzzTfbM88840Vw45814gsXAFJOfW3QD5922mk+AYSKiauYreqKaFjHLbfc4uvpxID65QceeMDeeeedONuhHwaAtPXBOuGqE7LKatIkOyNGjPCTsgMHDvS+VxlQCkJpAgj6YCDtGIIHZEP6wtTYddUReeutt/yLVpThNG/ePC+Iq7M8mh1Pw/GUBTVx4kRfBgBIu3379nlgXxlNOsDRJA8ayqEDItUYKVSokM9Sqpp7Kkh+4403+l8AQPr0wap7qn5WfbBmfdaMz9u3b/fbtFyBJ2WbajjeVVddZS+88EJG7zaQ5RGyBbLhOHcd3KxcudJnVFLwacmSJV5fRDWf9KWruLS+ZPVl+8033/hQPKUeAwBSL7xOiAL8GzZssHHjxvnQZx30vPbaaz7UQ0NCVGfk/fff9+F3OjuvWnzxC5IDAFLXBy9dutQznpRdqj54y5YtnuGk38Lqg+vXr+8z4WmonbJTf//9d/pgIB2QAQVkI7t27fLZ7DTFbOvWrT0opSm/v/zyS68DpSEfynhSMEqFFTUrngreBig4DgCpExy4aLidsp7UF6sf1pl3Hex88MEHNm3aNOvSpYuVLl3a++XLL7/chg8f7rPjaZi07s8BEACkPvikPvjAgQO2YsUKz+zXjHea/EF1oDTBzkUXXWSlSpWyTz/91Ifh3XnnnT47noZG6/70wUDakAEFZCM33XSTZzRpho9rr73Wnn32WZ/VQ1/AOgvfqlUrX0/1n1TkVvWhwhF8AoDU0QGLitVecMEFfrZdsyydcMIJ3herb61du7Z9/PHH1qlTJ1+/adOmXpNEypYt63858AGA1FHwSIGniy++2Jo0aeIzjKrWaffu3f2kQI0aNeyTTz7xySDUzyrr9J9//vH7qjSFMNsdkHYEoIBsRGfWp0yZYpMnT/Yz65rdTl+k5cuXD62joXmaBU8HSBzoAED60Zl3BZuUXSrTp0/3jFMF/DXkLuiLlZGqAx3VJAlHnwwAqaffvCpD8e2333oA6quvvvITsTVr1rRKlSpZxYoVPdtfs5IqWKXafPHvDyBtGIIHRKmEztJofLuG2OnMj4qPBzT0Q7VHatWq5dlPuu+cOXN8Zg/OuANA6gV9aDCEefbs2daiRQuvs6c6fAENuVOdEQWpXnzxRT9I0rrMsAQAaacMVPWnOgGgzKfHHnvMM1ADkyZN8t/Nqs33yiuveO09+mAg/RHGBaJITExMnODT/v37fUalgMa033fffT7MQ5lQARVi1Pj3WbNmeX2oIPikL2uCTwCQfOo3RRM6SHCeLxjCrDPqF154odcX0RA79dcKPA0dOtR69OjhBXFLlizp/bH6YQWuAADJo9++ogwmUR8rQSBJmU46CaCae1pXt2/evNlGjBhh559/vvfBxYoVow8GIoQAFBAllC6s8es6qFHwSQc/qu3UoUMHrzUS0FkfHQAp7TigKWZVeFFBKc34EQSfOOsDAMmnWZKuuOIK+/fffy1fvnx+AuCcc87xPlgFb0W19dQvf/HFFz7kWf21DnZ+/PFHv2i5ThLkyZPH+2Fq7wFA8vz222924403+m9i9aHKYlL5CQWXFPBXMEnFxNVPa/izTriqD9bED+p/VYJCv49VC4o+GIgMAlBAlNCwjbp161rv3r19KIf079/fv2x1NqdevXr25JNPeqHF66+/3ouOK804ULBgQStcuHDojD3BJwBImY8++sg+++wzu/XWW23t2rX2119/+Qx3d999t2c96cBIB0F9+vTxQrdarqF2oj5XJw3Uj+uASGfl6YcBIGV98EsvvWSPPPKIB/0ViFq9erXdf//9XoJCs9rpt69mutPliSeesO3bt4eypE477TQ/SUsfDEQOASggSqho+HPPPecHMN26dfOMpmuuucY+//xzP5ujAJS+aOvXr+9fvgo26f+EUosZdgcAKaeDHAWfZs6caQMGDPAi4jqjrtp6jRs39mm+jz/+eF+nTJky3tcuX77c7xscAAUodgsAKfPggw96YF99ra5rgodffvnF5s2b579/9ZtYv5dV/0n9r4beKVM1CDiFow8GIoMi5EAUCC8UrvTju+66yw94lF6s8eyB77//3mfA09khnXXXmXalHCv7iWLjAJB6QZHxYFizJnbQCQEF+jXDnYaCKFNVB0ULFizw+iNyyy23xBkmDQBIWx88ePBgGzt2rGc5qT+uXbu297/KTH366aft559/9swo/X/DDTf4MgDHBgEoIEq+cMMDSPPnz7c77rjDg0vvv/++1xsJ98cff/hZ+SFDhtidd97p6wIAUieY+CF89lGdCBg/frydcsopNmbMGKtcuXJo/VWrVtlPP/0UGv6hfrphw4YZ2AIAiK4+WL9tNaNo586dQ0GowMKFC23ZsmVeokK/nZUxpTqqACKPABSQhQWFwlVwXBlP+r99+/b+5bto0SIf6x4ehAr/Yta6qgWl9ON3333Xiy2SAQUAqeuHNePSli1bvOaTht7FD0I988wzVrFixTj31cmCs846y+vzXX755RnUAgCIjj5Yv2k1s121atVCQShl/Xfq1Mnuvfdeq1mzZpz7qkaUhkcrC1VlKwBEHoNbgSyc+aQvXI1db9WqlV188cV23nnn+ZC7X3/91ce66wv19NNP95pQKkyu4FMQc9Z9CxUqZHPnzvXheASfACD1/bCGemhqb/XHTz31lN/+8MMPW8+ePX2mpZtuusmHe4RPD37SSSfZCSec4LOXMtU3AKS+D9bvYPXB+h2svldGjRplV111lfexw4YNC9XcC/pbZUVpqLRO4gI4NghAAVmUht3t2rXLvzg1s53Gur/zzjs2ffp0L4SrIJQObBSEOuOMM6x79+4+tXcQaNJ6+sLVlOAEnwAgZRTMVz+s2k4nn3yyrV+/3s+yayidsk8TCkKpMLnqjijjVL799lsvjlukSBH6YQBIZR/ctGlT+/fff3220fLly3uwafjw4XGCUJqh9KGHHvKhd0GtKP0OnjVrVqiUBYDIIwAFZFH6ohwxYoTPpKRitzrAUVHxJk2aeKHx22+/3QuSKwilOiNKO9YQkIDO+jRr1syH3xUrVixD2wIAWY0CRhr60a9fP59p6YMPPvCg06WXXuoHQApCPfroo6EglIbYTZw40caNG+fLNHRa26hUqZKvy4xLAJDyPrhv375ev0n9q4Yz9+7d20qWLOmBp6FDh8YJQun3sspSiLL/FbzSb2f9ZuYkAHBsUAMKyMI05E4ZTG+//bZ/CWusu84AXXDBBT6rx5lnnulj3pUltWbNGqtQoYIf5ASFy4Nx8wCAlIuJibHWrVv7AdCNN95o//33n0/uoP5Wfe1HH33kBch1m6gWSZ8+fUJn30WZrBoODQBIeR/cpk0bPxFw3XXX+aQOCjr9888/fvvXX3/t/6senzz//PO+bvDbV7+HNTsefTBw7HDkCWRBihvrC1NneIIYsrKbdGb9iy++8BokyoKaMGGC/f77755y3KtXL19PhciDgx+CTwCQOupLVdPpr7/+8hMBQT/83nvv+UGPlqkfvvnmm+3777+3Z5991s/AS3jwnwMfAEg5/f7V0Gf9zi1VqpRnMD333HP+23fatGlWtGhRz/a/++67feZn9c3XXnttnD5Yv4fpg4FjiwwoIAsIvij1cQ1PEVZhcQWhypUrZw0aNLCnn37aU491RkfFGLWuZgJ57LHH4pxxBwCkTJA5Gp+yTFV3RDMvadIHDcXr0qWLB6g0GYRmxNN9dXKAfhgA0rcPfuCBB3xEgCZ3UM3ToA/Wb+d69epZnTp1/PewCpHTBwMZj/QHIIvM8KFx6jqLs3XrVq8vMnr0aDv33HN9HZ3p0RkcTSUrixcv9iEgSjlWICrYDl+8AJD6kwB79uzxGiKa+EE1R5RtqmK3okxT1dXTgY+CUSpsq/XuuecePygSBaWo9QQAqe+DVXZC/ah+8zZq1ChU5+m+++6z0047LdQHz5492zNRVWOvXbt2vg59MJDxCEABWWCGD9UI0ZesvjT1v6aRnTt3rhdcLFGihAenNmzY4GnHP/74o38568BHZ+UDBJ8AIHWCab5VT2/z5s22adMmzy5VrT0VrxXdPn/+fJ9haePGjX4wpLp7OiAK+nMOfAAg9X1wixYtfNidTsZWrVrV+1/1w8pwUtDpl19+sT/++MN/Nyv4X7p0aTvrrLN8G/TBQObApxDIpJSxpC9Una3RmHbNYqdMp+nTp/sZ+EWLFnmASUVvO3bs6OPaR44c6Wfj8+XLZ999950HnbQdAEDqzroHBy6q31SxYkWfyluBfgWgxo4d6zPcSffu3T07VUM+dAZeB0Aa8qEDHvXjzLAEAKnrg0V9sIL6n3zyic/yrOCSZnnW7KNy/vnn+6yiJ510kl100UVeK1X9NX0wkLlQAwrIxDRFrIba/fzzz/6FqgBTsFxfqppZSQc7kyZN8mKL8+bNs4IFC3rRRX3hMssdAKSNAkkK6KvPVZ2RILNUQ51vueUWW7JkiQ0YMMBuvfVWz06dOnWqFS5c2Lp168ZsowCQDn3w559/7oF/ZTN17tzZl6vURNeuXW3dunV25513+ix4CkwpC0p97xVXXEEfDGRCfBqBTGzBggV+dl1ncVRkPKAgU6dOnfz6TTfdZBdccIEPxwtqQInO9vCFCwBp89JLL3kNEVFmU9C/Hn/88TZmzBif5S44A68glLJV49fwAwCkzosvvuh9qyizSVRwXNlOH330kffLQSZq//79vWRFgD4YyHwYggdkAuHD5HRgI0pOVEFbnXnXOPe33nrLZ/YIFChQwINQmvZ7xowZXm8kHOPcASB1wpPDe/Xq5dmnGr4xefJk2759u/evWqdu3bo+++gJJ5zgk0S8+eabcbZD7T0ASBsFmDTTXZ48eXz4nei6Mps0JE8nYPU7WUGq9957L04fTh8MZD4MwQMy2Jw5c2z8+PF28skn2+WXXx5ari9WHeTo8sMPP/htGu+uoorBGaBgON7MmTOtdevWfNECQCpo+LKGcahguGZNEv08CmqGqNaehkOPGDHCg/233XabD7MLLFy40F5++WV79NFH6YcBIIU0gcOff/7pQ5yDjKXw2ZvXrl3rwf5HHnnE7r33Xg9ISTC8Trc/+OCD9uyzz9IHA5kcASggg+ijFxMTY02aNLGVK1d6LSd9aWo2j3POOceqV68eZ30VH+/du3eCQahA+Jc1AODo/v33Xz97Lueee66deOKJXk9EASZN6BAEopT5pACThnqEB6HCA1VCPwwAyadaTlWqVPHr+v2rE7LKZtLJgPC+VespCDVq1CgbMmSIB5wkfo0n+mAgc2OMDpBB9KVarFgxTy1WTSedXVfxcA3jUC0nBZm++uorX1cHOK1atbJXXnnFtmzZ4sNB3n777SO2yRcuAKRMmTJlrEOHDtayZUvvkzXcWUEozSyqDFXV4BMdDN1xxx1+0Wyjmn1J04LHn1mJfhgAkk995tlnnx2qsadsUtXS0wlZzf4cUM0nTb4zaNAgGz58eCgLKn6NJ/pgIHMjAAVkkKDWk6aV1ZkfFRxXMUUFlpTp9Prrr3txcV1/9913PVuqbdu29s4779jvv/9uU6ZMyegmAECWp4OVYObQu+66y2cdVUBKQ59PP/10n83uueees3379vlsozoBoAOgoUOHhuqNAABSp3z58larVi0fRqdJH9T3du/e3Wd4VlbqJZdc4plPOhlbuXJlz0C9/fbbPQClAuUAshaG4AEZTGfXNY2svnhnz54dOpOjAyGlGRcqVMj27NljNWrU8JTk9u3b+wGTvoQ5ywMAaaf6TxoGrRmUVGMkqPuk2nsqPK6fSpqJ9LzzzvMZ8fbv32/ffvut9ezZkxmWACCVgiHM27Zt89IT/fr1s8cee8xv0+/ia665xvtgnSBQH60TAjpxq1nwdCK2T58+9MFAFkMGFJDBX7z58+f3sziLFi2ysWPH+vLXXnvNg0+6KDNK49zLli1r119/vackV6tWzYNP4bPnAQBSTv2oZlK6+uqr7f333/eC5KKZlXTgoyF36pM165KCUzpb//HHH3t2qg58VH8EAJByCj7pt7BOtirr6ZtvvrHFixf7bQowffnll16TT8XFS5Qo4b+XNWpg6tSp3mfTBwNZDxlQQCagMz9XXHGFB6NOPfVU/7LVLB8qcqshH6IvWE0/27lzZ872AEA604FPx44dfVidhtvpYEj98ODBg71vVtaTAk+//PKLPfTQQ/TDAJCOfvzxR693qj5Y/atmxFOxcQ25C/rbV1991U/Yaig0fTCQNRGAAjKJN954w6688kq/rswnFcAtUqSI/x9/lqX4M34AABIX3ofG70/D6UTAhAkTvI+9//77fdizZrpTzT4NAQlHPwwA6SPol/U7WLVQ1edqMh5loCo7ij4YiB4MwQOOcdFxSSjue9lll9n5559vJ5xwghdcDIJPEv9giS9cAEi+8D40oeBT0CcrA0r9qw6ClIGq4JPEP/AR+mEASB9Bv6zZ8BRYUu0nZT8VKFDAl9MHA9GDABRwDOjgRl+eKia+cuXKBA+AVF+kadOmtnr1alu2bJkvY1w7AKTNwoUL7e6777YePXp4RtOWLVuOWCfokzXsrkmTJjZ//nw/6x7/5AEAIO0SG4CjiR/atGljM2fO9N/F+u3MYB0guhCAAo4BHdyo0K1mTNLU3TqgSSgjStPKlixZ0qebFc7uAEDqaTrvtm3bejFb1Rd56qmn/P8VK1b47eH9cDCpw4ABAzwANW7cuETPvAMAki/+pDkJnYgN+mOdCFiyZIm9+OKLia4LIOviVxVwjGjWOs3g8f333/v0seFndfTlqi9eDffQzEqfffaZHzABAFJnxowZHmxS5pOKh//+++/2+uuv25o1a+zmm28+IrikPlo0EYRmGlUtqO3bt2fY/gNANFA2v/pXjQJ4+eWXbfjw4fbtt98ekV0a9McqPq7ZRt955x3/vQwguhCAAiIkPGU4+ALVl66u62/8szrBF+9ZZ53l6cc6cAIApC74pL70+uuvtxEjRlilSpWsWLFifma9W7duNnv2bFu1alWC/XbVqlU9W1UHTboPACB1FGRSNv+OHTs8uD969Gi/KAAVHuAPglH6W7ZsWR+Kp9lIGQkARB9mwQMilGqssz3xZ+jYuXOnF1Zcu3atffrpp1a0aNEkZ2cKtgMASB4Fl1q3bu0ziT7xxBOhflXBf9UUefTRR335zz//7MGmhChLqkKFCn5iIKHZlwAAyaNAUrt27Sxfvnz2+OOPW8WKFT24r+Xbtm3zgFPevHnj/GZWvVRlotIHA9GHTzOQzoKg0e7du+3iiy/2szj6ItXZHw2xu+mmm7wWyQcffHDU2ZkIPgFAyvpfTd29d+9eq1KlSqhf1YGOgk8KRo0fP95OOumkRINPoowpDnwAIO00/Hnjxo2ejdqgQQMPNr322mveD5988sl2+umn265duzz4FORFVK9enT4YiFJ8ooF0pC9KBY30RaqC4joYUs0npR336dPHZs2a5V+4/fv3t7ffftvPsgMA0of6X9V5OuOMM3wyhzFjxtj+/fv9zLtccMEFnon65ptvJmuGOw58ACDt9Lv4l19+8cLinTt39tEAJ554ol100UU++/MDDzyQ4P3og4How8BaIJ0EZ2l05v3MM8+0E044wR555BE/Cz9s2DAPRGl5v379bNOmTbZ582Zbv369n2nnDA8ApJ2C/hreoQLiXbp08eEeyny67rrr/KBHZ+I///xzK126tJ9pp98FgPST0O9ZZTPVrFnTBg8e7IXI69ataxMnTvQ+Wb+ZNRw6OBnAjHdA9KMGFJCONMzjmWeesQ8//NDP8tSpU8cPfmTLli0+7O7dd9/1wNPSpUutQ4cOPjtTsA4AIH2GQa9bt86DUBr6UbBgQYuJibGvv/7aD34AAOkrqOGk38LK+N+wYYNVrlzZWrRo4WUovvvuO6/9dPzxx1u5cuX8Pn/99ZeXq7jiiit85ACA6EcACkgn+ihdffXVfnZdX7CLFy/2s0Aa/qHx7oF///3XD4yUbqwglAJSTZs2PaIAOQAg7UEozXr3008/2aBBgzwrNfx2AED6ZT4p0KQZSBV8Ura/fgNr6LMmhTj33HN93SlTpvhkPPnz57ennnoqFLBixjsgeyAABaRB/KDRDz/8YPfee69Nnz7dA0y6Hnwxa73wdZWGrLNAXbt29SlpAQDJpym8CxQoEKrvFF8QZNKBkOqMKBg1cOBAu/HGGyluCwDp8Ps3vB9VIKlVq1aecTp06FDPfvrnn3/s7LPP9mLjOuFapkwZr9GnE7AaLl2/fn0fjqeRAJwYALIHfnkBqaQvyuDLVxed5dGXqqb41oweY8eOteeee87X1Zdz/FivDpy03sKFC49aCBcA8H+UYarMUQ1h1kFPQnQgo35aQz00LLp8+fL25JNP2rPPPkv9JwBIAwX2Rf2o+lmZP3++1ze94447/Pew6j4FbrvtNh8BoPW//fZbr4uq/vvTTz/14JOG7xF8ArIHfn0BqRB8UWpWD6UVd+zY0Xr27OlfvDrLo4wm1X967LHHPBAlwRn3gNKNVY9EB0LBlzcA4OhURFxDm3WgM2nSpCSDUOqvK1So4GfZNemDhuK9//77x3yfASAaKKtJE+zcd999/n8QONKwuhUrVljVqlV92fjx4z376cEHH7S2bdt69qmyoNR/N2vWzBo2bBj6bczwOyD7IAAFpFDwRalx7joDP2PGDNu2bZsPu2vevLkHoU499VSvNaKDnSeeeMLGjRvn9w3OuOuASLS+MqYoQg4AR6f+VcEmDePQGXRlNemgJrEglAL86q+Voap133vvPbvyyiu96C0AIOV00lS19YYPH24jR44MLS9atKjXddJso2+88YYXFn/ooYdsyJAhVrhwYZ8QQsGr+MhGBbIXPvFACumLUgc6F154oZ/lmTx5sgehdFCjMz8KKqnwov6OGjXKx8BrZg+dfQ/ogEhBKs2K16BBgwxtDwBkBX///bfVqlXLZxgNglCfffaZn4lPKAgV1ChR8EnraYZSZULphEAwPA8AkDI1atSwYcOG2VVXXWX33HOPjRgxwpcry0lZTddcc4317dvXA1SDBw/2vnjRokUenFLtUwDZGwEoIBUWLFjgBW31xaovYhXD1Zn5888/33bv3u1TzioIpSDTww8/bL1797ZOnTodsZ3EiucCAOJSYVvNrqR+V2fX9+7da2XLlk00CBUEnzQzqQ6UXn311Ti1+Kg3AgApc+DAAf+r+k4aztyvXz/PcNIwO3nttdc8yKT+um7durZ8+XL77rvv7KabbvJRAZdcckkGtwBARmPALZAKSiFetWqVlSxZ0v/XNLKa5vvNN9+0X3/91a677jo/E6SzP507d/ZAlDDDBwCkjoJNmthBBz06mBEN8QiCUJrqW0EoBZmCIXYqcHvLLbf4wdLMmTM9KBV/9lIAQPKoZITqn+7cudNq165td955p/en999/v2f333333R6E0olX1UiNiYnx9ZSxOmXKlFD2Kb+FgeyLABRwFAlN1a3hdSqgqOEcn3zyiY9x15n3U045xUqVKuVnflSMUfWflPkUHOzwhQsAyRMeKAquK9ikunmSUBBK/e2AAQP8AEd9t4JVOvBR8CmYaYlitwCQOupXu3fvbnPnzvWLhkVrMghRJpT6aWWpzp492yfaUbCqYsWK/vtYv6XpgwHkiI0/NzyAkOCLUkM6fv75Z5+5QwEmFbPVEDsd2KgQowJLmtlDQ0JU12nMmDE++4fOuusLlzPuAJD24H+wTEOg77rrLpswYYI9/fTTHoRSfREVuVXW6cKFC73f1Wyk6rsJPgFA2qkfVWapMp8KFCjgJ181BPqvv/7yuqcvvfSSZ0MFM+QdrU8HkP3wSwxIxmx37dq18y9XBaLat2/vX64qHq7/16xZ44EoWblypdcZ0RkhXRR04gsXAFJm1qxZNnToUOvQoYMH/M844wzvZ/Pmzeu3K/tUgScFmW644QZfdvnll3smlLJS1U+rf9Z21I8TfAKAlIs/XE79qIY7K+Cvvve8887zyXiCTKhgOJ7qoaoGajh+CwMQMqCAJL5wVWyxTZs2fr1///6ebqxMp3r16tljjz1mjRo18iloNQuIvnz1hasDIKUe60ua4BMApLz/7dq1qweSAhrWrJp7qqd38skne3FbDYPeunWrPfvss15/b+zYsV77qVChQrZt2zYrVqwYQz4AII327Nlj33zzjQeeAvp9rCF2qrtXuHBhD0IFmVCqA6Vaqco+JfsfQHwEoIBEaDid6jiphoi+TJs2bRoqOK7Lcccd5wc81apV84MfBZ2qVq3q63LGHQBST0PpFPT/+OOP7bbbbvOhHlo2ffp0+/PPPz24X7RoUR8SreC/itsq+K+sKU3/HcwwykkAAEg9/ZbVzHUzZsywxx9/3Ic7hwehFHjq06ePD3d+//33PQj177//et0nSlAASAgBKCAB+lhceOGFXtRWQaUffvjBKleuHLpdNZ5UYLxGjRo+DKR+/fpx7k/wCQBSJ+g/N2/e7Ac+Gtr8yCOP+HUFlNavX29Lly71AyIFo1RgXAdCq1evtrPPPtu++uorDngAIIXUr37++ed+8rVJkyZ2zjnneO3TX375xYNMyk7VMDvNcBfuyiuvtDfeeMPKlStn8+fP95MBwgkAAAkhAAUkQgc1GlqnFGIVFtcXcfg4eAWelAmloSEffvihTzMLAEg7DWdetmyZn03v2LGjD+tQxum5555rRYoUibOuhuEFE0VoFjz105x1B4Dk+/HHHz3IrxMA6lMVbFKgSSdbixcvbgsWLPDbRQXIw4NQylLVqAEFm5588klmfAaQJAJQQAJFFgPz5s3zwrb6QtYwO9UcCaf6T7/++qvXheILFwDSh2qNbNiwwYc2b9++3c4//3wPQj3zzDOhArjBz5f4gSYyUAEg+ZRNquzRa665xnr16uV19i677DLPJlVgKsjyD4JQ6nMVdFJWlPrlq666ynr27On3T+o3NQAIAShke8HBioosasjdkiVLrFKlSl5o/PTTT/d04u7du/u6SjGOH4QKzrTzhQsA6WPq1KnWuXNnn+zh6quv9jPyym7SkLvwIBQAIG3Bp7POOsuLiQ8bNszr7SmT6YsvvrBu3bp53T3VQA1+4yoIpeCUhulphlJln6oIuSbpIfAPIDkIQCFbC8an79ixw84880w/yFEg6r///vMvVI11v+uuu0JBKAWaFIQ65ZRT4myH4R4AkLaTAOH1QpT9pAkgtmzZYs8//7zX4FNNqIsuusjPuGsItLKidLAEAEhdqYlWrVrZwIEDbciQIT68OeiHX3rpJa+9p5MB4TVQRcOjdbtmuqtQoYKNHj3a+3BOxAJIDgJQyPb279/vBzIKII0YMcJTj1VLRFN7jx8/3r+AdSD022+/eWqyCt3+9NNPdvzxx2f0rgNA1NR8Uk2RU0891Yd2qD/WGfiuXbvayy+/bJdeeqmvp4DUxRdfbN9//719++231qZNm4zedQDIcpYvX+4nUxVcUpHxPHny+O/hvHnz2u+//+59a82aNe2BBx7wSR5UWFwjABSgSijTiaHPAJKLngLZis6clyhRwqfuDugMzt9//+2BpkaNGvmy5s2b+8wf+qIdNWqUnXHGGdaiRQt74YUXPDCl6WYBAOlDQX9N5vDRRx/5LEzKcOrQoYPdcsstdvvtt3sfrAMl9d2a6nvo0KHWsmXLjN5tAMiS8uXL57959fv3oYcesvvvv9+DT3PmzPEheTt37rRdu3b5CdqAZsbT718FrjQkWgEqUS4DwScAycXcmMg21q1bZw0aNLCvv/76iKCUzgSdcMIJ/gWqszhSq1Ytu+GGG3xYnupCBYEpFSNXirFSjQEAKafgfjj1zZrKu2HDhrZp0yYfFqLhzzrAUVaq+l2dndf9ypQp4zPi0Q8DQOoooP/aa6953/vKK694vb0g80klJ5T1/8cff/jJAQX9laGq38eadEcnCqpXrx7aFiUoAKQEQ/CQbehMjtKMddZcBzIa8qGpZVXfSWfXNcxOF9UUCa9JokwoTTmrCwAgfagf3rZtm5UrV87/nzhxot100032zjvveKFbDYHWiQPNgqcglDKkVJsPAJA+NApA/a6KiG/cuNEnfXjuuedCv4PDa5zqN7FmJtVQPNWJCq/bBwDJRa+BqKcvVClUqJAXGteXqgJOmi5WxcZ1xl0zfGh4nWqKqAh5kAmlL9qiRYv6jHgAgPSh/lWzjHbs2NEPdkQFxnXR1N46267i4/qrSSKmTJnifTQAIP1Uq1bNZxZVUEm/d1WmQoLfwUHwSdmmCjZpJID+Bv8DQEqRAYWotnfvXrv11lu9eKLGt4uG1KmorWqI9OjRww9yVNi2devWHpC66qqrvPDtokWL7KmnnvIvXxW8ZWYPAEi9+LOFfvrpp/boo4/aypUrrXbt2jZu3Dg/4Ln33ns96B/UJNGkD6+//rrX36POCACkv1WrVnkm1Lx586xv376h38xkOQFIbwSgEPUBqBtvvNHPnusLVePYNc5dZ951QKMCtzrDPmbMGB+ip3HvSkNW8cWqVat6HSjNxKTZQZheFgBSJxjOoX5UQ+8U9FcNEvXRn332mc+0pJMDl112mQ+703oKRJ144okJbgcAkP7D8W6++WYPQvXr189P1ApBKADpiQAUop4OZq644gr78ccfPbVYmU2q/aSA06uvvmoDBw4MDffQF6xqj6xZs8YPjurXr+/LOOgBgNQJgvcK7Gt4nSZ1WLp0qQ/5UOFxZZ2K6uyp/1XGk7JRlaH61ltvZfTuA0C2CkINGDDA66N26dLFT9oCQHoiAIWopbe2DnwUONKBzooVKyx//vyeYhwUFNcBkWYBCYJQo0eP9uBUOM78AEDaKODfokULrzGiKb51MkDFxpVxevvtt9sjjzzi6+mgRycLlLnauHFjv50ZlgDg2A7H0wkAla/Q5A/0wQDSEwEoRKX4w+WmTp3qsydpSMfixYvtuuuu8ym+gyCUhuPpIOiCCy7wQrfxg1AAgNQbNmyYT92tYdA1a9b0AxplQqnOnjJRdZb9hhtuCK2v21QXSv14/NpRAIDI2rBhg5UpU8ZPwNIHA0hPBKAQdYLhcjrjrqK1OpOjWT569uxp+/bts/79+/vwj2uvvdYGDx4cCkJpCJ5qkXz33XdkPAFAOlJ9PfWzkyZN8oMZ0QGNTghoiHSRIkW83p6Kjof3v9TeA4CMwygAAOmNABSiSnCWRgc6mipW1xWMiomJ8ZnvNNPdsmXLvMiiglCqPaKDnxkzZtiZZ55pVapU8fvwhQsA6UNBpIsvvtj+/fdfmz17tvet6mPV1+qimfCGDBnis+FVrFgxo3cXAAAAEcIRNqKKDmZ0sKMsp9KlS9vEiRN9No85c+Z48EkHPXXq1LFXXnnFTjjhBBs1apQdd9xxPgueio7r/gpiEXwCgJRT/xsIzm8pg0mz2f3xxx/23nvv+Sx4QR+rPlkFxxs0aGClSpXKsP0GAABA5HGUjSztl19+sTfffDPOMh3QaCYlFbytVauWL1PhW1m/fr3dc889tnHjRhs7dqwNHz7c7r//fi96yzh3AEi9YLichj+rn1WG6bhx4/w2TeetoP8dd9xhH3zwQaiv/fPPP23KlCkeoMqXL19GNwEAAAARxLzyyJJ08LJnzx6fwlu1RcKtWbPG/v7779DZdJ1tV12R4LqKjCs7SjPfqehtcCY+qB0FAEh5n6zgk4Y/N2nSxPbu3evLVWB85syZoRp7muhB/bayT1XgVn21Joh46aWXQtvhJAAAAEB0IgMKWZIOUAoWLGhff/211w5RMOrzzz/32zSkTsPtHn/8cdu6dasHnxR4Cm6rUKGCZ0BJ+FA7gk8AkLrMp6B2ngJN1atX97p6uijT9N133/VJH0qWLOk1oJQdVaNGDf//8ssv9yHS6n91EoDgEwAAQPTiiBtZUnCWvHz58n7Qo4OY6dOn+1TevXv3tptuusmXnXfeeT7rUpANtWjRIj9Y0jTgAIC0U+aTTgIo6P/NN9/4CYCqVat6H61sJwX61ScHASoNx4tP/TInAQAAAKIbv/aQpezYscOzmVavXu3FxHXAogynu+66y2e3e+SRR7yOyKWXXuoHOarvpOK2OvhRodupU6d65pQOigAAqRN/plANr3v22Wdt27Ztdskll4QmdMifP7+fFJABAwZYnjx57KGHHvJM1PhBLAAAAES3HLHBNDVAFig4rqLhc+fO9aF1mrVOZ9pvv/12q127tv3+++8eeNKZdB3gdO3a1c/G66BI2VEafqdCt5oBT4GroGAuACB5lEWqIuLff/+9lShRwtq1a2fXXHON3zZ+/HgvMq5+9a233rKWLVuG7rdv3z6fMELrqh+/++67M7AVAAAAyAgEoJAl/PDDD9axY0dr06aNNW7c2KpUqeLTeU+bNs0zmj788EMPRs2fP98uu+wyDy498MADHpCSVatW+XA9nX3XmXkKjgNAymi20G7dunmNp0KFCnm/qmHOt912m1WqVMnXeeONNzzApGHPo0ePttNPPz1OEOqrr77y+9D/AgAAZD8EoJDpqZDtWWedZddff70NHjzYypUrF7rt9ddft5EjR9q6dets8uTJfrDz22+/WY8ePfx2nY2PP9yOWZYAIGXmzZtnbdu2tSuuuMJuvfVWzyjVZA4K5lesWDHOusoyffjhh73IuOryhQehApwEAAAAyH4IQCFT0/TdrVq1soEDB/rMSUWLFvXlqgOl2k/y8ccf26BBg7wIrs7QV6tWzf744w8PQikwpWEfHTp0yOCWAEDWpP62X79+nsGkWe1Kly4dqgGlunzqZzW7nYbeKQNVXnvtNQ9CKRNq2LBhHrwCAABA9vZ/FUSBTGb58uV2/vnnW7169XxIh4JPOugRBZ+C2OmFF17oAapNmzZ53RGdWVfhcQWezjnnHGvfvn0GtwQAsi71qb/++qsPf1bwSRR8Wrp0qQ9zPuWUU6xXr17Ws2dPO+2002zz5s2eeXrnnXfawoUL7d13383oJgAAACATIACFTEuz2TVq1Mh27tzpRcWDg54gCBXMsiQanqeDI9UXCYZ16L4qiquz8qoJBQBIuWC2u5UrV4YKkT/99NPWvHlz++KLL7z+3oQJE2zcuHG2YsUKnxhC+vTpY++8844999xzGbr/AAAAyBwowIBMS7PcaRjHTTfd5DVF5P777w8FofRXQahgOJ4ynV599VWvS6Kz9OFThDPbHQCkjoL6GkKnfvinn37ybFNdlO10+eWXW//+/UPr/vnnnzZnzhwfmlekSJHQ8GdmHQUAAAABKGRqVatWtTFjxtjNN9+caBAqqAWls/MNGza0smXLZvBeA0B0BaA0nC5//vz2yy+/WIkSJey+++6ziy66KFSAPOiPN2zY4Lcr+BSO4BMAAAAIQCHTU1HxxIJQwYx2mvlOZ94vuOCCjN5dAIjKjFQNhVbAf+vWrT7DXTj1x6r3tGzZMq/LBwAAAMTHLHjIMv755x8PQmk6cNUWeeCBB3y5ao5oanCdYZ86dSpn2gEgAoKAf3D977//turVq/t1FSlX/3zgwAEfphfU4gMAAAACBKCQZYNQmhb86quvtu7du9uWLVts/vz5lidPHmqNAECE9ejRw2bOnGktW7b0elCrVq3ymUqnT59OPwwAAIAEEYBClgxCDRgwwINQ27Zt86EhQfBJ04Vz5h0AIuvbb7+1YcOGec2nGjVqeEHyu+66y4NO9MMAAABICAEoZEk6296zZ08/yJkyZYr/5aAHAI6dffv2eb9bqFCh0DIynwAAAJAYAlDIsjZu3GilS5f24rcEnwAAAAAAyLwIQCHLC6b/BgAAAAAAmRMBKAAAAAAAAEQUaSMAAAAAAACIKAJQAAAAAAAAiCgCUAAAAAAAAIgoAlAAAAAAAACIKAJQAAAAAAAAiCgCUAAAAAAAAIgoAlAAAAAAAACIKAJQAAAAAAAAiCgCUAAAAAAAALBI+n+CqCWZ+ABJ5QAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📋 详细分析结果:\n", "==================================================\n", "\n", "📄 文本统计:\n", "  • 原始文本长度: 39084 字符\n", "  • 过滤后词数: 8715 个\n", "  • 提取表格数: 101 个\n", "\n", "🔍 关键词 (前10个):\n", "   1. 证券 (权重: 0.3959)\n", "   2. 服务 (权重: 0.3534)\n", "   3. 财富 (权重: 0.2977)\n", "   4. 金融 (权重: 0.2840)\n", "   5. 图表 (权重: 0.2409)\n", "   6. 业务 (权重: 0.2405)\n", "   7. 用户 (权重: 0.2218)\n", "   8. 产品 (权重: 0.2052)\n", "   9. 国金 (权重: 0.1938)\n", "  10. 指南针 (权重: 0.1440)\n", "\n", "📊 情感分析结果:\n", "  • 词典法得分: 0.0033\n", "  • SnowNLP得分: 1.0000\n", "  • 规则法得分: 0.0023\n", "  • 平均得分: 0.3352\n", "  • 情感倾向: 正面\n", "\n", "💡 匹配的情感词 (前10个):\n", "  • 发展 (正面, +1.0)\n", "  • 优势 (正面, +1.0)\n", "  • 优势 (正面, +1.0)\n", "  • 优势 (正面, +1.0)\n", "  • 稳定 (正面, +1.0)\n", "  • 优势 (正面, +1.0)\n", "  • 提升 (正面, +1.0)\n", "  • 突破 (正面, +1.0)\n", "  • 稳定 (正面, +1.0)\n", "  • 领先 (正面, +1.0)\n", "\n", "✅ 所有分析和可视化完成！\n"]}], "source": ["# 设置文件路径\n", "PDF_PATH = 'data/2025-04-01_国金证券_非银行金融行业深度研究：金融IT商业模式、产品和客户全方位对比.pdf'\n", "STOPWORDS_PATH = 'data/stopwords.txt'\n", "POSITIVE_DICT_PATH = 'data/正面词典.csv'\n", "NEGATIVE_DICT_PATH = 'data/负面词典.csv'\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists(PDF_PATH):\n", "    print(f\"❌ PDF文件不存在: {PDF_PATH}\")\n", "    print(\"请确保PDF文件在正确的位置\")\n", "else:\n", "    print(f\"✅ 找到PDF文件: {PDF_PATH}\")\n", "    \n", "    # 执行完整分析\n", "    analysis_results = analyze_research_report(\n", "        pdf_path=PDF_PATH,\n", "        stopwords_path=STOPWORDS_PATH,\n", "        positive_dict_path=POSITIVE_DICT_PATH,\n", "        negative_dict_path=NEGATIVE_DICT_PATH\n", "    )\n", "    \n", "    if analysis_results:\n", "        print(\"\\n🎨 开始生成可视化图表...\")\n", "        \n", "        # 1. 情感分析方法对比图\n", "        create_sentiment_comparison_chart(\n", "            analysis_results['dict_analysis']['overall_score'],\n", "            analysis_results['snownlp_analysis']['overall_score'],\n", "            analysis_results['rules_analysis']['overall_score']\n", "        )\n", "        \n", "        # 2. 关键词情感分布图（词典法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['dict_analysis']['keyword_scores'],\n", "            \"Dictionary Method\"\n", "        )\n", "        \n", "        # 3. 关键词情感分布图（SnowNLP法）\n", "        create_keyword_sentiment_chart(\n", "            analysis_results['snownlp_analysis']['keyword_scores'],\n", "            \"SnowNLP Method\"\n", "        )\n", "        \n", "        # 4. 关键词云图\n", "        create_wordcloud(analysis_results['keywords'])\n", "        \n", "        # 5. 显示详细结果\n", "        print(\"\\n📋 详细分析结果:\")\n", "        print(\"=\" * 50)\n", "        \n", "        print(f\"\\n📄 文本统计:\")\n", "        print(f\"  • 原始文本长度: {len(analysis_results['original_text'])} 字符\")\n", "        print(f\"  • 过滤后词数: {len(analysis_results['filtered_words'])} 个\")\n", "        print(f\"  • 提取表格数: {len(analysis_results['tables'])} 个\")\n", "        \n", "        print(f\"\\n🔍 关键词 (前10个):\")\n", "        for i, (word, weight) in enumerate(analysis_results['keywords'][:10], 1):\n", "            print(f\"  {i:2d}. {word} (权重: {weight:.4f})\")\n", "        \n", "        print(f\"\\n📊 情感分析结果:\")\n", "        print(f\"  • 词典法得分: {analysis_results['dict_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • SnowNLP得分: {analysis_results['snownlp_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 规则法得分: {analysis_results['rules_analysis']['overall_score']:.4f}\")\n", "        print(f\"  • 平均得分: {analysis_results['average_score']:.4f}\")\n", "        print(f\"  • 情感倾向: {analysis_results['sentiment_label']}\")\n", "        \n", "        print(f\"\\n💡 匹配的情感词 (前10个):\")\n", "        matched_words = analysis_results['dict_analysis']['matched_words'][:10]\n", "        for word, score in matched_words:\n", "            sentiment = \"正面\" if score > 0 else \"负面\"\n", "            print(f\"  • {word} ({sentiment}, {score:+.1f})\")\n", "        \n", "        print(\"\\n✅ 所有分析和可视化完成！\")\n", "    else:\n", "        print(\"❌ 分析失败，请检查输入文件\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}