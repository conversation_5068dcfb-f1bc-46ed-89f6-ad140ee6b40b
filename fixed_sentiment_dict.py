#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复后的情感词典加载函数
"""

import os
import pandas as pd
from typing import Dict

def load_sentiment_dict_fixed(positive_path: str, negative_path: str) -> Dict[str, float]:
    """
    加载情感词典（支持多种编码）
    
    参数:
        positive_path: 正面词典文件路径
        negative_path: 负面词典文件路径
    
    返回:
        情感词典，正面词为正值，负面词为负值
    """
    sentiment_dict = {}
    
    # 默认情感词
    default_positive = ['好', '优秀', '增长', '上涨', '盈利', '收益', '利润', '成功', '发展', '提升', '改善', '积极', '正面', '乐观', '强劲', '稳定', '优势', '机遇', '突破', '创新', '领先', '卓越', '高效', '可靠', '安全', '便利', '满意', '信心', '希望', '繁荣']
    default_negative = ['差', '下跌', '亏损', '损失', '风险', '问题', '困难', '挑战', '下降', '减少', '恶化', '消极', '负面', '悲观', '疲软', '不稳定', '劣势', '威胁', '危机', '衰退', '滞后', '落后', '低效', '不安全', '不便', '担忧', '焦虑', '恐慌', '萧条', '困境']
    
    # 添加默认词
    for word in default_positive:
        sentiment_dict[word] = 1.0
    for word in default_negative:
        sentiment_dict[word] = -1.0
    
    # 加载正面词典
    if os.path.exists(positive_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if positive_path.endswith('.csv'):
                    df = pd.read_csv(positive_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = 1.0
                else:
                    with open(positive_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = 1.0
                print(f"✅ 使用 {encoding} 编码加载正面词典: {positive_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载正面词典: {positive_path}")
    
    # 加载负面词典
    if os.path.exists(negative_path):
        loaded = False
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                if negative_path.endswith('.csv'):
                    df = pd.read_csv(negative_path, encoding=encoding)
                    if not df.empty:
                        words = df.iloc[:, 0].tolist()
                        for word in words:
                            if isinstance(word, str) and word.strip():
                                sentiment_dict[word.strip()] = -1.0
                else:
                    with open(negative_path, 'r', encoding=encoding) as f:
                        for line in f:
                            word = line.strip()
                            if word:
                                sentiment_dict[word] = -1.0
                print(f"✅ 使用 {encoding} 编码加载负面词典: {negative_path}")
                loaded = True
                break
            except (UnicodeDecodeError, pd.errors.EmptyDataError):
                continue
        
        if not loaded:
            print(f"⚠️ 无法加载负面词典: {negative_path}")
    
    print(f"📚 情感词典加载完成，共 {len(sentiment_dict)} 个词")
    return sentiment_dict

# 测试函数
if __name__ == "__main__":
    print("🔧 测试修复后的情感词典加载函数...")
    
    # 测试加载
    sentiment_dict = load_sentiment_dict_fixed(
        'data/正面词典.csv',
        'data/负面词典.csv'
    )
    
    print(f"\n📊 加载结果:")
    print(f"  总词数: {len(sentiment_dict)}")
    
    # 统计正负面词数
    positive_count = sum(1 for v in sentiment_dict.values() if v > 0)
    negative_count = sum(1 for v in sentiment_dict.values() if v < 0)
    
    print(f"  正面词: {positive_count} 个")
    print(f"  负面词: {negative_count} 个")
    
    # 显示一些示例词
    print(f"\n📝 示例正面词:")
    positive_words = [k for k, v in sentiment_dict.items() if v > 0][:10]
    for word in positive_words:
        print(f"  • {word}")
    
    print(f"\n📝 示例负面词:")
    negative_words = [k for k, v in sentiment_dict.items() if v < 0][:10]
    for word in negative_words:
        print(f"  • {word}")
    
    print("\n✅ 测试完成！")
